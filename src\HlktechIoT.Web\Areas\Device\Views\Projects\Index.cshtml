﻿@using System.Security.Cryptography
@using System.Text
@{
    Html.AppendTitleParts(T("项目管理").Text);

    var calculateMenuId = new Func<string, string, string>((Name, Url) =>
    {
        using (var md5 = MD5.Create())
        {
            var hashTarget = string.IsNullOrEmpty(Url) ? Name : Url.ToUpper();
            var md5ComputeHash = md5.ComputeHash(Encoding.UTF8.GetBytes(hashTarget));
            var bitConverterResult = BitConverter.ToString(md5ComputeHash);
            var replaceResult = bitConverterResult.Replace("-", "");
            return replaceResult;
        }
    });

    var url = Url.Action("Index", "Products", new { area = HlktechIoT.Areas.Device.DeviceArea.AreaName });
    var urls = Url.Action("Index", "APP", new { area = HlktechIoT.Areas.Device.DeviceArea.AreaName });
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    .layui-layer-title {
        background-color: #fff;
    }

    .dtree-nav-item {
        padding-left: 0;
    }

    .layui-table-tool {
        z-index: 1;
    }

    .guanli {
        font-weight: bold;
        color: rgb(93, 193, 133);
        /* color:#da9d03; */
    }

    /* 操作列自适应样式 */
    .layui-table-fixed-r .layui-table-body {
        overflow-x: hidden !important;
    }

    .operation-column {
        white-space: nowrap;
        padding: 0 5px; /* 添加左右边距 */
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center; /* 垂直居中 */
        flex-wrap: nowrap;
        height: 100%; /* 确保容器占满单元格高度 */
        width: 100%; /* 确保容器占满单元格宽度 */
        box-sizing: border-box; /* 确保内边距不增加总宽度 */
    }

        .operation-column .pear-btn {
            margin-right: 6px; /* 保持原有按钮间距 */
            padding: 0 8px; /* 保持按钮内边距 */
            height: 30px; /* 保持按钮高度 */
            line-height: 30px; /* 行高与高度一致 */
            min-width: 44px; /* 按钮最小宽度 */
            text-align: center;
            flex-grow: 1; /* 按钮自动扩展占用空间 */
            flex-basis: 0; /* 所有按钮基础宽度相同 */
        }

            .operation-column .pear-btn:last-child {
                margin-right: 0;
            }

    /* 确保表格单元格内容居中 */
    .layui-table-cell {
        text-align: center;
        padding: 0 2px; /* 保持单元格内边距 */
    }

    /* 固定列样式优化 */
    .layui-table-fixed {
        height: auto !important;
    }

    /* 确保表格右侧固定列正确显示 */
    .layui-table-fixed-r {
        right: 0 !important;
    }

        /* 优化表格右侧固定列的单元格 */
        .layui-table-fixed-r .layui-table-cell {
            padding: 0;
        }
</style>
<form class="layui-form dg-form">
    <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width: auto">@T("关键词")：</label>
            <div class="layui-input-inline" style="width:330px">
                <input type="text" name="key" id="key" placeholder="@T("项目名称、ProjectKey、AppKey、PushAppKey等")" autocomplete="off" class="layui-input" lay-filter="key">
            </div>
        </div>
    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    $("body").on("click", function (event) {
        $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
        $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon', 'dtree'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;
        var dtree = layui.dtree;

                 // 按钮配置集中定义
        var operationButtons = [
            @if (this.Has((PermissionFlags)4))
            {
                @:{ text: '@T("编辑")', event: 'edit', class: 'pear-btn-primary' },
            }
            @if (this.Has((PermissionFlags)8))
            {
                @:{ text: '@T("删除")', event: 'del', class: 'pear-btn-danger' },
            }
            @if (this.Has((PermissionFlags)32))
            {
                @:{ text: '@T("用户")', event: 'user', class: 'pear-btn-warming' },
            }
        ];

        // 将按钮配置保存到全局变量，供模板使用
        window.operationButtons = operationButtons;

        // 动态计算操作列宽度的函数
        function calculateOperationColumnWidth() {
            try {
                var buttonMargin = 6; // 按钮间距
                var extraPadding = 24; // 额外边距
                var baseButtonPadding = 20; // 按钮内边距

                if (operationButtons.length === 0) {
                    return 100;
                }

                var totalWidth = 0;

                operationButtons.forEach(function(button) {
                    // 调整文字宽度估算：中文字符约16px，英文字符约9px
                    var text = button.text;
                    var textWidth = 0;
                    for (var i = 0; i < text.length; i++) {
                        var char = text.charAt(i);
                        if (/[\u4e00-\u9fa5]/.test(char)) {
                            textWidth += 16; // 中文字符
                        } else {
                            textWidth += 9;  // 英文字符
                        }
                    }

                    var buttonWidth = textWidth + baseButtonPadding;
                    totalWidth += buttonWidth;
                });

                // 加上按钮间距和额外边距
                totalWidth += (operationButtons.length - 1) * buttonMargin + extraPadding;

                // 设置最小宽度
                if (totalWidth < 120) {
                    totalWidth = 120;
                }

                console.log('计算的操作列宽度:', totalWidth, '按钮数量:', operationButtons.length, '按钮配置:', operationButtons);
                return totalWidth;
            } catch (error) {
                console.error('计算操作列宽度时出错:', error);
                return 200;
            }
        }

        // 应用操作列宽度的函数
        function applyOperationColumnWidth() {
            try {
                var calculatedWidth = calculateOperationColumnWidth();

                var fixedRightHeader = $('.layui-table-fixed-r .layui-table-header table thead tr th:last-child');
                var fixedRightBody = $('.layui-table-fixed-r .layui-table-body table tbody tr td:last-child');

                if (fixedRightHeader.length > 0) {
                    fixedRightHeader.css('width', calculatedWidth + 'px');
                    fixedRightHeader.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                if (fixedRightBody.length > 0) {
                    fixedRightBody.css('width', calculatedWidth + 'px');
                    fixedRightBody.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                var mainTableHeader = $('#tablist').next('.layui-table-view').find('.layui-table-header table thead tr th:last-child');
                var mainTableBody = $('#tablist').next('.layui-table-view').find('.layui-table-body table tbody tr td:last-child');

                if (mainTableHeader.length > 0) {
                    mainTableHeader.css('width', calculatedWidth + 'px');
                    mainTableHeader.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                if (mainTableBody.length > 0) {
                    mainTableBody.css('width', calculatedWidth + 'px');
                    mainTableBody.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                console.log('已应用操作列宽度:', calculatedWidth);
            } catch (error) {
                console.error('应用操作列宽度时出错:', error);
            }
        }

        // 监听窗口大小变化
        $(window).on('resize', function() {
            try {
                setTimeout(function() {
                    applyOperationColumnWidth();
                    console.log('窗口大小变化，已重新调整操作列宽度');
                }, 100);
            } catch (error) {
                console.error('窗口resize时出错:', error);
            }
        });

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetList")'
            , page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 80
            , cols: [[
                { field: 'Name', title: '@T("项目名称")', minWidth: 160 },
                { field: 'ProjectKey', title: '@T("ProjectKey")', width: 180 },
                { field: 'AppKey', title: '@T("AppKey")', minWidth: 180 },
                { field: 'PushAppKey', title: '@T("PushAppKey")', minWidth: 180 }
                , {
                    title: '@T("产品")', minWidth: 100, align: 'center', templet: (d) => {

                        return `<div data-row_id=${d.Id} data-field="ProductCount" style="cursor: pointer;">` +
                            d.ProductCount + '<span style="color:rgb(233, 170, 94);"> (管理<i class="layui-icon layui-icon-util"></i>) </span>' + '</div>';
                    }
                }
                , {
                    title: '@T("App")', minWidth: 150, align: 'center', templet: (d) => {

                        return `<div data-row_id=${d.Id} data-field="AppCount" style="cursor: pointer;">` +
                            d.AppCount + '<span style="color:#da9d03;"> (管理<i class="layui-icon layui-icon-util"></i>) </span>' + '</div>';
                    }
                }
                , {
                    title: '@T("成员数")', minWidth: 150, align: 'center', templet: (User) => {
                        return `<div data-row_id=${User.Id} data-field="UserCount" style="cursor: pointer;">` +
                            User.UserCount + '<span class="guanli"> (管理<i class="layui-icon layui-icon-util"></i>) </span>' + '</div>';
                    }
                }
                , { field: 'CreateUser', title: '@T("创建人")', width: 160 }
                , { field: 'CreateTime', title: '@T("创建时间")', minWidth: 170 }
                , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: calculateOperationColumnWidth() }
            ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-100'
            , id: 'tables'
            , done: (res, curr, count) => {
                try {
                    // 延迟执行，确保DOM已经渲染完成
                    setTimeout(function() {
                        applyOperationColumnWidth();
                    }, 200);
                } catch (error) {
                    console.error('表格done回调中出错:', error);
                }

                $("div[data-field='ProductCount']").each(function () {
                    // 在这里执行您想要的操作
                    $(this).css("cursor", "pointer") //设置鼠标移入变形
                    $(this).click(function () {
                        // console.log('点击了项目一下');
                        let ProductCount_CurrentRow_Id = $(this).attr('data-row_id')
                        window.product(ProductCount_CurrentRow_Id) //把当前行的Id传给他
                    })
                });


                $("div[data-field='AppCount']").each(function () {
                    // 在这里执行您想要的操作
                    $(this).css("cursor", "pointer") //设置鼠标移入变形
                    // $(this).mouseenter(function(){ //设置tips
                    //     layer.tips('APP管理', this, {tips: [1, 'rgba(80,80,80,0.8)']});
                    // });
                    $(this).click(function () {
                        // console.log('点击了APP一下');
                        let AppCount_CurrentRow_Id = $(this).attr('data-row_id')
                        window.app(AppCount_CurrentRow_Id) //把当前行的Id传给他
                    })
                });


                $("div[data-field='UserCount']").each(function () {
                    // 在这里执行您想要的操作
                    $(this).css("cursor", "pointer") //设置鼠标移入变形

                    $(this).click(function () {
                        let UserCount_CurrentRow_Id = $(this).attr('data-row_id')
                        window.UserCount(UserCount_CurrentRow_Id)
                    })
                });
            }
        })

        $("#key").on("input", function (e) {
            active.reload()
        });

        window.active = {
            reload: function () {
                table.reload('tables',
                    {
                        where: {
                            key: $("#key").val(),
                            PType: $("#PType").val(),
                        },
                        page: {
                            curr: 1
                        }
                    });
            }
        }

        $("td").on("input", function (e) {
            active.reload();
        });

        table.on('toolbar(tool)', function (obj) {
            // console.log('点了一下：',obj);
            if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });


        table.on('tool(tool)', function (obj) {
            // console.log('点了一下：',obj);
            var data = obj.data;
            if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("Delete")', { Id: data.Id }, function (data) {
                        if (data.success) {
                            abp.notify.success(data.msg);
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            } else if (obj.event === 'edit') {
                window.edit(data);
            } else if (obj.event === 'user') {
                window.user(data.Id);
            } else if (obj.event === 'product') {
                window.product(data);
            } else if (obj.event === 'UserCount') {
                window.UserCount(data.Id);
            } 
        });

        window.saveCallback = function (data) {
            parent.layer.close(data.index);
            abp.notify.success(data.msg);
            table.reload("tables");
        }

        window.add = function () {
            parent.layer.open({
                type: 2,
                title: '@T("添加项目")',
                content: "@Url.Action("Add")",
                area: ['450px', '219px'],
                shade: 0.1,
                btn: ['@T("确定")', '@T("取消")'],
                yes: function (index, layero) {
                    parent.window['layui-layer-iframe' + index].submitForm();
                }
            });
        }

        window.edit = function (data) {
            top.layui.dg.popupRight({
                id: 'ProductEdit'
                , title: ' @T("编辑项目")'
                , closeBtn: 1
                , area: ['580px']
                , success: function () {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Edit")' + abp.utils.formatString("?id={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }

        window.user = function (Id) {
            top.layui.dg.popupRight({
                id: 'Users'
                , title: ' @T("用户")'
                , closeBtn: 1
                , area: ['1080px']
                , success: function () {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Users")/' + Id + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }

        window.app = function (Id) {
            parent.layui.admin.closeTab('@calculateMenuId("APP管理(项目)", urls!)');
            parent.layui.admin.addTab('@calculateMenuId("APP管理(项目)", urls!)', '@T("APP管理(项目)")', '@urls' + abp.utils.formatString("?PId={0}", Id));
            // top.layui.dg.popupRight({
            //     id: 'APP'
            //     , title: ' @T("APP管理")'
            //     , closeBtn: 1
            //     , area: ['780px']
            //     , success: function () {
            //         $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("APP")/' + Id + '" frameborder="0" class="layadmin-iframe"></iframe>');
            //     }
            // });
        }
        window.product = function (Id) {
            console.log(Id);
            parent.layui.admin.closeTab('@calculateMenuId("产品管理(项目)", url!)');
            parent.layui.admin.addTab('@calculateMenuId("产品管理(项目)", url!)', '@T("产品管理(项目)")', '@url' + abp.utils.formatString("?PId={0}", Id));
        }

        window.UserCount = function (Id) {
            top.layui.dg.popupRight({
                id: 'UserCount'
                , title: ' @T("成员")'
                , closeBtn: 1
                , area: ['1080px']
                , success: function () {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("UserCount")/' + Id + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }

        //监听开关事件
        form.on('switch(statusedit)', function (data) {
            //开关是否开启，true或者false
            var checked = data.elem.checked;
            //获取所属属性值
            var id = data.elem.attributes['data-id'].nodeValue;
            $.post('@Url.Action("ModifyState")', { id: id, status: checked }, function (res) {
                if (res.success) {
                    abp.notify.success(res.msg);
                }
                else {
                    abp.notify.error(res.msg);
                }
            });
        });

        window.warning = function (msg) {
            os.warning(msg);
        }
    });
</script>

<script type="text/html" id="toolOnclickRow">
    <a>
        {{# if(d.UserCount){ }}
            {{ d.UserCount }}
        {{# } else { }}
            -
        {{# } }}
    </a>

</script>


<script type="text/html" id="tool">
    <div class="operation-column">
    {{#  layui.each(window.operationButtons, function(index, button){ }}
        <a class="pear-btn {{button.class}} pear-btn-xs" lay-event="{{button.event}}">{{button.text}}</a>
    {{#  }); }}
    </div>
</script>
<script type="text/html" id="user-toolbar">
    @if (this.Has((PermissionFlags)2))
    {
           <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
               <i class="layui-icon layui-icon-add-1"></i>
            @T("新增")
           </button>
    }
</script>