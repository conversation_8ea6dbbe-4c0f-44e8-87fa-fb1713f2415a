﻿using DG.Web.Framework;

using DH.AspNetCore.MVC.Filters;

using HlktechIoT.Common;
using HlktechIoT.Data;
using HlktechIoT.Dto;
using HlktechIoT.Entity;
using HlktechIoT.Sign;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Serialization;

using Pek;
using Pek.Compress;
using Pek.Helpers;
using Pek.Ids;
using Pek.Models;
using Pek.MVC;
using Pek.Swagger;
using Pek.Timing;
using Pek.Webs.Clients;

using System.Security.Claims;

using XCode;

namespace HlktechIoT.Controllers.Api;

/// <summary>
/// 通用接口
/// </summary>
[Produces("application/json")]
[CustomRoute(ApiVersions.V1)]
[Authorize("jwt")]
public class Http2Controller : ApiControllerBaseX {
    #region 设备
    /// <summary>
    /// 上报设备数据
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="Data"></param>
    /// <param name="Id"></param>
    /// <param name="Lng"></param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("SetDeviceData")]
    [DeviceApiSign]
    public IActionResult SetDeviceData<T>([FromBody] DeviceDataDto<T> Data, [FromHeader] String Id, [FromHeader] String Lng) where T : class
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(SetDeviceData));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        if (Data == null || Data.Data == null || !Data.Data.Any())
        {
            result.Message = GetResource("数据为空", Lng);
            return result;
        }

        if (Data.DeviceName.IsNullOrEmpty())
        {
            result.Message = GetResource("设备DN不能为空", Lng);
            return result;
        }

        if (Data.DType <= 0)
        {
            result.Message = GetResource("数据类型错误", Lng);
            return result;
        }

        if (Data.DType == 1)
        {
            var list = new List<DeviceDataPush>();
            foreach (var item in Data.Data)
            {
                DeviceDataPush model;

                switch (item)
                {
                    case DeviceDataUDto dto:
                        model = new DeviceDataPush
                        {
                            DeviceName = Data.DeviceName,
                            ReportTime = UnixTime.ToDateTime(dto.T!.ToLong()),
                            Content = dto.ToJson()
                        };
                        break;

                    case DeviceDataHXDto dto:
                        model = new DeviceDataPush
                        {
                            DeviceName = Data.DeviceName,
                            ReportTime = UnixTime.ToDateTime(dto.T!.ToLong()),
                            Content = dto.ToJson()
                        };
                        break;

                    default:
                        continue;
                }

                list.Add(model);
            }

            if (list.Count > 0)
            {
                list.Insert();
            }
        }

        result.Code = StateCode.Ok;
        return result;
    }

    #endregion

    /// <summary>
    /// 获取设备数据
    /// </summary>
    /// <param name="StartTime">起始时间</param>
    /// <param name="EndTime">结束时间</param>
    /// <param name="DeviceName">设备DeviceName</param>
    /// <param name="Page">当前页</param>
    /// <param name="Limit">每页的条数</param>
    /// <returns></returns>
    [HttpPost("GetDeviceData")]
    [AppApiSign]
    public IActionResult GetDeviceData([FromForm] DateTime StartTime, [FromForm] DateTime EndTime, [FromForm] String DeviceName, [FromForm] Int32 Page = 1, [FromForm] Int32 Limit = 10)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(GetDeviceData));

        var result = new DGResult
        {
            Id = Pek.Webs.HttpContext.Current.Items["Id"].SafeString()
        };

        var Lng = Pek.Webs.HttpContext.Current.Items["Lng"].SafeString();
        var modelProjectApp = Pek.Webs.HttpContext.Current.Items["ProjectApp"]?.AsTo<ProjectApp>();

        var UId = DHWeb.Identity.GetValue(ClaimTypes.Sid);
        var modelUserEx = AppUser.FindByIdentityId(UId);
        if (modelUserEx == null)
        {
            result.Message = GetResource("用户非法", Lng);
            return result;
        }

        var AId = DHWeb.Identity.GetValue(ClaimTypes.GroupSid).ToDGInt();
        if (AId <= 0)
        {
            result.Message = GetResource("非法操作", Lng);
            return result;
        }

        if (modelProjectApp?.Id != AId)
        {
            result.Message = GetResource("APP版本参数错误", Lng);
            return result;
        }

        if (DeviceName.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("设备DN不能为空", Lng);
            return result;
        }

        if (StartTime <= DateTime.MinValue)
        {
            result.Message = GetResource("起始时间不能为空", Lng);
            return result;
        }

        if (EndTime <= DateTime.MinValue)
        {
            EndTime = DateTime.Now.AddDays(1).Date;
        }

        var pages = new PageParameter()
        {
            PageIndex = Page,
            PageSize = Limit,
            RetrieveTotalCount = true
        };

        var list = DeviceDataPush.Search(DeviceName, StartTime, EndTime, "", pages);

        result.Data = new { Data = list.Select(e => JsonHelper.DecodeJson(e.Content)), pages.TotalCount, pages.PageCount };

        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 生成设备DN和密钥（适用于阿里云切换为私有云）
    /// </summary>
    /// <param name="Id"></param>
    /// <param name="Lng"></param>
    /// <param name="DeviceName">设备DN</param>
    /// <param name="DeviceSecret">设备密钥</param>
    /// <param name="ProductKey">产品Key</param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("SaveDeviceInfo")]
    //[DeviceApiSign]
    [ApiSignature]
    public IActionResult SaveDeviceInfo([FromHeader] String Id, [FromHeader] String Lng, [FromForm] String ProductKey, [FromForm] String DeviceName, [FromForm] String DeviceSecret)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(SaveDeviceInfo));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        if (ProductKey.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("产品Key不能为空", Lng);
            return result;
        }

        if (DeviceName.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("设备DN不能为空", Lng);
            return result;
        }

        var model = Product.FindByCode(ProductKey);
        if (model == null)
        {
            result.Message = GetResource("产品不存在", Lng);
            return result;
        }

        var device = Data.Device.FindByCode(DeviceName);
        if (device != null)
        {
            result.Message = GetResource("设备已存在", Lng);
            return result;
        }

        device = new Data.Device
        {
            Name = DeviceName,
            Code = DeviceName,
            NickName = DeviceName,
            Secret = DeviceSecret,
            ProductId = model.Id,
            GroupId = 1,
            Enable = true,
            PostPeriod = model.PostPeriod,
            PollingTime = model.PollingTime,
            MId = 0,
            ExpiredTime = DateTime.Now.AddMonths(15)
        };

        var (tableCount, connCount) = Data.Device.HashDbName(DeviceName);
        device.ConnName = connCount;
        device.DBName = tableCount;

        device.Insert();

        // 复制产品属性
        device.Fix(true);

        // 更新产品信息
        device.Product?.Fix();

        result.Data = new
        {
            model.ProjectKey,
            ProductKey = model.Code,
            ProductSecret = model.Secret,
            DeviceName = device.Code,
            DeviceSecret = device.Secret,
        };
        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 获取设备五元组数据。要通过第三方平台鉴权，主要针对客户端等容易泄露的场景
    /// </summary>
    /// <param name="Id"></param>
    /// <param name="Lng"></param>
    /// <param name="DeviceName">设备DN</param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("GetDeviceInfo")]
    //[DeviceApiSign]
    [ApiSignature]
    public async Task<IActionResult> GetDeviceInfo([FromHeader] String Id, [FromHeader] String Lng, [FromForm] String DeviceName)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(GetDeviceInfo));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        if (DeviceName.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("设备DN不能为空", Lng);
            return result;
        }

        var device = Data.Device.FindByCode(DeviceName);
        if (device == null)
        {
            result.Message = GetResource("设备不存在", Lng);
            return result;
        }

        // 缓存
        var client = new Pek.Webs.Clients.WebClient();
        var res = await client.Post("https://fl.hlktech.com/api/v1/http2/getcheckcache")
            .IgnoreSsl()
            .ContentType(HttpContentType.FormUrlEncoded)
            .Header("Id", IdHelper.GetNextId())
            .Header("Lng", Lng)
            .Data("CacheKey", Id)
            .Retry(3)
            .WhenCatch<HttpRequestException>(ex =>
            {
                return $"请求失败：{ex.StackTrace}";
            })
            .ResultStringAsync();

        if (res.Contains("请求失败"))
        {
            result.ErrCode = 1001;
            result.Message = GetResource("请求失败", Lng);
            return result;
        }

        var dGResult = res.ToJsonEntity<DGResult>();
        if (dGResult?.Code != StateCode.Ok)
        {
            result.ErrCode = 1002;
            result.Message = GetResource("请求失败", Lng);
            return result;
        }

        String cacheValue = dGResult.Data ?? String.Empty;
        if (cacheValue.Contains('_'))
        {
            var arr = cacheValue.Split("_");
            if (arr.Length == 2)
            {
                var UId = arr[0].ToInt();
                var UName = arr[1];

                // 写入请求信息到数据库

            }
            else
            {
                result.ErrCode = 1003;
                result.Message = GetResource("请求失败", Lng);
                return result;
            }
        }
        else
        {
            result.ErrCode = 1004;
            result.Message = GetResource("请求失败", Lng);
            return result;
        }

        var content = $"{device.Code}|{device.Product?.ProjectKey}|{device.Product?.Code}|{device.Product?.Secret}|{device.Secret}";
        var key = DHLZW.GetKeyFromText(device.Code);

        result.Data = new
        {
            DeviceName = device.Code,
            Content = Base64Helper.StringToBase64(DHLZW.Compress(content, key).Join()),
        };

        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 获取设备五元组数据。适用于内部云平台间调用(根据IP限制)
    /// </summary>
    /// <param name="Id"></param>
    /// <param name="Lng"></param>
    /// <param name="DeviceName">设备DN</param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("GetFiveDeviceInfo")]
    //[DeviceApiSign]
    //[ApiSignature]
    public IActionResult GetFiveDeviceInfo([FromHeader] String Id, [FromHeader] String Lng, [FromForm] String DeviceName)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(GetFiveDeviceInfo));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        if (DeviceName.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("设备DN不能为空", Lng);
            return result;
        }

        var device = Data.Device.FindByCode(DeviceName);
        if (device == null)
        {
            result.Message = GetResource("设备不存在", Lng);
            return result;
        }

        var ip = UserHost;
        if (Settings.Current.AllowIps.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("不允许请求", Lng);
            return result;
        }

        if (!Settings.Current.AllowIps.Split(',').Contains(ip))
        {
            result.Message = GetResource("不允许请求", Lng);
            return result;
        }

        var content = $"{device.Code}|{device.Product?.ProjectKey}|{device.Product?.Code}|{device.Product?.Secret}|{device.Secret}";
        var key = DHLZW.GetKeyFromText(device.Code);

        result.Data = new
        {
            DeviceName = device.Code,
            Content = Base64Helper.StringToBase64(DHLZW.Compress(content, key).Join())
        };

        result.Code = StateCode.Ok;
        return result;
    }
}
