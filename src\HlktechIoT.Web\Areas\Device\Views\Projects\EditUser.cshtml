@model ProjectShare
@{
    Html.AppendTitleParts(T("编辑成员").Text);
        // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        padding-top: 30px;
        padding-left: 30px;
    }

    .layui-form-item.btn {
        padding-top: 10px;
        padding-left: 115px;
    }

    .label-width span {
        color: #f00;
    }
    .label-width{
        white-space:nowrap;
    }

    .layui-form-label {
        width: 94px;
    }

    .upload {
        cursor: pointer;
        max-width: 120px;
        max-height: 90px;
    }
    .select{
        max-width: 420px !important;
        border: 1px solid #e6e6e6;
    }
    .font-style{
        font-size:10px;
        font-weight:lighter;
    }
</style>
<div class="containers">
    <form class="layui-form">

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("项目成员账号")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="UserName" placeholder="@T("请输入项目成员账号")" autocomplete="off" class="layui-input" lay-filter="Versions" value="@Model.UserName" disabled>
            </div>
        </div>

        

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("备注名")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="UName" placeholder="@T("请输入备注名")" autocomplete="off" class="layui-input" lay-filter="Versions"  value="@Model.UName">
            </div>
        </div>

        <div class="layui-form-item">
                <label class="layui-form-label">@T("权限类型")</label>
                <div class="layui-input-block">
                    <input type="radio" name="RType" value="1" title="@T("指定产品")" lay-filter="demo-radio-filter" @(Model.RType==1 ? "checked" : "")>
                    <input type="radio" name="RType" value="0" title="@T("项目授权")" lay-filter="demo-radio-filter" @(Model.RType==0 ? "checked" : "")>
                </div>
            </div>

        <div class="layui-form-item" id="products"  @(Model.RType==0 ? "hidden" : "")>
            <label class="layui-form-label label-width"></label>
            <div class="layui-input-inline" style="width: 300px;">
                    <div id="demo1" style=" width: 100%;"></div><br/>
                 <lable class="font-style">选择指定产品授权，被授权者只允许编辑授权产品，不可在该项目下创建、复制和删除产品</lable><br/>
                 </div>
        </div>

        <div class="layui-form-item layui-row">
            <label class="layui-form-label label-width">@T("权限范围")</label>
            <div class="layui-col-xs12">
                <table class="layui-table">
                    <tr>
                         <th><input type="checkbox" class="layui-bg-gray" name="RScope0" value="0" lay-skin="primary" lay-filter="check_all" title="@T("全选")"></th>
                         <td>
                             <div class="layui-row">
                                 <div class="layui-col-xs3">
                                      <input type="checkbox" class="authority" name="RScope1" value="1" lay-skin="primary" lay-filter="authority" title="@T("运营中心")">
                                 </div>
                                 <div class="layui-col-xs3">
                                      <input type="checkbox" class="authority" name="RScope2" value="2" lay-skin="primary" lay-filter="authority" title="@T("开发中心")">
                                 </div>
                                 <div class="layui-col-xs3">
                                     <input type="checkbox" class="authority" name="RScope3" value="3" lay-skin="primary" lay-filter="authority" title="@T("量产中心")">
                                 </div>
                             </div>
                         </td>
                     </tr>
                </table>
            </div>
        </div>
       


        <div class="layui-form-item btn">
            <input hidden name="Id" value="@Model.Id" />
            <input hidden name="ProjectId" value="@Model.ProjectId" />
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" value="@T("保存")" />
        </div>
    </form>
</div>

<script asp-location="Footer">
        var demo1 = xmSelect.render({
            el: '#demo1',
            // radio: true,
            tips:'请选择指定产品',
            name: 'PId',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            // clickClose: true,
            pageRemote: true,  // 分页
            remoteMethod: function (val, cb, show, pageIndex) {  // 远程方法
                var obj = [];
                PId = '@Model.ProductIds';
                // 接口数据
                $.post('@Url.Action("SearchProduct")', { keyword:val,Id:'@Model.ProjectId',PId, page: pageIndex }, function (res) {
                    console.log('搜索后的数据',res);
                    if (res.success) {
                        if (res.data != null) {
                             var json = JSON.parse('@Html.Raw(ViewBag.ProductList)');
                               if (json[0].name == null) {
                                    demo1.update({ disabled: false, remoteSearch: false, pageRemote: false });
                                }
                                 demo1.setValue(json); // 传入一个包含默认值的数组
                            cb(res.data, res.extdata);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });

            },
            on: function (data) {  // 监听选择
                if (data.arr.length > 0) {
                    var a = "";
                    for (var i = 0; i < data.arr.length; i++) {
                        if (i == 0) {
                            
                            a = data.arr[i].value;
                        }
                        else {
                            a += "," + data.arr[i].value;
                        }
                    }
                }
            }
        });
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;
        var layer = layui.layer

        var arr_box = ('@Model.RScope');
        var newarr = arr_box.split(",");
        newarr.shift();
        newarr.pop();
        for (var i = 0; i < newarr.length; i++) { 
            $("input[name='RScope"+newarr[i]+"']").prop("checked", true);
        }
        form.render('checkbox');
        // console.log(window.parent.length); //第一步：先拿到所有父元素
        var parentList = window.parent
        var parentPage = null;

        // 第二步：拿到对应层
        for (let i = 0; i < parentList.length; i++) { 
            if (parentList[i].name === 'EditUser') { //这里的name自己去对应层DIY
                parentPage = parentList[i]
                break;
            }
        }
        // 第三步：操作对应层
        var parent_window = parentPage.window  //获取父层的window层
        var parent_layer = parentPage.layer //获取父层的layer
        var parent_notify = parentPage.layui.abp.notify //获取父层的layui.notify --消息通知
        var parent_layui  = parentPage.layui  //获取父层的layui
        var currentPageCloseIndex = parent_window.editPageIndex //当前层的关闭index下标

        form.on('radio(demo-radio-filter)', function(data){
            var elem = data.elem; // 获得 radio 原始 DOM 对象
            var checked = elem.checked; // 获得 radio 选中状态
            var value = elem.value; // 获得 radio 值
            var othis = data.othis; // 获得 radio 元素被替换后的 jQuery 对象
            if (value == 0) {
                $("#products").hide();
            } else {
                $("#products").show();
            }
        });

        form.on('checkbox(check_all)', function(data){
            $(this).parent().siblings("td").find("input").prop("checked", this.checked);
            form.render('checkbox');
          });

        form.on('checkbox(authority)', function(data){
            var elem = data.elem; // 获得 checkbox 原始 DOM 对象
            var checked = elem.checked; // 获得 checkbox 选中状态
            var value = elem.value; // 获得 checkbox 值
            var othis = data.othis; // 获得 checkbox 元素被替换后的 jQuery 对象
            if (this.checked) {
                if ($(this).parent().siblings("div").children().filter(".authority").not("input:checked").length == 0) {
                    $(this).parent().parent().parent().siblings("th").children("input").prop("checked", true);
                    form.render("checkbox");
                }
            } else { 
                $(this).parent().parent().parent().siblings("th").children("input").prop("checked", false);
                    form.render("checkbox");
            }
          });

        form.on('submit(Submit)',  (data)=> {
            if (data.field.UserName.length == 0) {
                abp.notify.warn("@T("用户名称不能为空")");
                return;
            }
              var arr_box = [];
            $('input[type=checkbox]:checked').each(function() {
                arr_box.push($(this).val());
            });
            var RScopes = arr_box.toString();
            data.field.RScope = RScopes;
            var waitIndex = layer.load(2);

            abp.ajax({
                url: "@Url.Action("EditUser")",
                contentType : "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done( (data)=> {
                 if (!data.success) {
                    console.log(data);
                    abp.notify.error(data.msg);
                    return false;
                }
                parent_notify.success(data.msg)
                parentPage.active.reload()
                dg.reload('tables'); //刷新首页
                // 关闭当前编辑页面
                parent.layer.close(currentPageCloseIndex);
            }).fail(function (jqXHR) {
                layer.msg(jqXHR.message, { icon: 5 }); //新增失败且不关闭当前页面
            }).always(function () {
                layer.close(waitIndex);
            });
            layer.close(waitIndex);
            return false;


        });
       
    });
</Script>