syntax = "proto3";

option csharp_namespace = "HlktechIoT";

package greet;

service Greeter {
  rpc <PERSON><PERSON> (HelloRequest) returns (HelloReply);
  rpc PProcessUpData (PUpDataRequest) returns (PUpdataReply);
  rpc UpdateThingStatus (ThingStatusRequest) returns (ThingStatusReply);
}

message HelloRequest {
  string name = 1;
}

message HelloReply {
  string message = 1;
}

message PUpDataRequest {
  string topic = 1;
  bytes payload = 2;
}

message PUpdataReply {
}

message ThingStatusRequest {
  string iotId = 1;
  string status = 2;
  int64 time = 3;
}

message ThingStatusReply {
}