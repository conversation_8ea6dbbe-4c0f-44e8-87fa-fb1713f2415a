﻿using DG.SafeOrbit.Extensions;
using HlktechIoT.Data;
using NewLife.Data;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using XCode;

namespace HlktechIoT.Entity
{
    public class ProjectEX : Project
    {
        /// <summary>根据列表uId和项目Id获取列表</summary>
        /// <param name="uId">用户Id</param>
        /// <param name="projectId">项目Id</param>
        public static IList<Project> SearchIn(int uId, int projectId)
        {
            WhereExpression where = new WhereExpression();
            if (uId != 0)
            {
                where &= @_.CreateUserID == uId;
            }
            if (projectId != 0)
            {
                where &= @_.Id == projectId;
            }

            return Entity<Project>.FindAll(where);
        }
    }
}
