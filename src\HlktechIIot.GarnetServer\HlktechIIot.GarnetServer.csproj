﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
	<Platforms>AnyCPU</Platforms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>GServer</RootNamespace>
	<AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="DH.NAgent" Version="4.12.2025.617-beta1020" />
    <PackageReference Include="DH.NStardust" Version="4.12.2025.621-beta0339" />
    <PackageReference Include="Microsoft.Garnet" Version="1.0.72" />
  </ItemGroup>

  <ItemGroup>
    <None Update="garnet.conf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
