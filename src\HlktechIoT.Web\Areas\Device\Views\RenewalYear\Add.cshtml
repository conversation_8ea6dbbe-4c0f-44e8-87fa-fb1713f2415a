﻿@{
    Html.AppendTitleParts(T("新增续期年限").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        padding-top: 30px;
        padding-left: 30px;
    }

    .layui-form-item.btn {
        padding-top: 10px;
        padding-left: 115px;
    }

    .label-width span {
        color: #f00;
    }

    .layui-form-label {
        width: 94px;
    }

    .upload {
        cursor: pointer;
        max-width: 120px;
        max-height: 90px;
    }
    .select{
        max-width: 420px !important;
        border: 1px solid #e6e6e6;
    }
    .uploadImage{
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
    #uploadDemoView{
        position: relative;
    }
    .close{
        content: "×";
        position: absolute;
        top: 10px;
        right: 0;
        width: 20px;
        height: 20px;
        font-size: 20px;
        line-height: 20px;
        color: #009688;
        background-color: rgba(0, 0, 0, 0.2);
        text-align: center;
        border-radius: 2px;
        cursor: pointer;
        z-index: 1000000;
        transition: all 1s;
    }
    .close:hover{
        color: red;
        transform: scale(1.2);
    }
</style>
<div class="containers">
    <form class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("年限")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="Year" placeholder="@T("请输入年限")" autocomplete="off" class="layui-input" lay-filter="Versions">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("价格")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="Amount" placeholder="@T("请输入价格")" autocomplete="off" class="layui-input" lay-filter="Versions">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("数量")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="Quantity" placeholder="@T("请输入数量")" autocomplete="off" class="layui-input" lay-filter="Versions">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("折扣")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="Discount" placeholder="@T("请输入折扣")" autocomplete="off" class="layui-input" lay-filter="Versions">
            </div>
        </div>
        <div class="layui-form-item btn">
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" value="@T("保存")" />
        </div>
    </form>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        form.on("switch(switch)", function (data) {
            let checked = data.elem.checked;
            $("input[name='ApnsProduction']").val(checked?'on':'off');
        });

        form.on("switch(switch1)", function (data) {
            let checked = data.elem.checked;
            $("input[name='LimitMultiLogin']").val(checked?'on':'off');
        });
        
        form.on('submit(Submit)', function (data) {
             if (data.field.Year.length == 0) {
                abp.notify.warn("@T("年限不能为空")");
                return;
            }
             if (data.field.Amount.length == 0) {
                abp.notify.warn("@T("价格不能为空")");
                return;
            }

            var field = data.field;

            var waitIndex = parent.layer.load(2);
            abp.ajax({
                url: "@Url.Action("Add")",
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                data: field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                dg.reload('tables');
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });
    });
</Script>