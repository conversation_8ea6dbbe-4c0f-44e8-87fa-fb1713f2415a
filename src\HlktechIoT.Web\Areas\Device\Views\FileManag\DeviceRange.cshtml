@{
    Html.AppendTitleParts(T("设备范围").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
  .footer-btn {
    margin-top: 20px;
  }
</style>

<div class="device-range">
  <div class="dgtable-body" style="margin-top: 0px; padding-top: 15px;">
    <div class="layui-form" style="display: flex; width: 100%; margin-bottom: 20px;">
      @* <div class="layui-inline" style="margin-right: 10px;">
        <div class="layui-input-inline">
            <input type="text" name="mutex" id="mutex" placeholder="@T("版本筛选")"
            autocomplete="off" class="layui-input" style="width: 190px !important; margin-right: 10px;">
        </div>
      </div> *@

      <div class="layui-inline" style="margin-right: 10px;">
        <div class="layui-input-inline">
            <input type="text" name="name" id="name" placeholder="@T("请输入设备名称")"
            autocomplete="off" class="layui-input" style="width: 190px !important; margin-right: 10px;">
        </div>
      </div>

      <div class="layui-inline">
        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" id="allData">@T("全部")</button>
        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" id="selected">@T("已选择") <span id="selectCount"></span></button>
        
      </div>
    </div>
    <table id="tablist" lay-filter="tool"></table>
  </div>
  <div class="footer-btn">

    <button type="button" class="pear-btn pear-btn-primary pear-btn-normal" id="submit">确定</button>
    <button type="button" class="layui-btn layui-btn-primary" id="cancel">取消</button>
  </div>
</div>

<script asp-location="Footer">
  layui.use(['table', 'abp', 'dg', 'form', 'dgcommon', 'dtree'], function () {
    var $ = layui.jquery;
    var abp = layui.abp;
    var form = layui.form;
    var table = layui.table;
    var dg = layui.dg;
    var os = layui.dgcommon;
    var dtree = layui.dtree;
    $('#submit').on('click', function() {
      window.top.receiveDeviceIds(deviceRangeInfo);
      parent.layer.close('deviceRange');
    })

    $('#cancel').on('click', function () {
      parent.layer.close('deviceRange');
    })

   

    // 获取传过来的参数
    const searchParams = new URLSearchParams(window.location.search);

    // 获取已经选中的数据
    let deviceRangeInfo = JSON.parse(localStorage.getItem('deviceRangeInfo'))
    $('#selectCount').html(`(${deviceRangeInfo.length})`)
    // 不在第一页已经选中的数据列表
    let notInPageData = deviceRangeInfo

    let allTableData = []
    var tableInt = table.render({
      elem: '#tablist'
      , url: '@Url.Action("SearchAllDevices")'
      , method: "POST"
      , page: true //开启分页
      , where: {
        productModuleId: searchParams.get('productModuleId') || null, // 7
        productIds: searchParams.get('productIds') || null,           // null（空字符串转null）
        version: searchParams.get('version') || null    
      }
      , cellMinWidth: 80
      , cols: [[
        { type: 'checkbox' }
        , { field: 'name', title: '@T("DeviceName")' }
        , { field: 'mutex', title: '@T("版本号")', width: 110 }
      ]]
      , response: {
        countName: 'extdata'
      }
      , limit: 10
      , limits: [10, 13, 20, 30, 50, 100]
      , height: 'full-200'
      , id: 'tablist'
      , parseData: function (res) {
        const ids = deviceRangeInfo.map(item => item.value) || []
        
        res.data.map(item => {
          if (ids.includes(item.value)) {
            notInPageData = notInPageData.filter(i => i.value !== item.value)
            item.LAY_CHECKED = true;
          }
        })
      }
    })
    $('#allData').on('click', function () {
      active.reload()
    })

    $('#selected').on('click', function () {
      if (deviceRangeInfo.length == 0) {
        layer.msg('请选择设备');
        return;
      }

      table.reload('tablist', {
        url: "",
        data: deviceRangeInfo,
        cols: [[
          { type: 'checkbox', LAY_CHECKED: true }
          , { field: 'name', title: '@T("DeviceName")' }
          , { field: 'mutex', title: '@T("版本号")', width: 110 }
        ]],
        page: {
          curr: 1
        }, //开启分页
       })
    })
      window.active = {
        reload: function (type) {
          table.reload('tablist',
            {
              url: '@Url.Action("SearchAllDevices")', 
              method: "POST", 
                where: {
                  productModuleId: searchParams.get('productModuleId') || null, // 7
                  productIds: searchParams.get('productIds') || null,           // null（空字符串转null）
                  version: searchParams.get('version') || null,  
                  key: $("#name").val(),
              },
              cols: [[
                { type: 'checkbox' }
                , { field: 'name', title: '@T("DeviceName")' }
                , { field: 'mutex', title: '@T("版本号")', width: 110 }
              ]],
              page: {
                  curr: 1
              },
          });
          
        }
    }

    $("#name").on("input", function (e) {
      active.reload();
    });

    // 勾选复选框
    table.on('checkbox(tool)', function(obj){
      var checkStatus = layui.table.checkStatus('tablist');
      if (obj.checked && obj.type === 'all') {
        // 去重
        const ids = deviceRangeInfo.map(item => item.value)
        checkStatus.data.map(item => {
          if (!ids.includes(item.value)) {
            deviceRangeInfo.push(item)
          }
        })
      } else if (!obj.checked && obj.type === 'all') {
        const pageInfo = obj.config.page
        const { curr, limit } = pageInfo
        deviceRangeInfo.splice((curr - 1) * limit, limit)
      } else if (obj.type === 'one' && obj.checked) {
        deviceRangeInfo.push(obj.dataCache)
      } else if (obj.type === 'one' && !obj.checked) {
        deviceRangeInfo = deviceRangeInfo.filter(item => item.value !== obj.dataCache.value)
      }
      $('#selectCount').html(`(${deviceRangeInfo.length})`)
    });
});
</script>