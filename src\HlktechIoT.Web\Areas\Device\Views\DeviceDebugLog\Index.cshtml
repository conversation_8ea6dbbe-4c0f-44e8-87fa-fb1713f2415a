﻿@{
    Html.AppendTitleParts(T("设备故障日志").Text);

    // 引入脚本
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    .layui-layer-title {
        background-color: #fff;
    }

    .dtree-nav-item {
        padding-left: 0;
    }

    .layui-table-tool {
        z-index: 1;
    }
</style>

<form class="layui-form dg-form">
    <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width: auto">@T("关键词")：</label>
            <div class="layui-input-inline">
                <input type="text" name="key" id="key" placeholder="@T("设备信息、故障数据等")" autocomplete="off" class="layui-input" lay-filter="key">
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">@T("操作时间")：</label>
            <div class="layui-input-inline seller-inline-4">
                <input type="text" name="date" id="date" placeholder="@T("开始时间") @T("到") @T("结束时间")" autocomplete="off" class="layui-input">
            </div>
        </div>
    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;
        var laydate = layui.laydate;

        laydate.render({
            elem: '#date',
            range: '@T("到")',
            format: 'yyyy-MM-dd',
            lang: '@language.UniqueSeoCode',
            trigger: 'click',
            done: function (value, date, endDate) {
                $("#date").val(value)
                active.reload();
            }
        });

        table.render({
            elem: '#tablist',
            url: '@Url.Action("GetList")',
            page: true,
            toolbar: '#user-toolbar',
            defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print'],
            cellMinWidth: 80,
            cols: [[
                { type: 'checkbox' },
                { field: 'ProductKey', title: '@T("产品Key")', minWidth:285},
                { field: 'DeviceCode', title: '@T("设备唯一标识符")', minWidth:285 },
                { field: 'Content', title: '@T("故障数据")',  minWidth:285},
                { field: 'DType', title: '@T("故障类型")',  minWidth:285},
                { field: 'CreateTime', title: '@T("故障创建时间")',minWidth:285, templet: function(d) {
                                if (d.CreateTime && !/^0001/.test(d.CreateTime) && !/^2000/.test(d.CreateTime) && !/^1970/.test(d.CreateTime)) {
                                    return d.CreateTime;
                                }
                                return "";
                            }},
                { fixed: 'right', title: '@T("操作")', toolbar: '#tool',minWidth:175  }
            ]],
            limit: 13,
            limits: [10, 13, 20, 30, 50, 100],
            height: 'full-100',
            id: 'tables'
        });

        window.active = {
            reload: function () {
                table.reload('tables', {
                    where: {
                        key: $("#key").val(),
                        date: $("#date").val()
                    },
                    page: {
                        curr: 1
                    }
                });
            }
        };

        $("#key").on("input", function () {
            active.reload();
        });

        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'refresh') {
                active.reload();
            }
        });

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === 'details') {
                window.details(data);
            }
        });

        window.details = function (data) {
            top.layui.dg.popupRight({
                id: 'LogDetails',
                title: '@T("日志详情")',
                closeBtn: 1,
                area: ['780px'],
                success: function () {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Details")' + abp.utils.formatString("?id={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        };
    });
</script>

<script type="text/html" id="tool">
    @if (this.Has((PermissionFlags)1))
    {
        <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="details"> @T("查看")</a>
    }
</script>
