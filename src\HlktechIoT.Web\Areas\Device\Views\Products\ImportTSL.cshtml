﻿@{
    Html.AppendTitleParts(T("导入TSL").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
        html {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
            background-color: white;
        }

        body {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            background-color: white;
        }

        .layui-input-block {
            margin-right: 40px;
        }

        .layui-form-label {
            width: 100px;
        }

        .uploder-box {
            width: 100%;
            height: 40%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: white;
        }

        .uploder-box .tip {
            text-align: center;
            color: #555;
            font-weight: bold;
            font-size: 1em;
            margin-top: 30px;
            opacity: .7;
        }

        .dgselect {
            margin-top:20px;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: white;
        }
    </style>
<div class="uploder-box">
    <div style="text-align: center;">
            <div class="layui-upload-list">
                <div class="layui-upload-drag" id="upload">
                <i class="layui-icon"></i>
                <p>点击上传，或将文件拖拽到此处</p>
                <div class="layui-hide" id="uploadDemoView">
                    <hr>
                    <label id="excel" class="layui-form-label-left"></label>
                </div>
            </div>
            <!-- 进度条 -->
            <div class="layui-progress layui-progress-big" style="margin: 10px auto 0px auto;width:258px;" lay-showPercent="yes" lay-filter="filter-demo">
                <div class="layui-progress-bar" lay-percent=""></div><!-- 进度条 绿色 -->
            </div>
        </div>
        <p class="tip" style="color:red;margin-top: -10px;">@T("说明:导入txt文档,格式如下图。")</p>
    </div>
</div>
<div style="text-align:center;">
    <img src='/images/importTSL.png' />
</div>



<script asp-location="Footer">
    layui.use(['laydate', "form", "table", "jquery", "common", 
    "layer", 'upload','dg', "dgcommon"], function () {
        var t = layui.form;
        var upload = layui.upload;
        var $ = layui.jquery;
        var os = layui.common;
        var dgcommon = layui.dgcommon;
        var laydate = layui.laydate;
        var element = layui.element;

        var dg = layui.dg;
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);
        t.render();

            var iframeName = window.name;
            var iframeArray = iframeName.split('_');
            var index = iframeArray[1];
            var parentIframe = iframeArray[0];

            var targetIframe = null;

            var parentIframes = window.parent.document.getElementsByTagName('iframe');
            for (var i = 0; i < parentIframes.length; i++) {
                if (parentIframes[i].name == parentIframe) {
                    targetIframe = parentIframes[i];
                    break;
                }
            }

        //拖拽阈值表上传
        const renderUpload = () => {
            upload.render({
            elem: '#upload'
            , url: '@Url.Action("UpLoadFilesTSL")' //改成您自己的上传接口
            , before: function (obj) {
                        this.data = { 'Id': @Model.Id };
                    element.progress('filter-demo', '0%'); // 进度条复位
                    layer.msg('上传中', {icon: 16, time: 0});
                }
                , done: function (res) {
                    layer.closeAll('loading');
                    if (!res.success) { //失败打印
                        dgcommon.warning(res.msg);
                        return;
                    }
                    dgcommon.success(res.msg);
                    targetIframe.contentWindow.active.reload();
                },
                // 进度条
                progress: function(n, elem, e){
                    element.progress('filter-demo', n + '%'); // 可配合 layui 进度条元素使用
                    if(n == 100){
                        layer.msg('上传完毕', {icon: 1});
                    }
                }
                , accept: 'file' //允许上传的文件类型
                , exts: 'txt' //只允许上传txt文件
            });

            t.on("submit(cancel)", function () {
                parent.layer.closeAll();
            })
        }
        renderUpload();

    });
</script>