/* 动态操作列通用样式 */
.layui-table td, .layui-table th {
    border-right: none;
}

.layui-table-tool {
    z-index: 1;
}

/* 操作列自适应样式 */
.layui-table-fixed-r .layui-table-body {
    overflow-x: hidden !important;
}

.operation-column {
    white-space: nowrap;
    padding: 0 2px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: nowrap;
    height: 100%;
    width: 100%;
    box-sizing: border-box;
}

.operation-column .pear-btn {
    margin-right: 4px;
    padding: 0 10px;
    height: 30px;
    line-height: 30px;
    min-width: 50px;
    text-align: center;
    display: inline-block;
    vertical-align: middle;
    white-space: nowrap;
    box-sizing: border-box;
}

.operation-column .pear-btn:last-child {
    margin-right: 0;
}

/* 禁用按钮的完整样式 */
.operation-column .pear-btn.disabled-button {
    background-color: #d9d9d9 !important;
    border-color: #d9d9d9 !important;
    color: #999 !important;
    cursor: not-allowed !important;
    opacity: 0.65;
    box-shadow: none !important;
}

/* 禁用按钮悬停时保持灰色，不变色 */
.operation-column .pear-btn.disabled-button:hover {
    background-color: #d9d9d9 !important;
    border-color: #d9d9d9 !important;
    color: #999 !important;
    cursor: not-allowed !important;
    box-shadow: none !important;
    transform: none !important;
}

/* 禁用按钮点击时也保持灰色 */
.operation-column .pear-btn.disabled-button:active,
.operation-column .pear-btn.disabled-button:focus {
    background-color: #d9d9d9 !important;
    border-color: #d9d9d9 !important;
    color: #999 !important;
    cursor: not-allowed !important;
    box-shadow: none !important;
    transform: none !important;
}

/* 确保禁用状态优先级最高 */
.operation-column .pear-btn.disabled-button.pear-btn-success,
.operation-column .pear-btn.disabled-button.pear-btn-primary,
.operation-column .pear-btn.disabled-button.pear-btn-danger,
.operation-column .pear-btn.disabled-button.pear-btn-warming {
    background-color: #d9d9d9 !important;
    border-color: #d9d9d9 !important;
    color: #999 !important;
}

/* 禁用按钮各种状态下都保持灰色 */
.operation-column .pear-btn.disabled-button.pear-btn-success:hover,
.operation-column .pear-btn.disabled-button.pear-btn-primary:hover,
.operation-column .pear-btn.disabled-button.pear-btn-danger:hover,
.operation-column .pear-btn.disabled-button.pear-btn-warming:hover,
.operation-column .pear-btn.disabled-button.pear-btn-success:active,
.operation-column .pear-btn.disabled-button.pear-btn-primary:active,
.operation-column .pear-btn.disabled-button.pear-btn-danger:active,
.operation-column .pear-btn.disabled-button.pear-btn-warming:active,
.operation-column .pear-btn.disabled-button.pear-btn-success:focus,
.operation-column .pear-btn.disabled-button.pear-btn-primary:focus,
.operation-column .pear-btn.disabled-button.pear-btn-danger:focus,
.operation-column .pear-btn.disabled-button.pear-btn-warming:focus {
    background-color: #d9d9d9 !important;
    border-color: #d9d9d9 !important;
    color: #999 !important;
    cursor: not-allowed !important;
    box-shadow: none !important;
    transform: none !important;
}

.layui-table-cell {
    text-align: center;
    padding: 0 2px;
}

.layui-table-fixed {
    height: auto !important;
}

.layui-table-fixed-r {
    right: 0 !important;
}

.layui-table-fixed-r .layui-table-cell {
    padding: 0;
}