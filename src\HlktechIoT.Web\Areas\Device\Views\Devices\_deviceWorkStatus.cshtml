@{
    var Id = Model.Device.Id;
    var list = Model.PropertyList;
}
<style>
.pageBox{
    width: 98%;
    /* border: 1px solid ; */
}
.title{
    padding: 10px;
    font-size: 18px;
    font-weight: 600;
    color: rgba(70, 70, 70, 1);
    /* font-weight: bold; */
}
.card{
    padding: 10px 10px 20px 10px;
    border-radius: 5px;
    background-color: white;
}
.runingTask{
    margin: 8px;
    display: flex;
    flex-wrap: wrap;
}
.itemBox{
    width: 300px;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 3px;
}
.itemBox>div{
     /* width: 100%; */
    /* min-height: 40px; */
    display: flex;
    max-width: 300px;
    align-items: center; /* 垂直居中 */
}
.icon{
    width: 20px;
    border-radius: 5px;
}
.copy{
    margin-left: auto;
    margin-right: 10px;
    color: dodgerblue;
    text-shadow: 0 0 1px skyblue;
    cursor: pointer;
    transition: all 1s;
}
.copy:hover{
    text-decoration: underline;
}

.layui-unselect {
    float:right;
}

    .itemSpan {
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex-shrink: 1; /* 允许子元素收缩 */
        max-width: 280px;
        margin-bottom: 25px;
        margin-top:25px;
    }

   /*  .layui-layer-tips .layui-layer-TipsG {
        border-color: transparent #000 transparent transparent !important;
    } */

    .layui-layer-TipsG {
        width: 15px !important;
        height: 15px !important;
        background-color: #fff;
        transform: rotate(45deg);
        border-color: #fff !important;
        box-shadow: 0px 3px 10px 0px rgba(143, 140, 140, 0.31) !important;
        border: 1px solid #000 !important;
    }

    .layui-layer-TipsB:after {
        position: absolute;
        bottom: -27px;
        left: 5px;
        content: '';
        width: 12px;
        height: 58px;
        background: #fff;
        transform: rotate(45deg);
    }

    .layui-layer-TipsT:after {
        position: absolute;
        bottom: 0px;
        left: -1px;
        content: '';
        width: 14px;
        height: 33px;
        background: #fff;
        transform: rotate(45deg);
    }


    .layui-layer-tips i.layui-layer-TipsB,
    .layui-layer-tips i.layui-layer-TipsT {
        left: 25px !important;
    }

    .layui-layer-tips .layui-layer-content {
        padding:15px;
        font-size: 15px;
    }

</style>
<div class="pageBox">
    <div class="card" style="margin-top: 15px;">
        <div class="title layui-form" style="margin-top: 8px;">@T("运行状态") <img class="icon" src="~/images/act.jpg"> @* <input type="checkbox" id="check1" name="reloadStatus" lay-text="实时刷新|实时刷新" title="实时刷新|实时刷新" lay-skin="switch" /> *@ </div>
        <div id="shuaxin" style="display: flex;flex-wrap: wrap;">
            @foreach (var item in list)
            {
                string lastPost = "--";
                if (@item.LastPost != null && @item.LastPost is DateTime)
                {
                    DateTime lastPostDateTime = (DateTime)@item.LastPost;
                    string formattedLastPost = lastPostDateTime.ToString("yyyy-MM-dd HH:mm:ss");

                    if (!formattedLastPost.StartsWith("0001"))
                    {
                        lastPost = lastPostDateTime.ToString("yyyy-MM-dd HH:mm:ss");
                    }
                }
                <div class="runingTask">
                    <div class="itemBox">
                        <div class="itemTop"><span style="font-size: 18px;">@item.NickName</span><a class="copy" onclick="SelectValue(@item.Id)">@T("查看数据")</a></div>
                        @if (item.Type == "text"&&item.Value!=null)
                        {
                            <div class="itemCenter">
                                <span class="itemSpan">@item.Value</span>
                            </div>
                        }
                        else if (item.Value == null)
                        {
                            <div class="itemCenter2" style="font-size:25px;margin-bottom: 25px;margin-top:15px">
                                --
                            </div>
                        }
                        else
                        {
                            <div class="itemCenter2" style="font-size:18px;margin-bottom: 25px;margin-top:25px;">
                                @item.Value
                            </div>
                        }
                        <div class="itemBottom" style="color:#838181">
                            @lastPost
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
    <div class="card" style="margin-top: 15px;">
        <div class="title" style="margin-top: 8px;">@T("操作")</div>
        <div class="tagBox">
            <div class="tagItem">
                <button class="pear-btn pear-btn-danger pear-btn-xs btn_width" lay-active="reloads">
                    <i class="layui-icon layui-icon-refresh"></i>
                    @T("刷新")
                </button>
            </div>
        </div>
    </div>
</div>

    <script asp-location="Footer">
        var timer = null;
        layui.use(['form', 'abp', 'util'], function () {
        var util = layui.util,
            form = layui.form,
            abp = layui.abp,
            layer = layui.layer;
        util.event('lay-active', {
            reloads: function () {
                window.reloads();
            }
        });
        window.reloads = function () {
            $("#shuaxin").load(location.href + " #shuaxin>*", "");
            setTimeout(() => {
                reloadMap();
            }, 200)
        };
        $(".itemSpan").mouseenter(function () { //打开tips
                clearTimeout(timer);
                var currentElement = $(this);
                var htmlContent = currentElement.html();
                var tipsIndex = layer.tips("<span style='word-break: break-all;color:#000;'>" + htmlContent + "</span>", currentElement,
                    {
                        tips: [1, 'rgba(255,255,255,0.85)'],
                        time: 0,
                        tipsMore: false,
                        area: ['350px', 'auto'],
                        border: 1,
                    });

                $('#layui-layer' + tipsIndex).on('mouseover', function () {
                    clearTimeout(timer);
                });
                $('#layui-layer' + tipsIndex).on('mouseleave', function () {
                    debounce(close_tips,2000);
                });
            });

            $(".itemCenter").mouseleave(function () { //关闭tips
                debounce(close_tips,2000);
            });
            
        });
        function close_tips() {
            layer.closeAll('tips');
        }
        function debounce(func, wait) {
            clearTimeout(timer)
            timer = setTimeout(() => {
                func();
            }, wait);
        }
    function SelectValue(val) {
        var Id = val;
            top.layui.dg.popupRight({
                id: 'DeviceLogList'
                , title: ' @T("设备属性历史数据")'
                , closeBtn: 1
                , area: ['780px', '100%']
                , success: function () {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("DeviceLogList")?Id=' + Id + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }
        function reloadMap() {
            location.reload();
            localStorage.setItem('myData',1);
        }
</script>