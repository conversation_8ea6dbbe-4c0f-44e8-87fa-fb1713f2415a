@{
    Html.AppendTitleParts(T("模组固件批量升级").Text);

    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style>
    .containers {
        max-height: 90vh; /* 设置最大高度 */
        overflow-y: auto; /* 超出时显示滚动条 */
        padding-bottom: 20px; /* 确保底部有足够的空间 */
    }

    .layui-form-label {
        width: 200px;
        /* white-space: nowrap; */
    }

    span {
        color: crimson;
    }

    .btn {
        margin-left: 180px;
    }

    #input-container {
        display: flex;
        flex-direction: column;
    }

    .device-item {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px 10px;
        background-color: #f5f5f5;
        margin: 5px;
        border-radius: 30px
    }
    .placeholder {
        color: #999;
        width: 100%;
        text-align: left;
        margin-left: 10px;
    }
</style>
<script asp-location="Head">
    var layuiNumber = '@T("个")';
    var pleaseSelect = '@T("请选择")';
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPage = '@T("页")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
    var layuiCloseCurrent = '@T("关 闭 当 前")';
    var layuiCloseOther = '@T("关 闭 其 他")';
    var layuiCloseAll = '@T("关 闭 全 部")';
    var layuiMenuStyle = '@T("菜单风格")';
    var layuiTopStyle = '@T("顶部风格")';
    var layuiThemeColor = '@T("主题配色")';
    var layuiMoreSettings = '@T("更多设置")';
    var layuiOpen = '@T("开")';
    var layuiClose = '@T("关")';
    var layuiMenu = '@T("菜单")';
    var layuiView = '@T("视图")';
    var layuiBanner = '@T("通栏")';
    var layuiThroughColor = '@T("通色")';
    var layuiFooter = '@T("页脚")';
    var layuiSelectAll = '@T("全选")';
    var layuiClear = '@T("清空")';
    var layuiReverseSelection = '@T("反选")';
    var layuiPeeling = '@T("换肤")';
    var layuiNoDataYet = '@T("暂无数据")';
    var layuiSearch = '@T("搜索")';
    var layuiPrevious = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNotAllowClose = '@T("前页面不允许关闭")';
    var layuiOpenAtMost = '@T("最多打开")';
    var layuiTabs = '@T("个标签页")';
</script>
<div class="containers">
    <form class="layui-form" id="updateForm">
        <input type="hidden" name="firmwareId" value="@Model.Id" />

        <div class="layui-form-item">
            <label class="layui-form-label"><span>*</span>@T("所属产品")</label>
            <div class="layui-input-inline" style="width: 360px;">
                <div id="demo1" style=" width: 100%;"></div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">@T("APP 确认升级")</label>
            <div class="layui-input-inline" style="width: 360px;">
                <input type="checkbox" lay-filter="switch" name="appPush" lay-skin="switch" lay-text="@T("是")|@T("否")">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"><span>*</span>@T("升级策略")</label>
            <div class="layui-input-inline" style="width: 360px;">
                <div id="demo2" style=" width: 100%;"></div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"><span>*</span>@T("待升级版本号")</label>
            <div class="layui-input-inline" style="width: 360px;">
                <div id="demo9" style=" width: 100%;"></div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"><span>*</span>@T("升级范围")</label>
            <div class="layui-input-inline" style="width: 360px;">
                <div id="demo3" style=" width: 100%;"></div>
            </div>
        </div>
        <div class="layui-form-item" id="demothr1" hidden>
            <label class="layui-form-label"><span>*</span>@T("设备范围")</label>
            <div class="layui-input-inline" style="width: 360px;">
                <div id="demo6" style="width: 100%; min-height: 36px; background-color: #fff; border: 1px solid #ededed; display: flex; flex-wrap: wrap; align-items: center">
                    
                </div>
            </div>
        </div>
        <div class="layui-form-item" id="demothr2" hidden>
            <label class="layui-form-label"><span>*</span>@T("指定地域")</label>
            <div id="input-container">
                <div class="input-pair">
                    <div class="layui-input-inline" style="width: 150px;">
                        <div id="province0" style=" width: 100%;"></div>
                    </div>
                    <div class="layui-input-inline" style="width:150px;">
                        <div id="city0" style=" width: 100%;"></div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item btn">
                <button type="button" id="add-button" class="pear-btn pear-btn-primary pear-btn-normal">新增</button>
            </div>
        </div>
        <div class="layui-form-item" id="demothr3" hidden>
            <label class="layui-form-label"><span>*</span>@T("灰度范围（%）")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="grayRange" id="grayRange" placeholder="@T("请输入灰度百分比")" autocomplete="off" class="layui-input" style="width: 360px;">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><span>*</span>@T("升级时间")</label>
            <div class="layui-input-inline" style="width: 360px;">
                <div id="demo4" style=" width: 100%;"></div>
            </div>
        </div>
        <div class="layui-form-item" id="demofour" hidden>
            <label class="layui-form-label"><span>*</span>@T("升级开始时间")</label>
            <div class="layui-input-inline" style="width: 360px;">
                <input type="text" name="start" id="start" readonly placeholder="@T("开始时间")" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item" id="demofour2" hidden>
            <label class="layui-form-label"><span>*</span>@T("升级结束时间")</label>
            <div class="layui-input-inline" style="width: 360px;">
                <input type="text" name="end" id="end" readonly placeholder="@T("结束时间")" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("固件推送速率")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="pushRate" id="pushRate" placeholder="@T("请输入每分钟推送的设备数")" autocomplete="off" class="layui-input" style="width: 360px;">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><span>*</span>@T("升级失败重试间隔")</label>
            <div class="layui-input-inline" style="width: 360px;">
                <div id="demo5" style=" width: 100%;"></div>
            </div>
        </div>
        <div class="layui-form-item" id="demofire" hidden>
            <label class="layui-form-label label-width" style="margin-top: -5px;">@T("升级重试上限次数")</label>
            <div class="layui-input-inline" style="width: 360px;">
                <select id="demo10" onchange="">
                    <option value="1">1次</option>
                    <option value="2">2次</option>
                    <option value="3">5次</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width" style="margin-top: -5px;">@T("设备升级超时时间（分钟）")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="timeout" id="timeout" placeholder="@T("请输入超时时间（分钟）")" autocomplete="off" class="layui-input" style="width: 360px;">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" style="margin-top: -5px;"><span>*</span>@T("是否覆盖设备之前的升级任务")</label>
            <div class="layui-input-inline" style="width: 360px;">
                <input type="checkbox" lay-filter="switch" name="isOverride" lay-skin="switch" lay-text="@T("是")|@T("否")">
            </div>
        </div>
        <div class="layui-form-item" id="demotow" hidden>
            <label class="layui-form-label" style="margin-top: -5px;"><span>*</span>@T("是否仅对新上报版本的设备生效")</label>
            <div class="layui-input-inline" style="width: 360px;">
                <input type="checkbox" lay-filter="switch" name="onlyNewlyReported" lay-skin="switch" lay-text="@T("是")|@T("否")">
            </div>
        </div>
        <div class="layui-form-item btn">
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" value="@T("保存")" />
        </div>
    </form>
</div>
<!-- <script src="~/js/xm-select.js" async></script> -->

<script asp-location="Footer">
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var form = layui.form;
        var $ = layui.jquery;
        var common = layui.dgcommon,
            os = layui.abp,
            dg = layui.dg;
        var laydate = layui.laydate;
        var layselect = layui.xmSelect;
        xmSelectList = []; //存储xmSelect对象
        var newData = [
            { name: '@T("全部设备")', value: 0, selected: true, },
            { name: '@T("区域升级")', value: 2, },
        ];
        //升级范围
        var demo3data = [
            { name: '@T("全部设备")', value: 0, selected: true, },
            { name: '@T("定向升级")', value: 1, },
            { name: '@T("区域升级")', value: 2, },
            { name: '@T("灰度升级")', value: 3, }
        ];
        //升级时间
        var demo4data = [
            { name: '@T("立即升级")', value: 1, selected: true, },
            { name: '@T("定时升级")', value: 2, },
        ];
        //升级失败重试间隔
        var demo5data = [
            { name: '@T("不重试")', value: 0, selected: true, },
            { name: '@T("立即重试")', value: 1, },
            { name: '@T("10分钟后重试")', value: 2, },
            { name: '@T("30分钟后重试")', value: 3, },
            { name: '@T("1个小时后重试")', value: 4, },
            { name: '@T("24小时后重试")', value: 5, },
        ];
        //设备范围
        // var demo6data = [
        //     { name: '@T("设备1")', value: 1, selected: true, },
        //     { name: '@T("设备2")', value: 2, },
        // ];

        var productIds = ""; // 定义 productIds 变量
        var demo1 = xmSelect.render({
            el: '#demo1',
            //radio: true,
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            //clickClose: true,//点击选项后是否关闭下拉框
            pageRemote: true,  // 分页
            disabled: true, // 设置禁用
            multiple: false, // 开启多选功能
            remoteMethod: function (val, cb, show, pageIndex) {  // 远程方法
                var obj = [];

                // 接口数据
                $.post('@Url.Action("SearchProduct")', { ProductKey: "@Model.ProductKey", keyword: val, page: pageIndex }, function (res) {
                    if (res.success) {
                        if (res.data != null) {
                            cb(res.data, res.extdata);
                        }
                    }
                    else {
                        cb(obj, 0);
                        common.error(res.data.msg);
                    }
                });

            },
            on: function (data) {  // 监听选择
                var a = "";
                if (data.arr.length > 0) {
                    for (var i = 0; i < data.arr.length; i++) {
                        if (i == 0) {

                            a = data.arr[i].value;
                        }
                        else {
                            a += "," + data.arr[i].value;
                        }
                    }
                }

                productIds = a; // 将选中的值赋给 productIds
                // 重载 demo6
                @* demo6.setValue([]); // 清空勾选值
                demo6.update(); *@
            }
        });

        // 静态搜索
        const demo2 = xmSelect.render({
            el: '#demo2',
            radio: true,
            name: 'BType',
            clickClose: true,
            remoteSearch: false,
            data: [{ name: '@T("静态升级")', value: 1, selected: true, }, { name: '@T("动态升级")', value: 2, }],
            on: function (data) {  // 监听选择
                if (data.arr.length > 0) {
                    var a = data.arr[0].value;
                    if (a == 2) {
                        $("#demothr1").hide();
                        if (demo3data[2].selected) {
                            //静态区域切动态
                            newData = setSelected(newData, 2);
                        } else {
                            newData = setSelected(newData, 0);
                            $("#demothr2").hide();
                        }
                        $("#demothr3").hide();
                        $("#demotow").show();
                        var newData2 = [
                            { name: '@T("立即升级")', value: 1, selected: true, },
                        ];
                        reloadSelect('#demo3', 'Condition', newData)
                        reloadSelect('#demo4', 'TType', newData2)
                        console.log("因动态升级被迫立即升级，隐藏时间范围");
                        $("#demofour").hide();
                        $("#demofour2").hide();
                    } else {
                        if (newData[1].selected) {
                            //动态区域切静态
                            demo3data = setSelected(demo3data, 2);
                        } else {
                            demo3data = setSelected(demo3data, 0);
                            $("#demothr2").hide();
                        }
                        $("#demotow").hide();
                        reloadSelect('#demo3', 'Condition', demo3data)
                        reloadSelect('#demo4', 'TType', demo4data)
                    }
                }
            }
        });
        reloadSelect('#demo3', 'Condition', demo3data)
        reloadSelect('#demo4', 'TType', demo4data)
        reloadSelect('#demo5', 'RetryInterval', demo5data)
       
        var demo6data = []
        window.top.receiveDeviceIds = function(selectedIds) {
            const $demo6 = $('#demo6');
            demo6data = selectedIds || []; // 确保总是数组
            
            // 清空容器
            $demo6.empty();

            // 使用map+join替代循环append，性能更好
            const itemsHtml = demo6data.map(item => 
                `<div class="device-item">
                    ${item.name}
                    <i class="layui-icon layui-icon-close" 
                    data-type="del-device-item"
                    data-id="${item.value}"
                    style="margin-left: 5px;"></i>
                </div>`
            ).join('');
            
            $demo6.append(itemsHtml);
        };

        if (demo6data.length === 0) {
            $('#demo6').append('<div class="placeholder">请选择</div>');
        }
        window.deviceRange = function () {
            localStorage.setItem('deviceRangeInfo', JSON.stringify(demo6data))
            top.layui.dg.popupRight({
                id: 'deviceRange'
                , title: ' @T("请选择设备")'
                , name: 'deviceRange'
                , closeBtn: 1
                , area: ['720px', '100%']
                , success: function () {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("DeviceRange")?productModuleId=' + '@Model.ProductType' + '&productIds=' + productIds + '&version=' + versionToBeUpgraded + '&selectDevices='+ demo6data +'" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }

        // 删除
        $('#demo6').on('click', '[data-type="del-device-item"]', function (e) {
            e.stopPropagation();
            demo6data = demo6data.filter(item => +item.value !== +e.target.dataset.id)
            if (demo6data.length === 0) {
                $('#demo6').append('<div class="placeholder">请选择</div>');
            }
            $(this).closest('.device-item').remove();
        });

        // 打开弹窗
        $('#demo6').click(function (e) {
            if (!$(e.target).closest('[data-type="del-device-item"]').length) {
                window.deviceRange();
            }
        });

        var parentId = "";
        var provinceCounter = 0;
        var cityCounter = 0;
        var xmSelectObjects = {};
        var provCount = 1;
        function createProvinceCityPair() {
            provinceCounter++;
            cityCounter++;
            var provinceId = 'province' + provinceCounter;
            var cityId = 'city' + cityCounter;
            var removeButtonId = 'remove-button' + provinceCounter;
            
            var inputPairHtml = `
                <div class="input-pair">
                    <div class="layui-input-inline" style="width: 150px;">
                        <div id="${provinceId}" style=" width: 100%;"></div>
                    </div>
                    <div class="layui-input-inline" style="width:150px;">
                        <div id="${cityId}" style=" width: 100%;"></div>
                    </div>
                    <button type="button" id="${removeButtonId}" class="pear-btn pear-btn-primary pear-btn-normal">删除</button>
                </div>
            `;

            $('#input-container').append(inputPairHtml);

            var province = xmSelect.render({
                el: '#' + provinceId,
                radio: true,
                filterable: true,
                remoteSearch: true,
                clickClose: true,
                remoteMethod: function (val, cb) {
                    var obj = [];
                    $.post('@Url.Action("SearchAllFirstLevel")', { key: val }, function (res) {
                        if (res.success) {
                            if (res.data != null) {
                                cb(res.data, res.extdata);
                            }
                        } else {
                            cb(obj, 0);
                            common.error(res.data.msg);
                        }
                    });
                },
                on: function (data) {
                    console.log("省份有变化");
                    xmSelectObjects[cityId].setValue([]);
                    if (data.arr.length >= 0) {
                        parentId = "";
                        for (var i = 0; i < data.arr.length; i++) {
                            if (i == 0) {
                                parentId = data.arr[i].value;
                            } else {
                                parentId += "," + data.arr[i].value;
                            }
                        }
                        xmSelectObjects[cityId].update({
                            remoteMethod: function (val, cb) {
                                var obj = [];
                                $.post('@Url.Action("SearchAllSecondLevel")', { parentId: parentId, key: val }, function (res) {
                                    if (res.success) {
                                        if (res.data != null) {
                                            cb(res.data, res.extdata);
                                        }
                                    } else {
                                        cb(obj, 0);
                                        common.error(res.data.msg);
                                    }
                                });
                            },
                        });
                    }
                }
            });

            var city = xmSelect.render({
                el: '#' + cityId,
                radio: true,
                filterable: true,
                remoteSearch: true,
                clickClose: true,
                on: function (data) {
                    if (data.arr.length > 0) {
                        var a = "";
                        for (var i = 0; i < data.arr.length; i++) {
                            if (i == 0) {
                                a = data.arr[i].value;
                            } else {
                                a += "," + data.arr[i].value;
                            }
                        }
                    }
                }
            });

            xmSelectObjects[provinceId] = province;
            xmSelectObjects[cityId] = city;

            $('#' + removeButtonId).click(function () {
                if (provCount <= 1) {
                   common.error('@T("至少选择一个区域升级")');
                   return false;
                } else {
                    $(this).closest('.input-pair').remove();
                    delete xmSelectObjects[provinceId];
                    delete xmSelectObjects[cityId];
                }
                provCount--;
            });
        }

        $('#add-button').click(function () {
            provCount++;
            createProvinceCityPair();
        });

        $(document).ready(function () {
            createProvinceCityPair(); // 初始化第一个省份和城市对
        });

        var versionToBeUpgraded = "";
        var demo9 = xmSelect.render({
            el: '#demo9',
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            multiple: true, // 开启多选功能
            remoteMethod: function (val, cb) {  // 远程方法
                var obj = [];
                // 接口数据
                $.post('@Url.Action("SearchAllDeviceVersion")', { productModuleId: @Model.ProductType, key: val }, function (res) {
                    if (res.success) {
                        if (res.data != null) {
                            cb(res.data, res.extdata);
                        }
                    }
                    else {
                        cb(obj, 0);
                        common.error(res.data.msg);
                    }
                });

            },
            on: function (data) {  // 监听选择
                versionToBeUpgraded = "";
                if (data.arr.length > 0) {
                    for (var i = 0; i < data.arr.length; i++) {
                        if (i == 0) {
                            console.log("监控版本号");
                            versionToBeUpgraded = data.arr[i].value;
                        }
                        else {
                            versionToBeUpgraded += "," + data.arr[i].value;
                        }
                    }
                    // 更新 demo6 的 remoteMethod
                    $('#demo6').empty();
                    $('#demo6').append('<div class="placeholder">请选择</div>');
                    demo6data = []
                    @* demo6.setValue([]); // 清空勾选值
                    demo6.update({
                        remoteMethod: function (val, cb, show, pageIndex) {  // 远程方法
                            var obj = [];
                            // 接口数据

                            $.post('@Url.Action("SearchAllDevices")', { key: val, productModuleId: @Model.ProductType, productIds: productIds, version: versionToBeUpgraded, page: pageIndex }, function (res) {
                                if (res.success) {
                                    if (res.data != null) {
                                        cb(res.data, res.extdata);
                                    }
                                }
                                else {
                                    cb(obj, 0);
                                    common.error(res.data.msg);
                                }
                            });
                        }
                    }); *@
                }
            }
        });

        // 获取当前日期
        function getCurrentDate() {
            var today = new Date();
            var year = today.getFullYear();
            var month = String(today.getMonth() + 1).padStart(2, '0');
            var day = String(today.getDate()).padStart(2, '0');
            var currentDate = year + '-' + month + '-' + day;
            return currentDate;
        }
        //时间插件
        var startDate = laydate.render({
            elem: '#start',
            btns: ['clear', "confirm", 'now'],//只显示清空和确定按钮
            type: 'datetime',       // 设置日期选择类型为年月-日
            min: getCurrentDate(),
            format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月-日
            done: function (value, date) {
                console.log('选择后:', value + '-' + date.date);

                $("#start").val(value);
                checkDateValidity();
            },
            choose: function (date) {
                // 用户选择日期的回调函数
                // 在这里可以处理用户选择日期后的逻辑
                laydate.close(); // 关闭日期选择器弹窗
            }
        });
        var endDate = laydate.render({
            elem: '#end',
            btns: ["clear", "confirm", 'now'],
            type: 'datetime',       // 设置日期选择类型为年月-日
            format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月-日
            done: function (value, date) {
                console.log('选择后:', value + '-' + date.date);

                $("#end").val(value);
                checkDateValidity();
            },
            choose: function (date) {
                // 用户选择日期的回调函数
                // 在这里可以处理用户选择日期后的逻辑
                laydate.close(); // 关闭日期选择器弹窗
            }
        });

        function checkDateValidity() {
            var startValue = $("#start").val();
            var endValue = $("#end").val();

            if (startValue && endValue) {
                var startDate = new Date(startValue + "-01");  // 这里是格式化-作-可对比的日期---格式
                var endDate = new Date(endValue + "-01");

                if (startDate > endDate) {
                    common.warning('开始时间不能晚于结束时间，请重新选择。');
                    $("#start").val(""); // 清空开始时间输入框
                    $("#end").val("");   // 清空结束时间输入框
                }
            }
        }

        // 动态设置selected属性
        function setSelected(data, value) {
            return data.map(item => {
                item.selected = (item.value === value);
                return item;
            });
        }

        var demo3 = xmSelect.render({
            el: '#demo3',
            radio: true,
            clickClose: true,
            on: function (data) {  // 监听选择
                console.log("没切过升级策略监控demo3")
                if (data.arr.length > 0) {
                    var a = data.arr[0].value;
                    demo3data[a].selected = true;//静态切动态，已选区域不会丢失
                    if (a == 1) {
                        $("#demothr1").show();
                        $("#demothr2").hide();
                        $("#demothr3").hide();
                    } else if (a == 2) {
                        $("#demothr1").hide();
                        $("#demothr3").hide();
                        $("#demothr2").show();
                    } else if (a == 3) {
                        $("#demothr1").hide();
                        $("#demothr2").hide();
                        $("#demothr3").show();
                    } else {
                        $("#demothr1").hide();
                        $("#demothr2").hide();
                        $("#demothr3").hide();
                    }
                }
            }
        });
        var demo4 = xmSelect.render({
            el: '#demo4',
            radio: true,
            clickClose: true,
            on: function (data) {  // 监听选择
                console.log("没切过升级策略监控demo4")
                if (data.arr.length > 0) {
                    var a = data.arr[0].value;
                    if (a == 2) {
                        console.log("展开升级时间");
                        $("#demofour").show();
                        $("#demofour2").show();
                    } else {
                        $("#demofour").hide();
                        $("#demofour2").hide();
                    }
                }
            }
        });
        var demo5 = xmSelect.render({
            el: '#demo5',
            radio: true,
            clickClose: true,
            on: function (data) {  // 监听选择
                console.log("没切过升级策略监控demo5");
                if (data.arr.length > 0) {
                    var a = data.arr[0].value;
                    if (a > 0) {
                        $("#demofire").show();
                    } else {
                        $("#demofire").hide();
                    }
                }
            }
        });
        function reloadSelect(id, name, data) {
            switch (id) {
                case '#demo3':
                    const demo3 = xmSelect.render({
                        el: id,
                        radio: true,
                        name: name,
                        clickClose: true,
                        remoteSearch: false,
                        data: data,
                        on: function (data) {  // 监听选择
                            if (data.arr.length > 0) {
                                var a = data.arr[0].value;
                                if (name == 'Condition') {
                                    if (a == 1) {
                                        $("#demothr1").show();
                                        $("#demothr2").hide();
                                        $("#demothr3").hide();
                                    } else if (a == 2) {
                                        $("#demothr1").hide();
                                        $("#demothr3").hide();
                                        $("#demothr2").show();
                                    } else if (a == 3) {
                                        $("#demothr1").hide();
                                        $("#demothr2").hide();
                                        $("#demothr3").show();
                                    } else {
                                        $("#demothr1").hide();
                                        $("#demothr2").hide();
                                        $("#demothr3").hide();
                                    }
                                }
                                else if (name == 'TType') {
                                    console.log("切过升级策略监控demo3");
                                    if (a == 2) {
                                        $("#demofour").show();
                                        $("#demofour2").show();
                                    } else {
                                        $("#demofour").hide();
                                        $("#demofour2").hide();
                                    }
                                }
                                else {
                                    if (a != 0) {
                                        $("#demofire").show();
                                    } else {
                                        $("#demofire").hide();
                                    }
                                }
                            }
                        }
                    });
                    break;
                case '#demo4':
                    const demo4 = xmSelect.render({
                        el: id,
                        radio: true,
                        name: name,
                        clickClose: true,
                        remoteSearch: false,
                        data: data,
                        on: function (data) {  // 监听选择
                            console.log("切过升级策略监控demo4");
                            if (data.arr.length > 0) {
                                var a = data.arr[0].value;
                                if (name == 'Condition') {
                                    if (a == 1) {
                                        $("#demothr1").show();
                                        $("#demothr2").hide();
                                        $("#demothr3").hide();
                                    } else if (a == 2) {
                                        $("#demothr1").hide();
                                        $("#demothr3").hide();
                                        $("#demothr2").show();
                                    } else if (a == 3) {
                                        $("#demothr1").hide();
                                        $("#demothr2").hide();
                                        $("#demothr3").show();
                                    } else {
                                        $("#demothr1").hide();
                                        $("#demothr2").hide();
                                        $("#demothr3").hide();
                                    }
                                }
                                else if (name == 'TType') {
                                    if (a == 2) {
                                        console.log("手改定时升级");
                                        $("#demofour").show();
                                        $("#demofour2").show();
                                    } else {
                                        console.log("手改立即升级");
                                        $("#demofour").hide();
                                        $("#demofour2").hide();
                                    }
                                }
                                else {
                                    if (a != 0) {
                                        $("#demofire").show();
                                    } else {
                                        $("#demofire").hide();
                                    }
                                }
                            }
                        }
                    });
                    break;
                case '#demo5':
                    const demo5 = xmSelect.render({
                        el: id,
                        radio: true,
                        name: name,
                        clickClose: true,
                        remoteSearch: false,
                        data: data,
                        on: function (data) {  // 监听选择
                            console.log("切过升级策略监控demo5");
                            if (data.arr.length > 0) {
                                var a = data.arr[0].value;
                                if (a > 0) {
                                    $("#demo10").show();
                                }
                                if (name == 'Condition') {
                                    if (a == 1) {
                                        $("#demothr1").show();
                                        $("#demothr2").hide();
                                        $("#demothr3").hide();
                                    } else if (a == 2) {
                                        $("#demothr1").hide();
                                        $("#demothr3").hide();
                                        $("#demothr2").show();
                                    } else if (a == 3) {
                                        $("#demothr1").hide();
                                        $("#demothr2").hide();
                                        $("#demothr3").show();
                                    } else {
                                        $("#demothr1").hide();
                                        $("#demothr2").hide();
                                        $("#demothr3").hide();
                                    }
                                }
                                else if (name == 'TType') {
                                    if (a == 2) {
                                        $("#demofour").show();
                                        $("#demofour2").show();
                                    } else {
                                        $("#demofour").hide();
                                        $("#demofour2").hide();
                                    }
                                }
                                else {
                                    if (a != 0) {
                                        $("#demofire").show();
                                    } else {
                                        $("#demofire").hide();
                                    }
                                }
                            }
                        }
                    });
                    break;
            }
        }
        // 监听提交事件
        form.on('submit(Submit)', function (data) {
            if (demo2.getValue().length == 0) {
                common.error('@T("请选择升级策略")');
                return false; // 阻止表单提交
            }

            if (demo3.getValue().length == 0) {
                common.error('@T("请选择升级范围")');
                return false; // 阻止表单提交
            }

            if (demo4.getValue().length == 0) {
                common.error('@T("请选择升级时间")');
                return false; // 阻止表单提交
            }

            if (demo5.getValue().length == 0) {
                common.error('@T("请选择升级失败重试间隔")');
                return false; // 阻止表单提交
            }

            // 自定义表单参数
            var customData = {
                firmwareId: '@Model.Id',
                versionToBeUpgraded: versionToBeUpgraded,
                appPush: data.field.appPush,
                product: productIds,
                upgradeStrategy: demo2.getValue()[0].value,
                upgradeScope: demo3.getValue()[0].value,
                //deviceScope: demo6.getValue().map(item => item.value).join(','),
                //region: city1.getValue()[0].value,
                //grayRange: $('#grayRange').val(),
                upgradeTime: demo4.getValue()[0].value,
                pushRate: $('#pushRate').val(),
                retryInterval: demo5.getValue()[0].value,
                //retryLimit: $('#demo10').val(),
                timeout: $('#timeout').val(),
                isOverride: data.field.isOverride,
                //onlyNewlyReported: data.field.onlyNewlyReported,
            };

            if (versionToBeUpgraded == "") {
                common.error('@T("请选择待升级版本号")');
                return false; // 阻止表单提交
            }

            if (productIds == "") {
                common.error('@T("请选择产品")');
                return false; // 阻止表单提交
            }

            if (customData.upgradeStrategy == 2) {
                customData.onlyNewlyReported = data.field.onlyNewlyReported;
            }

            // 只有在 upgradeScope 为 x 时才传递 x 参数
            if (customData.upgradeScope == 1) {
                if (demo6data.length == 0) {
                    common.error('@T("请选择指定设备")');
                    return false; // 阻止表单提交
                }
                customData.deviceScope = demo6data.map(item => item.value).join(',');
            } else if (customData.upgradeScope == 2) {
                customData.province = "";
                for (var i = 1; i <= provinceCounter; i++) {
                    if (xmSelectObjects["province" + i] !== undefined) {
                        if (xmSelectObjects["province" + i].getValue()[0] == undefined) {
                            common.error('@T("请选择指定地域")');
                            return;
                        }
                        if (customData.province !== "") {
                            customData.province += ",";
                        }
                        customData.province += xmSelectObjects["province" + i].getValue()[0].value;
                    }
                }
                customData.city = "";
                for (var i = 1; i <= cityCounter; i++) {
                    if (xmSelectObjects["city" + i] !== undefined) {
                        if (xmSelectObjects["city" + i].getValue()[0] == undefined) {
                            common.error('@T("请选择指定地域")');
                            return;
                        }
                        if (customData.city !== "") {
                            customData.city += ",";
                        }
                        customData.city += xmSelectObjects["city" + i].getValue()[0].value;
                    }
                }
                if (customData.city == "") {
                    common.error('@T("请选择指定地域")');
                    return false; // 阻止表单提交
                }
            } else if (customData.upgradeScope == 3) {
                var grayRange = $('#grayRange').val();
                if (!grayRange) {
                    common.error('@T("请输入灰度百分比")');
                    return false; // 阻止表单提交
                }
                customData.grayRange = grayRange;
            }

            // 只有在 upgradeTime 为 2 时才传递 x 参数
            if (customData.upgradeTime == 2) {
                var start = $('#start').val();
                var end = $('#end').val();
                if (!start || !end) {
                    common.error('@T("请选择升级时间")');
                    return false; // 阻止表单提交
                }
                customData.startTime = start;
                customData.endTime = end;
            }

            var pushRate = $('#pushRate').val();
            if (pushRate == "") {
                common.error('@T("请输入每分钟推送的设备数")');
                return false; // 阻止表单提交
            }
            if (pushRate == "" || !/^\d+$/.test(pushRate) || pushRate < 10 || pushRate > 1000) {
                common.error('@T("固件推送速率必须为10到1000之间的整数")');
                return false; // 阻止表单提交
            }

            if (customData.retryInterval > 0) {
                var demo10 = $('#demo10').val();
                customData.retryLimit = demo10;
            }

            // 获取 timeout 的值
            var timeout = $('#timeout').val();
            if (timeout == "" || !/^\d+$/.test(timeout) || timeout < 5 || timeout > 1440) {
                common.error('@T("设备升级超时时间必须为5到1440之间的整数")');
                return false; // 阻止表单提交
            }

            // 提交表单
            $.ajax({
                type: "POST",
                url: '@Url.Action("Update", "FileManag", new { area = "Device" })',
                data: customData,
                success: function (response) {
                    if (response.success) {
                        common.success('@T("保存成功")');
                        // 其他成功处理逻辑
                    } else {
                        common.error(response.message || '@T("后端保存失败")');
                    }
                },
                error: function () {
                    common.error('@T("错误")');
                }
            });
            return false; // 阻止表单默认提交
        });
    });
</script>