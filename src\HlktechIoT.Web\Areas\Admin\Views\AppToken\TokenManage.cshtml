﻿@{
    Html.AppendTitleParts("令牌管理");
    var appId = ViewBag.AppId as int?;
    var appName = ViewBag.AppName as string;
}
<div class="layui-form dg-form">
    <div class="layui-form-item" style="margin-bottom: 3px;">
        <div class="layui-inline">
            <label class="layui-form-label">@T("关键词")：</label>
            <div class="layui-input-inline">
                <input type="text" name="key" id="key" autocomplete="off" class="layui-input">
            </div>
        </div>
    </div>
</div>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tokenTable" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    layui.use(['table', 'abp', 'dg', 'form'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;

        table.render({
            elem: '#tokenTable',
            url: '@Url.Action("TokenList")',
            where: { appId: '@appId' },
            page: true,
            toolbar: '#token-toolbar',
            defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print'],
            loading: true,
            cols: [[
                { field: 'Id', title: '@T("编号")', width: 80 },
                { field: 'Name', title: '@T("名称")', width: 120 },
                { field: 'Token', title:  '@T("令牌")', width: 200 },
                { field: 'Enable', title: '@T("启用")', width: 80, templet: function (d) { return d.Enable ? '@T("是")' : '@T("否")'; } },
                { field: 'Expire', title: '@T("有效期")', width: 160 },
                { field: 'UpdateTime', title: '@T("更新时间")', width: 160 },
                { fixed: 'right', title: '@T("操作")', toolbar: '#tool', width: 150 }
            ]],
            height: 'full-100',
            limit: 13,
            limits: [10, 13, 20, 30, 50, 100],
            id: 'tokenTables'
        });

        window.active = {
            reload: function () {
                table.reload('tokenTables', {
                    where: {
                        key: $("#key").val(),
                        appId: '@appId'
                    },
                    page: { curr: 1 }
                });
            }
        };

        $("#key").on("input", function () {
            active.reload();
        });

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === "edit") {
                window.edit(data);
                // top.layui.dg.popupRight({
                //     id: 'TokenDetail',
                //     title: '@T("编辑令牌")',
                //     closeBtn: 1,
                //     area: ['750px'],
                //     success: function (layero, index) {
                //         $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("TokenEdit")?id=' + data.Id + '" frameborder="0" class="layadmin-iframe" name="' + index + '"></iframe>');
                //     },
                //     end: function () {
                //         active.reload();
                //     }
                // });
            } else if (obj.event === "del") {
                parent.layer.confirm('@T("确认删除吗")?', { icon: 3, btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function(index) {
                    $.post('@Url.Action("TokenDelete")', { id: data.Id }, function (data) {
                        if (data.success) {
                            abp.notify.success('@T("删除成功")');
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            }
        });
        
        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'add') {
                window.add();
                // top.layui.dg.popupRight({
                //     id: 'TokenDetail',
                //     title: '@T("新增令牌")',
                //     closeBtn: 1,
                //     area: ['750px'],
                //     success: function (layero, index) {
                //         $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("TokenAdd")?appId=@appId" frameborder="0" class="layadmin-iframe" name="' + index + '"></iframe>');
                //     },
                //     end: function () {
                //         active.reload();
                //     }
                // });
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });
        
        window.add = function() {
            window.addPageIndex = top.layui.dg.popupRight({
                id: 'TokenDetail',
                title: '@T("新增令牌")',
                closeBtn: 1,
                area: ['750px'],
                success: function (layero, index) {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("TokenAdd")?appId=@appId" frameborder="0" class="layadmin-iframe" name="' + index + '"></iframe>');
                },
            });
            window.name = 'TokenDetail';
        }

        window.edit = function(data) {
            window.editPageIndex = top.layui.dg.popupRight({
                id: 'TokenDetail',
                title: '@T("编辑令牌")',
                closeBtn: 1,
                area: ['750px'],
                success: function (layero, index) {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("TokenEdit")?id=' + data.Id + '" frameborder="0" class="layadmin-iframe" name="' + index + '"></iframe>');
                },
            });
            window.name = 'TokenDetail'
        }
    });
</script>

<script type="text/html" id="tool">
    <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="edit">@T("编辑")</a>
    <a class="pear-btn pear-btn-danger pear-btn-xs" lay-event="del">@T("删除")</a>
</script>

<script type="text/html" id="token-toolbar">
    <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        @T("新增")
    </button>
</script>
