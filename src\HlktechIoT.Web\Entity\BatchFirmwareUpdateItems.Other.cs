﻿using HlktechIoT.Data;

using NewLife.Data;

using Pek;

using XCode;

namespace HlktechIoT.Entity {
    public class BatchFirmwareUpdateItemsEx : BatchFirmwareUpdateItems
    {
        /// <summary>
        /// 通过BatchUpgradeFirmwareId搜那批次升级的所有设备
        /// </summary>
        /// <param name="batchUpgradeFirmwareId">批次</param>
        /// <param name="page"></param>
        /// <param name="deviceName">设备</param>
        /// <param name="state"></param>
        /// <returns></returns>
        public static IList<BatchFirmwareUpdateItems> SearchByBatchUpgradeFirmwareId(string batchUpgradeFirmwareId, PageParameter page, string deviceName, int state)
        {
            WhereExpression where = new();

            if (deviceName.IsNotNullOrWhiteSpace())
            {
                where &= (Expression)(@_.DeviceName.Contains(deviceName));
            }

            where &= (Expression)(@_.BatchUpgradeFirmwareId.Equal(batchUpgradeFirmwareId));

            if (state > -1)
            {
                where &= (Expression)(@_.Status.Equal(state));
            }

            return Entity<BatchFirmwareUpdateItems>.FindAll(where, page);
        }
    }
}
