# OAuth 2.0 授权码模式实现说明

## 完整的OAuth流程

你现在已经实现了完整的OAuth 2.0授权码模式，包括：

### 1. 授权端点 (`/api/v1/xiaoyu/authorize`)
- 支持GET和POST方法
- 验证OAuth参数（client_id, redirect_uri等）
- 检查用户登录状态
- 未登录时重定向到OAuth专用登录页
- 已登录时重定向到授权确认页

### 2. OAuth专用登录页 (`/oauth/login`)
- 独立的登录页面，不与常规登录混用
- 保持OAuth参数状态
- 登录成功后跳转到授权确认页

### 3. 授权确认页 (`/oauth/consent`)
- 显示第三方应用请求的权限
- 用户可以选择同意或拒绝授权
- 同意后生成授权码并重定向回第三方应用

### 4. 令牌端点 (`/api/v1/xiaoyu/token`)
- 用授权码换取访问令牌
- 验证客户端身份
- 返回access_token和refresh_token

### 5. 资源端点 (`/api/v1/xiaoyu/resource`)
- 验证访问令牌
- 返回受保护的用户数据

## 第三方应用如何使用

### 步骤1：重定向用户到授权页面
```
GET https://your-iot-platform.com/api/v1/xiaoyu/authorize?
    response_type=code&
    client_id=YOUR_CLIENT_ID&
    redirect_uri=https://your-app.com/callback&
    scope=read&
    state=random_string
```

### 步骤2：用户登录并授权
用户在OAuth专用登录页输入用户名密码，然后在授权确认页选择同意。

### 步骤3：接收授权码
```
GET https://your-app.com/callback?
    code=AUTHORIZATION_CODE&
    state=random_string
```

### 步骤4：用授权码换取访问令牌
```
POST https://your-iot-platform.com/api/v1/xiaoyu/token
Content-Type: application/x-www-form-urlencoded

grant_type=authorization_code&
code=AUTHORIZATION_CODE&
redirect_uri=https://your-app.com/callback&
client_id=YOUR_CLIENT_ID&
client_secret=YOUR_CLIENT_SECRET
```

### 步骤5：使用访问令牌访问API
```
GET https://your-iot-platform.com/api/v1/xiaoyu/resource
Authorization: Bearer ACCESS_TOKEN
```

## 需要完善的部分

1. **数据库表设计**：
   - OAuth应用表（存储client_id, client_secret, app_name等）
   - 授权码表（存储临时授权码）
   - 访问令牌表（存储access_token和refresh_token）

2. **安全性增强**：
   - 使用JWT token
   - 添加PKCE支持
   - 实现refresh_token逻辑

3. **权限范围(scope)处理**：
   - 根据不同scope返回不同的数据
   - 细粒度权限控制

## 测试方法

你可以使用Postman或其他HTTP客户端测试OAuth流程：

1. 首先访问授权端点
2. 完成登录和授权流程  
3. 获取授权码后调用token端点
4. 使用access_token调用resource端点
