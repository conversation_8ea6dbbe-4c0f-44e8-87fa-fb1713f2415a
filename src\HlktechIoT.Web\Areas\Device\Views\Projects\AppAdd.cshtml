@model Project
@{
    Html.AppendTitleParts(T("新增APP管理").Text);
        // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        padding-top: 30px;
        padding-left: 30px;
    }

    .layui-form-item.btn {
        padding-top: 10px;
        padding-left: 115px;
    }

    .label-width span {
        color: #f00;
    }
    .label-width{
        white-space:nowrap;
    }

    .layui-form-label {
        width: 94px;
    }

    .upload {
        cursor: pointer;
        max-width: 120px;
        max-height: 90px;
    }
    .select{
        max-width: 420px !important;
        border: 1px solid #e6e6e6;
    }
</style>
<div class="containers">
    <form class="layui-form">

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("名称")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="Name" placeholder="@T("请输入APP名称")" autocomplete="off" class="layui-input" lay-filter="Versions">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("关联产品")</label>
            <div class="layui-input-inline" style="width: 300px;">
                <div id="demo1" style=" width: 100%;"></div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("Android应用包名")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="AndroidPackName" placeholder="@T("请输入AndroidPackName")" autocomplete="off" class="layui-input" lay-filter="Versions">
            </div>
        </div>



        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("iOS应用包名")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="IosPackName" placeholder="@T("请输入IosPackName")" autocomplete="off" class="layui-input" lay-filter="Versions">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("极光推送Key")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="JPushKey" placeholder="@T("请输入JPushKey")" autocomplete="off" class="layui-input" lay-filter="Versions">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("极光推送Secret")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="JPushSecret" placeholder="@T("请输入JPushSecret")" autocomplete="off" class="layui-input" lay-filter="Versions">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("极光推送Activity")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="URIAction" placeholder="@T("请输入URIAction")" autocomplete="off" class="layui-input" lay-filter="Versions">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("Apns推送环境")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="checkbox" lay-filter="switch" name="ApnsProduction" lay-skin="switch" lay-text="@T("是")|@T("否")">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("极光推送Key")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="IosPackName" placeholder="@T("请输入IosPackName")" autocomplete="off" class="layui-input" lay-filter="Versions" >
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("极光推送Secret")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="IosPackName" placeholder="@T("请输入IosPackName")" autocomplete="off" class="layui-input" lay-filter="Versions" >
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("极光推送Activity")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="IosPackName" placeholder="@T("请输入IosPackName")" autocomplete="off" class="layui-input" lay-filter="Versions" >
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("Apns推送环境")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="IosPackName" placeholder="@T("请输入IosPackName")" autocomplete="off" class="layui-input" lay-filter="Versions" >
            </div>
        </div>

        <div class="layui-form-item btn">
            <input hidden name="Id" value="@Model.Id" />
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" value="@T("保存")" />
        </div>
    </form>
</div>

<script asp-location="Footer">

        // var mult_selectValue = ''; //多选的值

        var demo1 = xmSelect.render({
            el: '#demo1',
            // radio: true,
            name: 'PId',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            // clickClose: true,
            pageRemote: true,  // 分页
            remoteMethod: function (val, cb, show, pageIndex) {  // 远程方法
                var obj = [];

                // 接口数据
                $.post('@Url.Action("SearchProduct")', { keyword:val,Id:'@Model.Id', page: pageIndex }, function (res) {
                    console.log('搜索后的数据',res);
                    if (res.success) {
                        if (res.data != null) {
                            cb(res.data, res.extdata);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });

            },
            on: function (data) {  // 监听选择
                if (data.arr.length > 0) {
                    var a = "";
                    for (var i = 0; i < data.arr.length; i++) {
                        if (i == 0) {
                            
                            a = data.arr[i].value;
                        }
                        else {
                            a += "," + data.arr[i].value;
                        }
                    }
                }
            }
        });
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;
        var layer = layui.layer

        // console.log(window.parent.length); //第一步：先拿到所有父元素
        var parentList = window.parent
        var parentPage = null;

        // 第二步：拿到对应层
        for (let i = 0; i < parentList.length; i++) { 
            if (parentList[i].name === 'AppAdd') { //这里的name自己去对应层DIY
                parentPage = parentList[i]
                break;
            }
        }
        // 第三步：操作对应层
        var parent_window = parentPage.window  //获取父层的window层
        var parent_layer = parentPage.layer //获取父层的layer
        var parent_notify = parentPage.layui.abp.notify //获取父层的layui.notify --消息通知
        var parent_layui  = parentPage.layui  //获取父层的layui
        var currentPageCloseIndex = parent_window.addPageIndex //当前层的关闭index下标

        form.on('submit(Submit)',  (data)=> {
            if (data.field.Name.length == 0) {
                abp.notify.warn("@T("名称不能为空")");
                return;
            }
            if (data.field.PId == '' || data.field.PId == null || data.field.PId == undefined ||  data.field.PId  == 'undefined ') {
                abp.notify.warn("@T("关联产品不能为空")");
                return;
            }

            var waitIndex = layer.load(2);

            abp.ajax({
                url: "@Url.Action("AppAdd")",
                contentType : "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done( (data)=> {
                 if (!data.success) {
                    console.log(data);
                    abp.notify.error(data.msg);
                    return false;
                }
                parent_notify.success(data.msg)
                parentPage.active.reload()
                dg.reload('tables'); //刷新首页
                // 关闭当前编辑页面
                parent.layer.close(currentPageCloseIndex);
            }).fail(function (jqXHR) {
                layer.msg(jqXHR.message, { icon: 5 }); //新增失败且不关闭当前页面
            }).always(function () {
                layer.close(waitIndex);
            });
            layer.close(waitIndex);
            return false;


        });
    });
</Script>