菲凡物联网平台v1.6更新：
1. 完善设备通过MQTT接入IoTServer，修正数据采集与下发控制的一些BUG，提供完整文档和示例
2. IoT标准库更新TSL物模型规范，在阿里云物模型基础上，增加点位地址、解析转换等配置
3. IoTWeb支持公开产品物模型，支持从另一个IoTWeb订阅，支持TSL导入导出
4. IoTClient采集与控制，默认使用物模型对点位数据进行解析和逆解析
5. IoTWeb设备属性支持修改属性值，执行SetProperty服务调用下发
6. IoTClient支持非点位数据写入的服务调用
7. 新增IoTApp，作为第五大板块，提供SAAS用户平台、数据大屏、手机端的物联网应用框架
8. 进一步完善驱动集合，新增ModbusASCII、西门子PLC、三菱FxLinks、施耐德PLC等驱动
9. IoTClient支持扫描驱动并上报驱动信息和默认物模型
10. 支持应用钩子，在登录、心跳、属性上报、事件上报等关键节点实时调用外部接口
11. 增加设备地图，在地图上展示设备
12. 增加设备分段数据，实时计算每点位每天的最小值、最大值、第一值、最后值，便于累计差值计算

v1.7路线规划：
1. 进一步完善IoTApp，为多租户SAAS平台和移动端系统提供标准的前后端分离架构
2. 为HTTP接入和MQTT接入提供插件接口，支持外部插件把非标接入数据调整为内部标准数据