﻿using DG.Web.Framework;

using DH;
using DH.Core.Infrastructure;
using DH.Entity;

using HlktechIoT.Data;
using HlktechIoT.Data.Enums;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Caching;
using NewLife.Data;
using NewLife.IoT.ThingSpecification;
using NewLife.Log;
using NewLife.Serialization;
using Pek;
using Pek.Helpers;
using Pek.Models;
using Pek.MVC.Filters;
using Pek.Webs;

using System.ComponentModel;
using System.Text;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Device.Controllers;

/// <summary>产品管理</summary>
[DisplayName("产品管理")]
[Description("用户项目下的产品管理")]
[DeviceArea]
[DHMenu(90, ParentMenuName = "DeviceManager", CurrentMenuUrl = "~/{area}/Products", CurrentMenuName = "Products", LastUpdate = "20240124")]
public class ProductsController : BaseAdminControllerX {

    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 90;

    /// <summary>
    /// 产品管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("产品管理")]
    [HttpGet]
    public IActionResult Index(String PId,String key)
    {
        ViewBag.PId = PId;
        ViewBag.Key = key;

        return View();
    }

    /// <summary>
    /// 根据条件搜索
    /// </summary>
    /// <param name="key">项目名称/项目ID等</param>
    /// <param name="PId">项目编号</param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("产品查询")]
    public IActionResult GetList(String key, String PId, Int32 page = 1, Int32 limit = 10)
    {
        if (!key.IsNullOrWhiteSpace()) key = key.Trim();
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = Product._.Id,
            Desc = true,
        };

        var UId = 0;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            UId = ManageProvider.User?.ID ?? -1;
        }

        var list = Product.Search(PId, UId, key, pages);

        var data = list.Select(x =>
        {
            var BindType = string.Empty;
            switch (x.BindType)
            {
                case 0:
                    BindType = GetResource("独占式");
                    break;

                case 1:
                    BindType = GetResource("分享式");
                    break;

                case 2:
                    BindType = GetResource("抢占式");
                    break;
            }

            x.Fix();

            return new { x.Id, x.ProjectKey, x.Name, x.CreateUser, x.CreateTime, x.Code, Kind = GetResource(x.Kind.ToString()), NetType = GetResource(x.NetType), x.DataFormat, x.DeviceCount, ProductModuleCode = x.ProductModule?.Code, BindType, x.ProjectName,x.EnableLimitTime };
        });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 搜索项目
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [HttpPost]
    [DisplayName("搜索项目")]
    public IActionResult SearchProject(String keyword, Int32 PId, Int32 page)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = Project._.Id,
            Desc = true,
        };

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (modelRole.IsAdmin)
        {
            res.data = Project.Search(0, keyword, pages).Select(e =>
            {
                var selected = false;
                if (e.Id == PId)
                {
                    selected = true;
                }
                return new Xmselect<Int32>
                {
                    name = e.Name,
                    value = e.Id,
                    selected = selected
                };
            });
        }
        else
        {
            res.data = Project.Search(ManageProvider.User?.ID ?? -1, keyword, null).Select(e =>
            {
                var selected = false;
                if (e.Id == PId)
                {
                    selected = true;
                }

                return new Xmselect<Int32>
                {
                    name = e.Name,
                    value = e.Id,
                    selected = selected
                };
            });
        }

        var model = Project.FindById(PId);
        if (model != null)
        {
            res.extdata = new { pages.PageCount, data = new List<NameValueL<Int32?>>() { new() { name = model.Name, value = model.Id } } };
        }
        else
        {
            res.extdata = new { pages.PageCount, data = new List<NameValueL<Int32?>>() };
        }

        //res.extdata = pages.PageCount;
        res.success = true;
        return Json(res);
    }

    /// <summary>
    /// 增加产品
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("增加产品")]
    public IActionResult Add()
    {
        return View();
    }

    /// <summary>
    /// 增加产品
    /// </summary>
    /// <param name="Name">名称</param>
    /// <param name="Kind">节点类型。直连设备/网关设备/子设备</param>
    /// <param name="DataFormat">数据格式。Json</param>
    /// <param name="NetType">连网方式。MQTT/WiFi/蜂窝Cellular（2G/3G/4G/5G）/以太网/LoRaWAN/其它</param>
    /// <param name="BindType">绑定类型，0为独占式，1为分享式，2为抢占式</param>
    /// <param name="ProjectId">项目Id</param>
    /// <param name="ProductModuleId">产品型号Id</param>
    /// <param name="FileUrl">图片路径</param>
    /// <param name="EnableLimitTime"> 是否启用时间限制</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("增加产品")]
    [HttpPost]
    public IActionResult Add(String Name, ProductKinds Kind, String DataFormat, String NetType, Int32 BindType, Int32 ProjectId, Int32 ProductModuleId, String FileUrl,String EnableLimitTime)
    {
        var result = new DResult();

        if (Name.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("产品名称不能为空");
            return Json(result);
        }

        var modelProject = Project.FindById(ProjectId);
        if (modelProject == null)
        {
            result.msg = GetResource("项目不存在");
            return Json(result);
        }

        var cacheProvider = EngineContext.Current.Resolve<ICacheProvider>();

        var model = new Product();
        model.Name = Name;
        model.Code = model.CreateProductKey(cacheProvider);
        model.Secret = Randoms.RandomString(16);
        model.Enable = true;
        model.Kind = Kind;
        model.DataFormat = DataFormat;
        model.ProtocolType = "-1";
        model.NetType = NetType;
        model.BindType = BindType;
        model.ProjectId = ProjectId;
        model.ProjectKey = modelProject.ProjectKey;
        model.ProductModuleId = ProductModuleId;

        var result1 = Product.HashDbName(model.Code);
        model.DBName = result1.tableCount;
        model.ConnName = result1.connCount; 
        model.IconPath = FileUrl;
        model.EnableLimitTime = EnableLimitTime.SafeString() == "on";
        model.Insert();

        result.success = true;
        result.msg = GetResource("成功新增产品");

        return Json(result);
    }

    /// <summary>
    /// 编辑产品
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("编辑产品")]
    public IActionResult Edit(Int32 Id)
    {
        var model = Product.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("产品不存在"));
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserId != ManageProvider.User?.ID)
            {

                return Content(GetResource("您没有权限操作"));
            }
        }

        var modelProductModule = ProductModule.FindById(model.ProductModuleId);
        ViewBag.ModuleList = new List<NameValueL<Int32?>>() { new() { name = (string.IsNullOrWhiteSpace(modelProductModule?.Code) && string.IsNullOrWhiteSpace(modelProductModule?.Remark)) ? null : modelProductModule?.Code + $"({modelProductModule?.Remark})", value = modelProductModule?.Id } }.ToJson();

        var modelProject = Project.FindById(model.ProjectId);
        ViewBag.ProjectList = new List<NameValueL<Int32?>>() { new() { name = modelProject?.Name, value = modelProject?.Id } }.ToJson();

        return View(model);
    }

    /// <summary>
    /// 编辑产品
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("编辑产品")]
    [HttpPost]
    public IActionResult Edit(Int32 Id, String Name, Int32 BindType, Int32 ProjectId, Int32 ProductModuleId, String FileUrl,String EnableLimitTime)
    {
        var result = new DResult();

        if (Name.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("产品名称不能为空");
            return Json(result);
        }

        var model = Product.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("产品不存在");
            return Json(result);
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserId != ManageProvider.User?.ID)
            {
                result.msg = GetResource("您没有权限操作");
                return Json(result);
            }
        }

        var modelProject = Project.FindById(ProjectId);
        if (modelProject == null)
        {
            result.msg = GetResource("项目不存在");
            return Json(result);
        }

        model.Name = Name;
        model.BindType = BindType;
        model.ProjectId = ProjectId;
        model.ProjectKey = modelProject.ProjectKey;
        model.ProductModuleId = ProductModuleId; 
        model.IconPath = FileUrl;
        model.EnableLimitTime = EnableLimitTime.SafeString() == "on";
        model.Update();

        result.success = true;
        result.msg = GetResource("成功编辑产品");

        return Json(result);
    }

    /// <summary>
    /// 删除产品
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除产品")]
    [HttpPost]
    public IActionResult Delete(Int32 Id)
    {
        var res = new DResult();

        var model = Product.FindById(Id);
        if (model == null)
        {
            res.msg = GetResource("产品不存在");
            return Json(res);
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserId != ManageProvider.User?.ID)
            {
                res.msg = GetResource("您没有权限操作");
                return Json(res);
            }
        }

        var dlist = Data.Device.FindCountByProductId([Id]);
        if (dlist > 0)
        {
            res.msg = GetResource("产品下存在子设备，不允许删除");
            return Json(res);
        }

        model.Delete();

        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 搜索产品型号
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [HttpPost]
    [DisplayName("搜索产品型号")]
    public IActionResult SearchProductModule(String keyword, Int32 page)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = ProductModule._.Id,
            Desc = true,
        };

        res.data = ProductModule.Search(keyword, 0, pages).Select(e =>
        {
            var selected = false;

            return new Xmselect<Int32>
            {
                name = e.Code + $"({e.Remark})",
                value = e.Id,
                selected = selected
            };
        });

        res.extdata = pages.PageCount;
        res.success = true;
        return Json(res);
    }

    /// <summary>
    /// 搜索产品
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [HttpPost]
    [DisplayName("搜索产品")]
    public IActionResult SearchProduct(String keyword, Int32 page)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = Product._.Id,
            Desc = true
        };

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            res.data = Product.Search(keyword, ManageProvider.User?.ID ?? -1, pages).Select(e =>
            {
                var selected = false;

                return new Xmselect<Int32>
                {
                    name = e.Name + $"({e.Code})",
                    value = e.Id,
                    selected = selected
                };
            });
        }
        else
        {
            res.data = Product.Search(keyword, 0, pages).Select(e =>
            {
                var selected = false;

                return new Xmselect<Int32>
                {
                    name = e.Name + $"({e.Code})",
                    value = e.Id,
                    selected = selected
                };
            });
        }

        res.extdata = pages.PageCount;
        res.success = true;
        return Json(res);
    }

    /// <summary>功能定义</summary>
    /// <param name="Id">设备Id</param>
    /// <returns></returns>
    [DisplayName("功能定义")]
    [EntityAuthorize((PermissionFlags)16)]
    public IActionResult ProductFunction(Int32 Id)
    {
        var model = Product.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("产品不存在"));
        }

        var UId = 0;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            UId = ManageProvider.User?.ID ?? -1;

            if (UId <= 0)
            {
                return Content(GetResource("您没有操作权限"));
            }

            var listU = Product.FindAllByCreateUserId(UId);
            if (!listU.Any(e => e.Id == Id))
            {
                return Content(GetResource("您没有操作权限"));
            }
        }

        return View(model);
    }

    /// <summary>功能定义</summary>
    /// <param name="Id">设备Id</param>
    /// <param name="key">搜索关键词</param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [DisplayName("功能定义")]
    [EntityAuthorize((PermissionFlags)16)]
    public IActionResult ProductFunctionList(Int32 Id, String key, Int32 page = 1, Int32 limit = 10)
    {
        var result = new DResult();

        var model = Product.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("产品不存在");
            return Json(result);
        }

        var UId = 0;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            UId = ManageProvider.User?.ID ?? -1;

            if (UId <= 0)
            {
                result.msg = GetResource("您没有操作权限");
                return Json(result);
            }

            var listU = Product.FindAllByCreateUserId(UId);
            if (!listU.Any(e => e.Id == Id))
            {
                result.msg = GetResource("您没有操作权限");
                return Json(result);
            }
        }

        if (!key.IsNullOrWhiteSpace()) key = key.Trim();
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = Data.ProductFunction._.Id,
            Desc = true
        };

        IList<ProductFunction> list;

        list = Data.ProductFunction.Search(Id, key, pages);

        var data = list.Select(x => new { x.Id, Name = x.Name, ProductName = x.ProductName, Kind = GetResource(x.Kind.ToString()), Identifier = x.Identifier, x.Enable, AccessMode = GetResource(x.AccessMode.ToString()), DataType = x.DataType, Length = x.Length, Min = x.Min, Max = x.Max, Step = x.Step, MaxStep = x.MaxStep, Unit = x.Unit, UnitName = x.UnitName, Address = x.Address, WriteRule = x.WriteRule, ScalingFactor = x.ScalingFactor, ConstantFactor = x.ConstantFactor, Mapping = x.Mapping, CallMethod = x.CallMethod, EventType = x.EventType, x.DefaultValue });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>修改功能定义状态</summary>
    /// <param name="Id">功能编号</param>
    /// <param name="Status">状态</param>
    /// <returns></returns>
    [DisplayName("功能定义")]
    [HttpPost]
    [EntityAuthorize((PermissionFlags)16)]
    public IActionResult ModifyPFState(Int32 Id, Boolean Status)
    {
        var result = new DResult();

        var UId = 0;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            UId = ManageProvider.User?.ID ?? -1;

            if (UId <= 0)
            {
                result.msg = GetResource("您没有操作权限");
                return Json(result);
            }

            var listU = Product.FindAllByCreateUserId(UId);
            if (!listU.Any(e => e.Id == Id))
            {
                result.msg = GetResource("您没有操作权限");
                return Json(result);
            }
        }

        var model = Data.ProductFunction.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("状态调整出错");
            return Json(result);
        }

        model.Enable = Status;
        model.Update();

        result.success = true;
        result.msg = GetResource("状态调整成功");

        return Json(result);
    }

    /// <summary>
    /// 删除功能定义
    /// </summary>
    /// <param name="Id">功能定义编号</param>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)16)]
    [DisplayName("功能定义")]
    [HttpPost]
    public IActionResult DeletePF(Int32 Id)
    {
        var res = new DResult();

        var model = Data.ProductFunction.FindById(Id);
        if (model == null)
        {
            res.msg = GetResource("功能定义不存在");
            return Json(res);
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserId != ManageProvider.User?.ID)
            {
                res.msg = GetResource("您没有权限操作");
                return Json(res);
            }
        }

        var dlist = Data.Device.FindAllByProductId(Id).Select(e => e.Id);
        var list = DeviceProperty.FindAllByDeviceIds(dlist);
        list.Delete();

        model.Delete();

        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 增加功能定义
    /// </summary>
    /// <param name="Id">产品编号</param>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)16)]
    [DisplayName("功能定义")]
    public IActionResult AddPF(Int32 Id)
    {
        var model = Product.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("产品不存在"));
        }

        var UId = 0;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            UId = ManageProvider.User?.ID ?? -1;

            if (UId <= 0)
            {
                return Content(GetResource("您没有操作权限"));
            }

            var listU = Product.FindAllByCreateUserId(UId);
            if (!listU.Any(e => e.Id == Id))
            {
                return Content(GetResource("您没有操作权限"));
            }
        }

        return View(model);
    }

    /// <summary>
    /// 增加功能定义
    /// </summary>
    /// <param name="Id">产品编号</param>
    /// <param name="Name">名称</param>
    /// <param name="Kind">种类。属性/事件/服务</param>
    /// <param name="Identifier">标识。功能唯一标识，如Temperature</param>
    /// <param name="AccessMode">访问模式。只读r/读写rw/只写w</param>
    /// <param name="DataType">类型。数据类型，基础类型或子设备产品。选择子设备产品时，本产品的主设备将解析生成子设备</param>
    /// <param name="Length">长度。字节数或字符串长度，Modbus寄存器一般占2个字节</param>
    /// <param name="Min">最小值。如-40</param>
    /// <param name="Max">最大值。如120</param>
    /// <param name="Step">步长。最小间隔，数据精度</param>
    /// <param name="MaxStep">最大间隔。相邻数据超过该值时抛弃，剔除突发异常数据</param>
    /// <param name="Unit">单位。数据单位，例如°C</param>
    /// <param name="UnitName">单位名称。例如摄氏度</param>
    /// <param name="Address">点位地址。常规地址6，Modbus地址 4x0023，位域地址D12.05，虚拟点位地址#</param>
    /// <param name="WriteRule">反解析规则。用户属性值转设备值的计算公式，或者脚本</param>
    /// <param name="ScalingFactor">缩放因子。不能是0，默认1，n*scaling+constant</param>
    /// <param name="ConstantFactor">常量因子。默认0，n*scaling+constant</param>
    /// <param name="Mapping">枚举映射。布尔型和数字型特有，例如“0=关,1=开”，又如“1=东,2=南,3=西,4=北”</param>
    /// <param name="CallMethod">调用方法。EventPropertyMethod.Post/ServicePropertyMethod.Get/ServicePropertyMethod.Set</param>
    /// <param name="EventType">事件类型,info/alert/error</param>
    /// <param name="DefaultValue">默认值</param>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)16)]
    [DisplayName("功能定义")]
    [HttpPost]
    public IActionResult AddPF(Int32 Id, String Name, Int32 Kind, String Identifier, Int32 AccessMode, String DataType, Int32 Length, Double Min, Double Max, Double Step, Double MaxStep, String Unit, String UnitName, String Address, String WriteRule, Double ScalingFactor, Double ConstantFactor, String Mapping, String CallMethod, String EventType, String DefaultValue)
    {
        var result = new DResult();

        if (Name.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("功能名称不能为空");
            return Json(result);
        }

        if (Id <= 0)
        {
            result.msg = GetResource("请选择产品");
            return Json(result);
        }

        if (Identifier.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("标识不能为空");
            return Json(result);
        }

        var model = Data.ProductFunction.FindByProductIdAndIdentifier(Id, Identifier);
        if (model != null)
        {
            result.msg = GetResource("同一产品下标识不能重复");
            return Json(result);
        }

        model = new ProductFunction();
        model.ProductId = Id;
        model.Name = Name;
        model.Kind = (FunctionKinds)Kind;
        model.Identifier = Identifier;
        model.Enable = true;
        model.AccessMode = (AccessModes)AccessMode;
        model.DataType = DataType;
        model.Length = Length;
        model.Min = Min;
        model.Max = Max;
        model.Step = Step;
        model.MaxStep = MaxStep;
        model.Unit = Unit;
        model.UnitName = UnitName;
        model.Address = Address;
        model.WriteRule = WriteRule;
        model.ScalingFactor = ScalingFactor;
        model.ConstantFactor = ConstantFactor;
        model.Mapping = Mapping;
        model.CallMethod = CallMethod;
        model.EventType = EventType;
        model.DefaultValue = DefaultValue;

        model.Insert();

        if (model.Product != null)
        {
            var modelProduct = model.Product;

            modelProduct.HasQueue = Data.ProductFunction.FindAllByProductId(model.ProductId).Any(e => !e.DefaultValue.IsNullOrWhiteSpace());
            modelProduct.Update();
        }

        result.success = true;
        result.msg = GetResource("成功新增功能定义");

        return Json(result);
    }

    /// <summary>
    /// 编辑功能定义
    /// </summary>
    /// <param name="Id">功能定义编号</param>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)16)]
    [DisplayName("功能定义")]
    public IActionResult EditPF(Int32 Id)
    {
        var model = Data.ProductFunction.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("功能定义不存在"));
        }

        var UId = 0;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            UId = ManageProvider.User?.ID ?? -1;

            if (UId <= 0)
            {
                return Content(GetResource("您没有操作权限"));
            }

            var listU = Product.FindAllByCreateUserId(UId);
            if (!listU.Any(e => e.Id == Id))
            {
                return Content(GetResource("您没有操作权限"));
            }
        }

        return View(model);
    }

    /// <summary>
    /// 编辑功能定义
    /// </summary>
    /// <param name="Id">功能定义编号</param>
    /// <param name="Name">名称</param>
    /// <param name="Kind">种类。属性/事件/服务</param>
    /// <param name="Identifier">标识。功能唯一标识，如Temperature</param>
    /// <param name="AccessMode">访问模式。只读r/读写rw/只写w</param>
    /// <param name="DataType">类型。数据类型，基础类型或子设备产品。选择子设备产品时，本产品的主设备将解析生成子设备</param>
    /// <param name="Length">长度。字节数或字符串长度，Modbus寄存器一般占2个字节</param>
    /// <param name="Min">最小值。如-40</param>
    /// <param name="Max">最大值。如120</param>
    /// <param name="Step">步长。最小间隔，数据精度</param>
    /// <param name="MaxStep">最大间隔。相邻数据超过该值时抛弃，剔除突发异常数据</param>
    /// <param name="Unit">单位。数据单位，例如°C</param>
    /// <param name="UnitName">单位名称。例如摄氏度</param>
    /// <param name="Address">点位地址。常规地址6，Modbus地址 4x0023，位域地址D12.05，虚拟点位地址#</param>
    /// <param name="WriteRule">反解析规则。用户属性值转设备值的计算公式，或者脚本</param>
    /// <param name="ScalingFactor">缩放因子。不能是0，默认1，n*scaling+constant</param>
    /// <param name="ConstantFactor">常量因子。默认0，n*scaling+constant</param>
    /// <param name="Mapping">枚举映射。布尔型和数字型特有，例如“0=关,1=开”，又如“1=东,2=南,3=西,4=北”</param>
    /// <param name="CallMethod">调用方法。EventPropertyMethod.Post/ServicePropertyMethod.Get/ServicePropertyMethod.Set</param>
    /// <param name="EventType">事件类型,info/alert/error</param>
    /// <param name="DefaultValue">默认值</param>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)16)]
    [DisplayName("功能定义")]
    [HttpPost]
    public IActionResult EditPF(Int32 Id, String Name, Int32 Kind, String Identifier, Int32 AccessMode, String DataType, Int32 Length, Double Min, Double Max, Double Step, Double MaxStep, String Unit, String UnitName, String Address, String WriteRule, Double ScalingFactor, Double ConstantFactor, String Mapping, String CallMethod, String EventType, String DefaultValue)
    {
        var result = new DResult();

        if (Name.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("功能名称不能为空");
            return Json(result);
        }

        if (Identifier.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("标识不能为空");
            return Json(result);
        }

        var model = Data.ProductFunction.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("功能定义不存在");
            return Json(result);
        }

        var m = Data.ProductFunction.FindByProductIdAndIdentifier(model.ProductId, Identifier);
        if (m != null && m.Id != Id)
        {
            result.msg = GetResource("同一产品下标识不能重复");
            return Json(result);
        }

        model.Name = Name;
        model.Kind = (FunctionKinds)Kind;
        model.Identifier = Identifier;
        model.AccessMode = (AccessModes)AccessMode;
        model.DataType = DataType;
        model.Length = Length;
        model.Min = Min;
        model.Max = Max;
        model.Step = Step;
        model.MaxStep = MaxStep;
        model.Unit = Unit;
        model.UnitName = UnitName;
        model.Address = Address;
        model.WriteRule = WriteRule;
        model.ScalingFactor = ScalingFactor;
        model.ConstantFactor = ConstantFactor;
        model.Mapping = Mapping;
        model.CallMethod = CallMethod;
        model.EventType = EventType;
        model.DefaultValue = DefaultValue;

        model.Update();

        if (model.Product != null)
        {
            var modelProduct = model.Product;

            modelProduct.HasQueue = Data.ProductFunction.FindAllByProductId(model.ProductId).Any(e => !e.DefaultValue.IsNullOrWhiteSpace());
            modelProduct.Update();
        }

        result.success = true;
        result.msg = GetResource("成功编辑功能定义");

        return Json(result);
    }

    /// <summary>
    /// 发布功能定义
    /// </summary>
    /// <param name="Id">产品编号</param>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)16)]
    [DisplayName("功能定义")]
    [HttpPost]
    public IActionResult PublishBatch(Int32 Id)
    {
        var result = new DResult();

        var pEntity = Product.FindById(Id);
        if (pEntity == null)
        {
            result.msg = GetResource("产品不存在");
            return Json(result);
        }

        var dlist = Data.Device.FindAllByProductId(Id);
        var pflist = Data.ProductFunction.FindAllByProductId(Id);

        var list = new List<DeviceProperty>();

        foreach (var device in dlist)
        {
            var hashSet = new HashSet<String>();

            foreach (var pf in pflist)
            {
                hashSet.Add(pf.Identifier);

                switch (pf.Kind)
                {
                    case FunctionKinds.Property:
                        pf.PublishProperty(device);
                        break;
                    case FunctionKinds.Service:
                        pf.PublishService(device);
                        break;
                    case FunctionKinds.Event:
                    default:
                        break;
                }
                //pf.IsPublish = true;
                pf.Save();
            }

            var list1 = DeviceProperty.FindAllByDeviceIdAndNotNames(device.Id, hashSet);
            if (list1 != null && list1.Any())
            {
                list.AddRange(list1);
            }

            // 判断是否强制校验，强制校验不支持设备单独定义特殊设备属性，需要强制关闭
            if (pEntity.VerifyModel)
            {
                foreach (var item in device.Properties())
                {
                    if (pflist.Any(e => e.Identifier.Equals(item.Name, StringComparison.OrdinalIgnoreCase) && e.Kind == FunctionKinds.Property)) continue;

                    item.Enable = false;
                    item.Save();
                }

                foreach (var item in device.Services())
                {
                    if (pflist.Any(e => e.Identifier.Equals(item.Name, StringComparison.OrdinalIgnoreCase) && e.Kind == FunctionKinds.Service)) continue;

                    item.Enable = false;
                    item.Save();
                }
            }

            //// 发布后，更新设备信息，让客户端重新拉取
            //device.UpdateTime = DateTime.Now;
            //device.Update();
        }

        list.Delete();

        pEntity.HasQueue = Data.ProductFunction.FindAllByProductId(Id).Any(e => !e.DefaultValue.IsNullOrWhiteSpace());
        pEntity.Update();

        result.success = true;
        result.msg = GetResource("功能定义已成功发布给设备属性");

        return Json(result);
    }

    /// <summary>
    /// 功能定义TSL
    /// </summary>
    /// <param name="Id">产品编号</param>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)16)]
    [DisplayName("功能定义")]
    public IActionResult TSL(Int32 Id)
    {
        var model = Product.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("产品不存在"));
        }

        var UId = 0;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            UId = ManageProvider.User?.ID ?? -1;

            if (UId <= 0)
            {
                return Content(GetResource("您没有操作权限"));
            }

            var listU = Product.FindAllByCreateUserId(UId);
            if (!listU.Any(e => e.Id == Id))
            {
                return Content(GetResource("您没有操作权限"));
            }
        }

        var spec = model.GetSpecification();
        ViewBag.TSL = spec.ToJson();

        return View(model);
    }

    /// <summary>
    /// 功能定义TSL
    /// </summary>
    /// <param name="Id">产品编号</param>
    /// <param name="Tsl">Tsl内容</param>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)16)]
    [DisplayName("功能定义")]
    [HttpPost]
    public IActionResult TSL(Int32 Id, String Tsl)
    {
        var result = new DResult();

        try
        {
            if (Tsl.IsNullOrEmpty())
            {
                result.msg = GetResource("Tsl内容不能为空");
                return Json(result);
            }

            var spec = new ThingSpec();
            spec.FromJson(Tsl);

            var product = Product.FindById(Id);
            var list = Data.ProductFunction.FindAllByProductId(Id);

            // 属性
            var plist = list.Where(e => e.Kind == FunctionKinds.Property).ToList();
            foreach (var item in spec.Properties!)
            {
                // 跳过信息不全的点位
                if (item.Id.IsNullOrEmpty()) continue;

                var pEntity = plist.FirstOrDefault(e => e.Identifier == item.Id) ?? new ProductFunction();

                if (pEntity.Id == 0) plist.Add(pEntity);

                pEntity.FromPropertySpec(item);
                pEntity.ProductId = Id;
                // 每次导入完成后会重置发布状态
                //pEntity.IsPublish = false;
                pEntity.Enable = true;

                pEntity.Save();
            }

            // 补充扩展属性
            foreach (var item in spec.ExtendedProperties!)
            {
                var pEntity = plist.FirstOrDefault(e => e.Identifier == item.Id);

                if (pEntity == null) continue;

                pEntity.FromExtendedProperty(item);
                pEntity.ProductId = Id;
                // 每次导入完成后会重置发布状态
                //pEntity.IsPublish = false;
                pEntity.Enable = true;
                pEntity.Save();
            }

            if (product.VerifyModel)
            {
                foreach (var item in plist)
                {
                    if (!spec.Properties.Any(e => e.Id == item.Identifier))
                    {
                        item.Enable = false;
                        item.Update();
                    }
                }
            }

            // 事件
            var elist = list.Where(e => e.Kind == FunctionKinds.Event).ToList();
            foreach (var item in spec.Events!)
            {
                var eEntity = elist.FirstOrDefault(e => e.Identifier == item.Id) ?? elist.FirstOrDefault(e => e.Name == item.Name) ?? new ProductFunction();

                eEntity.FromEventSpec(item);
                eEntity.ProductId = Id;

                eEntity.Enable = true;
                eEntity.Save();
            }
            if (product.VerifyModel)
            {
                foreach (var item in elist)
                {
                    if (!spec.Events.Any(e => e.Id == item.Identifier))
                    {
                        item.Enable = false;
                        item.Update();
                    }
                }
            }

            // 服务
            var slist = list.Where(e => e.Kind == FunctionKinds.Service).ToList();
            foreach (var item in spec.Services!)
            {
                var sEntity = elist.FirstOrDefault(e => e.Identifier == item.Id) ?? slist.FirstOrDefault(e => e.Name == item.Name) ?? new ProductFunction();

                sEntity.FromServiceSpec(item);
                sEntity.ProductId = Id;
                // 每次导入完成后会重置发布状态
                //sEntity.IsPublish = false;
                sEntity.Enable = true;
                sEntity.Save();
            }
            if (product.VerifyModel)
            {
                foreach (var item in slist)
                {
                    if (!spec.Services.Any(e => e.Id == item.Identifier))
                    {
                        item.Enable = false;
                        item.Update();
                    }
                }
            }
        }
        catch (Exception ex)
        {
            XTrace.WriteLine(ex + "");

            result.msg = GetResource("保存失败");
            return Json(result);
        }

        result.success = true;
        result.msg = GetResource("保存成功");

        return Json(result);
    }

    /// <summary>
    /// 上传图标
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("上传图标")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult UploadFiles(IFormFile file)
    {
        var res = new DResult();
        if (file == null)
        {
            XTrace.WriteLine("获取到的文件为空");

            res.msg = GetResource("图标上传有误");
            return Json(res);
        }

        var OrignfileName = file.FileName;

        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(OrignfileName)}";
        var filepath = DHSetting.Current.UploadPath.CombinePath($"Product/{filename}");

        var saveFileName = filepath.GetFullPath();
        var f = saveFileName.AsFile();
        if (f.Exists)
        {
            f.Delete();
        }
        saveFileName.EnsureDirectory();
        file.SaveAs(saveFileName);

        res.msg = GetResource("文件上传成功");
        res.success = true;
        res.data = new { FileUrl = filepath.Replace("\\", "/") };
        return Json(res);
    }

    /// <summary>
    /// 导出
    /// </summary>
    /// <returns></returns>
    [DisplayName("导出")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult ExportTSL(int Id)
    {

        var model = Product.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("产品不存在"));
        }

        var UId = 0;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            UId = ManageProvider.User?.ID ?? -1;

            if (UId <= 0)
            {
                return Content(GetResource("您没有操作权限"));
            }

            var listU = Product.FindAllByCreateUserId(UId);
            if (!listU.Any(e => e.Id == Id))
            {
                return Content(GetResource("您没有操作权限"));
            }
        }

        var spec = model.GetSpecification();
        var ss = spec.ToJson();

        string fileName = $"{GetResource("功能定义TSL")}_{Id}_{DateTime.Now:yyyyMMddhhmm}.txt";

        // 创建文件内容
        string fileContent = $"{ss}";
        byte[] fileBytes = System.Text.Encoding.UTF8.GetBytes(fileContent);

        return File(fileBytes, "text/plain", fileName);
    }

    /// <summary>
    /// 导入TSL
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入TSL")]
    [EntityAuthorize((PermissionFlags)512)]
    public IActionResult ImportTSL(Int32 Id)
    {
        var model = Product.FindById(Id);
        var UId = 0;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            UId = ManageProvider.User?.ID ?? -1;

            if (UId <= 0)
            {
                return Content(GetResource("您没有操作权限"));
            }

            var listU = Product.FindAllByCreateUserId(UId);
            if (!listU.Any(e => e.Id == Id))
            {
                return Content(GetResource("您没有操作权限"));
            }
        }
        return View(model);
    }

    /// <summary>
    /// 导入TSL
    /// </summary>
    /// <param name="file"></param>
    /// <param name="Id"></param>
    /// <returns></returns>
    [DisplayName("导入TSL")]
    [HttpPost]
    [EntityAuthorize((PermissionFlags)512)]
    [AntiDuplicateRequest(Interval = 60, AutoUnLock = true)]
    public IActionResult UpLoadFilesTSL(IFormFile file, Int32 Id)
    {
        var result = new DResult();
        if (file == null)
        {
            result.msg = GetResource("文件不能为空");
            return Json(result);
        }
        var ext = Path.GetExtension(file.FileName).Trim('.');
        if (ext != "txt")
        {
            result.msg = GetResource("不能识别的文件格式");
            return Json(result);
        }
        if (file.Length == 0)
        {
            result.msg = GetResource("文件体积过小");
            return Json(result);
        }

        var import = file.OpenReadStream();
        StreamReader sr = new StreamReader(import, Encoding.Default);
        String line = sr.ReadToEnd();
        if (line != null)
        {
            try
            {
                var Tsl = line.ToString();
                if (Tsl.IsNullOrEmpty())
                {
                    result.msg = GetResource("Tsl内容不能为空");
                    return Json(result);
                }

                var spec = new ThingSpec();
                spec.FromJson(Tsl);

                var product = Product.FindById(Id);
                var list = Data.ProductFunction.FindAllByProductId(Id);

                // 属性
                var plist = list.Where(e => e.Kind == FunctionKinds.Property).ToList();
                foreach (var item in spec.Properties!)
                {
                    // 跳过信息不全的点位
                    if (item.Id.IsNullOrEmpty()) continue;

                    var pEntity = plist.FirstOrDefault(e => e.Identifier == item.Id) ?? new ProductFunction();

                    if (pEntity.Id == 0) plist.Add(pEntity);

                    pEntity.FromPropertySpec(item);
                    pEntity.ProductId = Id;
                    // 每次导入完成后会重置发布状态
                    //pEntity.IsPublish = false;
                    pEntity.Enable = true;

                    pEntity.Save();
                }

                // 补充扩展属性
                foreach (var item in spec.ExtendedProperties!)
                {
                    var pEntity = plist.FirstOrDefault(e => e.Identifier == item.Id);

                    if (pEntity == null) continue;

                    pEntity.FromExtendedProperty(item);
                    pEntity.ProductId = Id;
                    // 每次导入完成后会重置发布状态
                    //pEntity.IsPublish = false;
                    pEntity.Enable = true;
                    pEntity.Save();
                }

                if (product.VerifyModel)
                {
                    foreach (var item in plist)
                    {
                        if (!spec.Properties.Any(e => e.Id == item.Identifier))
                        {
                            item.Enable = false;
                            item.Update();
                        }
                    }
                }

                // 事件
                var elist = list.Where(e => e.Kind == FunctionKinds.Event).ToList();
                foreach (var item in spec.Events!)
                {
                    var eEntity = elist.FirstOrDefault(e => e.Identifier == item.Id) ?? elist.FirstOrDefault(e => e.Name == item.Name) ?? new ProductFunction();

                    eEntity.FromEventSpec(item);
                    eEntity.ProductId = Id;

                    eEntity.Enable = true;
                    eEntity.Save();
                }
                if (product.VerifyModel)
                {
                    foreach (var item in elist)
                    {
                        if (!spec.Events.Any(e => e.Id == item.Identifier))
                        {
                            item.Enable = false;
                            item.Update();
                        }
                    }
                }

                // 服务
                var slist = list.Where(e => e.Kind == FunctionKinds.Service).ToList();
                foreach (var item in spec.Services!)
                {
                    var sEntity = elist.FirstOrDefault(e => e.Identifier == item.Id) ?? slist.FirstOrDefault(e => e.Name == item.Name) ?? new ProductFunction();

                    sEntity.FromServiceSpec(item);
                    sEntity.ProductId = Id;
                    // 每次导入完成后会重置发布状态
                    //sEntity.IsPublish = false;
                    sEntity.Enable = true;
                    sEntity.Save();
                }
                if (product.VerifyModel)
                {
                    foreach (var item in slist)
                    {
                        if (!spec.Services.Any(e => e.Id == item.Identifier))
                        {
                            item.Enable = false;
                            item.Update();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                XTrace.WriteLine(ex + "");

                result.msg = GetResource("导入失败");
                return Json(result);
            }

            result.success = true;
            result.msg = GetResource("导入成功");
        }
        return Json(result);
    }

}
