﻿using HlktechIoT.Core.Models;
using HlktechIoT.Data;
using HlktechIoT.IoTServer.Services;
using HlktechIoT.Services;

using IoTMqtt.Services;

using IoTServer.Models;

using NewLife;
using NewLife.Caching;
using NewLife.Data;
using NewLife.IoT.ThingModels;
using NewLife.Log;
using NewLife.MQTT.Handlers;
using NewLife.MQTT.Messaging;
using NewLife.Net;
using NewLife.Serialization;

using Newtonsoft.Json;

using Pek;
using Pek.Helpers;
using Pek.Models;
using Pek.Timing;

using System.Collections.Concurrent;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

using XCode;

using LogLevel = NewLife.Log.LogLevel;

namespace IoTServer.Services;

/// <summary>MQTT控制器。处理业务逻辑</summary>
public class MqttController : MqttHandler {
    #region 属性
    private readonly MyDeviceService _deviceService;
    private readonly ThingService _thingService;
    private readonly MqttService _mqttService;
    private readonly QueueService _queue;
    private ICacheProvider _cacheProvider;

    private static readonly ConcurrentDictionary<String, Int32> _Sessions = new();
    private static readonly ConcurrentDictionary<String, CancellationTokenSource> _DeviceSources = new();

    private Boolean Forward = false;
    #endregion

    #region 构造
    /// <summary>实例化</summary>
    /// <param name="deviceService"></param>
    /// <param name="thingService"></param>
    /// <param name="queue"></param>
    /// <param name="tracer"></param>
    /// <param name="log"></param>
    public MqttController(MyDeviceService deviceService, ThingService thingService, MqttService mqttService, QueueService queue, ITracer tracer, ILog log, ICacheProvider cacheProvider)
    {
        Tracer = tracer;
        Log = log;
        _deviceService = deviceService;
        _thingService = thingService;
        _mqttService = mqttService;
        _queue = queue;
        _cacheProvider = cacheProvider;
    }
    #endregion

    #region 接收指令
    /// <summary>当客户端连接时</summary>
    /// <param name="session"></param>
    /// <param name="message"></param>
    /// <returns></returns>
    protected override ConnAck OnConnect(ConnectMessage message)
    {
        using var span = Tracer?.NewSpan("MqttLogin", message);

        var session = Session;
        var clientId = message.ClientId;
        //WriteLog("客户端[{0}]连接 user={1} pass={2} clientId={3}", session.Remote.EndPoint, message.Username, message.Password, clientId);
        if (clientId.IsNullOrEmpty()) clientId = message.Username;

        //XTrace.WriteLine($"测试获取到的MqttServer:{MqttFields.MqttServer.Sessions.Count}");

        //var deviceSessions = MqttFields.MqttServer.Sessions
        //    .Select(item => item.Value)
        //    .Where(s => s is IExtend extend &&
        //        (extend["DeviceSession"] is MqttDeviceSession deviceSession && deviceSession.ClientId == clientId ||
        //         extend["GateWaySession"] is MqttUserSession gateWaySession && gateWaySession.ClientId == clientId))
        //    .Cast<INetSession>()
        //    .ToList();

        ////XTrace.WriteLine($"有重复的数据：{deviceSessions.Count}_{clientId}");

        //foreach (var item in deviceSessions)
        //{
        //    //item.MqttHandler.Close($"有新的同ClientId设备上线被挤下2:{clientId}");
        //    item.Close($"有新的同ClientId设备上线被挤下:{clientId}");
        //    item?.TryDispose();
        //}

        //foreach (var item in MqttFields.MqttServer.Sessions)
        //{
        //    var s = item.Value;
        //    XTrace.WriteLine($"测试获取到的MqttServeraaaaaaa:{s.ID}");

        //    if (s is IExtend extend)
        //    {
        //        if (extend["DeviceSession"] is MqttDeviceSession ss)
        //        {
        //            XTrace.WriteLine($"测试获取到的MqttServer1111:{ss.ToJson()}");
        //        }
        //        else if (extend["UserSession"] is MqttUserSession sss)
        //        {
        //            XTrace.WriteLine($"测试获取到的MqttServer2222:{sss.ToJson()}");
        //        }
        //        else if (extend["SystemSession"] is MqttUserSession ssss)
        //        {
        //            XTrace.WriteLine($"测试获取到的MqttServer3333:{ssss.ToJson()}");
        //        }
        //    }
        //}

        try
        {
            var strArray = clientId.Split('|');
            if (strArray.Length == 5)  // 设备登录
            {
                XTrace.WriteLine($"获取到的设备登录数据：{clientId}：{message.Username}:{message.Password}");
                var dvSession = _mqttService.MqttLogin(new MqttAuthReq
                {
                    ClientId = clientId,
                    Username = message.Username,
                    Password = message.Password,
                    RemoteIp = session.Remote.Host
                });

                // 非法传参
                if (dvSession == null) return new ConnAck { ReturnCode = ConnectReturnCode.RefusedIdentifierRejected };

                // 存储客户端信息
                if (session is IExtend extend) extend["DeviceSession"] = dvSession;

                var device = dvSession.Device;

                //  判断该客户端是否有连接存在，要挤掉
                if (_Sessions.TryGetValue(clientId, out var sessionId))
                {
                    var oldSession = session.Host.GetSession(sessionId);
                    oldSession?.TryDispose();
                }

                // 取消并清理旧的消费任务
                if (_DeviceSources.TryGetValue(clientId, out var oldSource))
                {
                    oldSource?.Cancel();
                    oldSource?.Dispose();
                }

                // 注册新的会话
                _Sessions[clientId] = session.ID;

                _queue.Publish(device.Code, new { Type = "CloseQueue", SessionId = Session.ID, DeviceCode = device.Code, Time = UnixTime.ToTimestamp() }.ToJson());  // 关闭之前的队列

                // 创建新的消费任务
                var newSource = new CancellationTokenSource();
                _DeviceSources[clientId] = newSource;
                _ = Task.Run(() => ConsumeMessage(device, session.Remote.Host, newSource));

                // 以服务的方式推送设备上线信息
                _queue.Publish(new ServiceModel
                {
                    Type = "OnLine",
                    InputData = $"Status=1;Time={UnixTime.ToTimestamp()};ProductKey={dvSession.ProductKey};DeviceCode={dvSession.Device.Code}",
                });
            }
            else if (strArray.Length == 4) // 用户登录
            {
                //XTrace.WriteLine($"进入到用户登录");

                var userSession = _mqttService.MqttUserLogin(new MqttAuthReq
                {
                    ClientId = clientId,
                    Username = message.Username,
                    Password = message.Password,
                    RemoteIp = session.Remote.Host
                }, strArray.Length);

                // 存储用户信息
                if (session is IExtend extend) extend["UserSession"] = userSession;
            }
            else if (strArray.Length == 3) // 系统客户端
            {
                //XTrace.WriteLine($"进入到系统客户端登录");

                var mqttAuthReq = new MqttAuthReq
                {
                    ClientId = clientId,
                    Username = message.Username,
                    Password = message.Password,
                    RemoteIp = session.Remote.Host
                };

                var username = mqttAuthReq.Username;
                var password = mqttAuthReq.Password;

                if (clientId.IsNullOrEmpty())
                    throw new Exception($"MqttLogin:clientId为空:{mqttAuthReq.ToJson()}");

                if (username.IsNullOrEmpty())
                    throw new Exception($"MqttLogin:username为空:{mqttAuthReq.ToJson()}");

                if (password.IsNullOrEmpty())
                    throw new Exception($"MqttLogin:password为空:{mqttAuthReq.ToJson()}");

                var ss = clientId.Split('|');
                if (ss.Length >= 1)
                {
                    if (!ss[0].EqualIgnoreCase("System"))
                        throw new Exception($"MqttLogin:登录参数非法:{mqttAuthReq.ToJson()}");
                }

                var time = 0L;
                if (!username.IsNullOrEmpty())
                {
                    var sss = username.Split('|');
                    if (sss.Length >= 2)
                    {
                        var Name = sss[0];
                        time = sss[1].ToLong();

                        if (!Name.EqualIgnoreCase(DHMqttSetting.Current.ServerUserName))
                            throw new Exception($"MqttLogin:登录用户名不一致:{mqttAuthReq.ToJson()}");
                    }
                }

                var now = UnixTime.ToTimestamp();
                var fiveMinutesInMilliseconds = 5 * 60 * 1000; // 5分钟转换为毫秒
                if (Math.Abs(time - now) > fiveMinutesInMilliseconds) return null;

                if (!password.EqualIgnoreCase(DHMqttSetting.Current.ServerPwd))
                    throw new Exception($"MqttLogin:登录密码不一致:{mqttAuthReq.ToJson()}");

                // 取消并清理旧的消费任务
                if (_DeviceSources.TryGetValue(clientId, out var oldSystemSource))
                {
                    oldSystemSource?.Cancel();
                    oldSystemSource?.Dispose();
                }

                // 注册新的会话
                _Sessions[clientId] = session.ID;

                var systemSession = new MqttUserSession
                {
                    ClientId = clientId,
                };

                // 存储用户信息
                if (session is IExtend extend) extend["SystemSession"] = systemSession;

                // 创建新的消费任务
                var newSystemSource = new CancellationTokenSource();
                _DeviceSources[clientId] = newSystemSource;
                _ = Task.Run(() => ConsumeMessage1(newSystemSource));
            }
            else if (strArray.Length == 2) // 对接网关
            {
                //XTrace.WriteLine($"进入到对接网关");

                var mqttAuthReq = new MqttAuthReq
                {
                    ClientId = clientId,
                    Username = message.Username,
                    Password = message.Password,
                    RemoteIp = session.Remote.Host
                };

                var username = mqttAuthReq.Username;
                var password = mqttAuthReq.Password;

                if (clientId.IsNullOrEmpty())
                    throw new Exception($"MqttLogin:clientId为空:{mqttAuthReq.ToJson()}");

                if (username.IsNullOrEmpty())
                    throw new Exception($"MqttLogin:username为空:{mqttAuthReq.ToJson()}");

                if (password.IsNullOrEmpty())
                    throw new Exception($"MqttLogin:password为空:{mqttAuthReq.ToJson()}");

                var ss = clientId.Split('|');
                if (ss.Length >= 1)
                {
                    if (!ss[0].EqualIgnoreCase("GateWay"))
                        throw new Exception($"MqttLogin:登录参数非法:{mqttAuthReq.ToJson()}");
                }

                var time = 0L;
                if (!username.IsNullOrEmpty())
                {
                    var sss = username.Split('|');
                    if (sss.Length >= 3)
                    {
                        var strArray1 = sss[1].SplitAsDictionary("=", ",");
                        time = sss[2].ToLong();

                        var random = strArray1["random"];
                        var appKey = strArray1["appKey"];

                        var modelProject = Project.FindByPushAppKey(appKey);
                        if (modelProject == null)
                            throw new Exception($"MqttLogin:appKey参数不存在:{mqttAuthReq.ToJson()}");

                        if (!modelProject.PushEnabled)
                            throw new Exception($"MqttLogin:项目未开启推送:{mqttAuthReq.ToJson()}");

                        var signContent = $"random={random}";
                        var password1 = doSign(signContent, modelProject.PushAppSecret);

                        if (!password1.EqualIgnoreCase(password))
                            throw new Exception($"MqttLogin:登录密码不一致:{mqttAuthReq.ToJson()}");
                    }
                }

                var now = UnixTime.ToTimestamp();
                var fiveMinutesInMilliseconds = 5 * 60 * 1000; // 5分钟转换为毫秒
                if (Math.Abs(time - now) > fiveMinutesInMilliseconds) return null;

                //  判断该客户端是否有连接存在，要挤掉
                if (_Sessions.TryGetValue(clientId, out var sessionId))
                {
                    var oldSession = session.Host.GetSession(sessionId);
                    oldSession?.TryDispose();
                }

                // 注册新的会话
                _Sessions[clientId] = session.ID;

                var gateWaySession = new MqttUserSession
                {
                    ClientId = clientId,
                };

                // 存储用户信息
                if (session is IExtend extend) extend["GateWaySession"] = gateWaySession;
            }
            else
            {
                return new ConnAck { ReturnCode = ConnectReturnCode.RefusedNotAuthorized };
            }

            return base.OnConnect(message);
        }
        catch (Exception ex)
        {
            span?.SetError(ex, null);

            XTrace.WriteException(ex);

            // 登录出错时，向MQTT客户端发送错误响应
            return new ConnAck { ReturnCode = ConnectReturnCode.RefusedBadUsernameOrPassword };
        }
    }

    /// <summary>
    /// 检查是否登录，如果未登录则断开会话
    /// </summary>
    /// <param name="session"></param>
    /// <returns></returns>
    private (Object MqttSession, Int32 Type) CheckLogin(INetSession session)
    {
        if (session is IExtend extend && extend["DeviceSession"] is MqttDeviceSession ss) return (ss, 1);
        else if (session is IExtend extend1 && extend1["UserSession"] is MqttUserSession sss) return (sss, 2);
        else if (session is IExtend extend2 && extend2["SystemSession"] is MqttUserSession ssss) return (ssss, 3);
        else if (session is IExtend extend3 && extend3["GateWaySession"] is MqttUserSession sssss) return (sssss, 4);

        // 未登录
        session.TryDispose();

        return (null, 0);
    }

    /// <summary>当客户端断开连接时</summary>
    /// <param name="session"></param>
    /// <param name="message"></param>
    /// <returns></returns>
    protected override MqttMessage OnDisconnect(DisconnectMessage message)
    {
        var session = Session;
        //WriteLog("客户端[{0}]断开", session.Remote);

        var dvSession = CheckLogin(session);
        if (dvSession.MqttSession == null || dvSession.Type == 0) return null;

        if (dvSession.Type == 1)  // 设备端
        {
            var deviceSession = (MqttDeviceSession)dvSession.MqttSession;

            _ = PublishAsync($"sys/{deviceSession.ProductKey}/{deviceSession.Device.Code}/thing/status/update", new { DeviceName = deviceSession.Device.Code, Status = 3, Time = UnixTime.ToTimestamp() }, true);

            _deviceService.Logout(deviceSession.Device, "OnDisconnect", "Mqtt", session.Remote.Host);

            // 清理会话和消费任务
            if (_Sessions.TryRemove(deviceSession.ClientId, out var sessionId))
            {
                var oldSession = session.Host.GetSession(sessionId);
                oldSession?.TryDispose();
            }

            if (_DeviceSources.TryRemove(deviceSession.ClientId, out var source))
            {
                source?.Cancel();
                _ = Task.Run(async () =>
                {
                    await Task.Delay(100); // 等待任务结束
                    source?.Dispose();
                });
            }
        }
        else if (dvSession.Type == 2)  // 用户端
        {
            var userSession = (MqttUserSession)dvSession.MqttSession;
            var IdentityId = userSession.IdentityId;

            var user = AppUser.FindByIdentityId(IdentityId);
            if (user != null)
            {
                user.Online = false;
                user.Update();
            }

            if (_Sessions.TryRemove(userSession.ClientId, out var sessionId))
            {
                var oldSession = session.Host.GetSession(sessionId);
                oldSession?.TryDispose();
            }
        }
        else if (dvSession.Type == 3)  // 系统端
        {
            var userSession = (MqttUserSession)dvSession.MqttSession;

            if (_Sessions.TryRemove(userSession.ClientId, out var sessionId))
            {
                var oldSession = session.Host.GetSession(sessionId);
                oldSession?.TryDispose();
            }

            if (_DeviceSources.TryRemove(userSession.ClientId, out var source))
            {
                source?.Cancel();
                _ = Task.Run(async () =>
                {
                    await Task.Delay(100); // 等待任务结束
                    source?.Dispose();
                });
            }
        }
        else if (dvSession.Type == 4)  // 网关端
        {
            var userSession = (MqttUserSession)dvSession.MqttSession;

            if (_Sessions.TryRemove(userSession.ClientId, out var sessionId))
            {
                var oldSession = session.Host.GetSession(sessionId);
                oldSession?.TryDispose();
            }
        }

        return base.OnDisconnect(message);
    }

    /// <summary>当收到客户端发布的消息时</summary>
    /// <param name="session"></param>
    /// <param name="message"></param>
    /// <returns></returns>
    protected override MqttIdMessage OnPublish(PublishMessage message)
    {
        if (NewLife.Setting.Current.Debug)
            XTrace.WriteLine($"收到客户端发布的消息：{message.ToJson()}_{message.Topic}");

        var session = Session;
        var dvSession = CheckLogin(session);
        if (dvSession.MqttSession == null || dvSession.Type == 0) return null;

        using var span = Tracer?.NewSpan(nameof(OnPublish));

        if (dvSession.Type == 1)  // 设备端
        {
            var deviceSession = (MqttDeviceSession)dvSession.MqttSession;

            var dv = deviceSession.Device;
            //var deviceCode = dv.Code;
            //var productKey = dvSession.ProductKey;

            // TODO 通过产品查询判断上报的topic是否合规
            //var serviceReplyTopic = $"sys/{productKey}/{deviceCode}/thing/service/post_reply";
            //var propertyPostTopic = $"sys/{productKey}/{deviceCode}/thing/property/post";
            //var pingPostTopic = $"sys/{productKey}/{deviceCode}/thing/event/ping/post";

            var ip = session.Remote.Host;
            if (dv == null)
            {
                //throw new Exception($"非法设备编码，产品[{productKey}]-设备[{deviceCode}]不存在！");
                session.TryDispose();
                return null;
            }

            CheckAllMessage(dv, ip);  // 统计收到的所有消息数

            // 更新设备在线状态。网关上线
            _deviceService.SetDeviceOnline(dv, ip, nameof(OnPublish));

            var topic = message.Topic;
            var msg = message.Payload.ToStr();
            //XTrace.WriteLine($"收到客户端发布的消息：{msg}_{topic}");
            span?.SetTag(topic + Environment.NewLine + msg);

            try
            {
                var segs = topic.Split('/', StringSplitOptions.RemoveEmptyEntries);
                //if (segs == null || segs.Length < 4) throw new Exception($"收到过短Topic[{topic}]，Payload={msg}");
                if (segs != null && segs.Length >= 4)
                {
                    var productKey = segs[1];
                    var deviceCode = segs[2];
                    var child = Device.FindByCode(deviceCode);
                    if (child == null || child.Id != dv.Id && child.ParentId != dv.Id)
                        throw new Exception($"非法设备编码，[{deviceCode}]并非当前登录设备[{dv}]的子设备");

                    // 默认使用登录设备，然后使用Topic中的设备
                    dv = child;

                    var topic2 = segs.Skip(3).Join("/");

                    // 数据上报
                    if (topic2.EqualIgnoreCase("thing/property/post"))
                    {
                        CheckMessage(dv, ip);  // 统计消息条数
                        OnPostProperty(msg, dv, productKey, deviceCode, ip);
                        _deviceService.WriteHistory(dv, "设备上报", true, $"Topic:{topic}\r\ncontent:{msg}", ip);
                    }
                    // 服务调用结果
                    else if (topic2.EqualIgnoreCase("thing/service/post_reply"))
                    {
                        OnServiceReply(msg, dv, productKey, deviceCode, ip);
                    }
                    // 事件上报
                    else if (topic2.EqualIgnoreCase("thing/event/info/post", "thing/event/alert/post", "thing/event/error/post"))
                    {
                        var eType = segs[2];

                        OnPostEvent(msg, dv, productKey, deviceCode, ip, eType);
                    }
                    // ping上报
                    else if (topic2.EqualIgnoreCase("thing/event/ping/post"))
                    {
                        OnPing(msg, dv, productKey, deviceCode, ip);
                    }
                    // OTA升级 旧
                    else if (topic2.EqualIgnoreCase("thing/ota/firmware/get"))
                    {
                        OnUpgrade(msg, dv, productKey, deviceCode, ip);
                    }
                    // OTA升级 新
                    else if (topic2.EqualIgnoreCase("thing/ota/get_reply"))
                    {
                        OnUpgrade1(msg, dv, productKey, deviceCode, ip);
                    }
                    // 设备发送消息请求重置云端
                    else if (topic2.EqualIgnoreCase("thing/event/reset"))
                    {
                        dv.Online = false;
                        dv.Update();

                        CheckMessage(dv, ip);  // 统计消息条数
                        OnReset(msg, dv, productKey, deviceCode, ip);
                    }
                    // 透传下发回复
                    else if (topic2.EqualIgnoreCase("thing/property/set_reply"))
                    {
                        _deviceService.WriteHistory(dv, "透传回复", true, $"Topic:{topic}\r\ncontent:{msg}", ip);
                    }
                    else
                        throw new Exception($"收到非法Topic[{topic}]，Payload={msg}");
                }
            }
            catch (JsonReaderException ex)
            {
                span?.SetError(ex, null);
                Log?.Error(ex.Message);

                // 收到无法识别的Topic
                var ev = new EventModel
                {
                    Type = "error",
                    Name = "Json格式错误",
                    Remark = ex.Message,
                    Data = topic,
                    Time = DateTime.UtcNow.ToLong(),
                };

                _deviceService.WriteHistory(dv, ev.Name, false, ex.Message, ip);
                _thingService.PostEvent(dv, ev, ip);

                return null;
            }
            catch (Exception ex)
            {
                span?.SetError(ex, null);
                Log?.Error(ex.Message);

                // 收到无法识别的Topic
                var ev = new EventModel
                {
                    Type = "error",
                    Name = "非法发布",
                    Remark = ex.Message,
                    Data = topic,
                    Time = DateTime.UtcNow.ToLong(),
                };

                _deviceService.WriteHistory(dv, ev.Name, false, ex.Message, ip);
                _thingService.PostEvent(dv, ev, ip);

                return null;
            }
        }
        else if (dvSession.Type == 2)  // 用户端
        {
            var deviceSession = (MqttUserSession)dvSession.MqttSession;

            var user = deviceSession.User;

            //var IdentityId = deviceSession.IdentityId;

            var ip = session.Remote.Host;
            //if (IdentityId.IsNullOrWhiteSpace())
            //{
            //    session.TryDispose();
            //    return null;
            //}
            if (user == null)
            {
                session.TryDispose();
                return null;
            }

            user.Online = true;
            user.Update();

            var topic = message.Topic;
            var msg = message.Payload.ToStr();
            span?.SetTag(topic + Environment.NewLine + msg);

            Device dv = null;

            try
            {
                var segs = topic.Split('/', StringSplitOptions.RemoveEmptyEntries);

                if (segs == null || segs.Length < 4)
                {
                    XTrace.WriteException(new Exception($"收到过短Topic[{topic}]，Payload={msg}"));
                    session.TryDispose();
                    return null;
                }

                var productKey = segs[1];
                var deviceCode = segs[2];

                dv = Device.FindByCode(deviceCode);

                CheckAllMessage(dv, ip);  // 统计收到的所有消息数

                var topic2 = segs.Skip(3).Join("/");

                // 透传通道，比如APP端发信息
                if (topic2.EqualIgnoreCase("thing/property/set"))
                {
                    CheckMessage(dv, ip);  // 统计消息条数
                    _deviceService.WriteHistory(dv, "APP下发", true, $"Topic:{topic}\r\ncontent:{msg}", ip);
                }
            }
            catch (JsonReaderException ex)
            {
                span?.SetError(ex, null);
                Log?.Error(ex.Message);

                if (dv != null)
                {
                    // 收到无法识别的Topic
                    var ev = new EventModel
                    {
                        Type = "error",
                        Name = "Json格式错误",
                        Remark = ex.Message,
                        Data = topic,
                        Time = DateTime.UtcNow.ToLong(),
                    };

                    _deviceService.WriteHistory(dv, ev.Name, false, ex.Message, ip);
                    _thingService.PostEvent(dv, ev, ip);
                }

                return null;
            }
            catch (Exception ex)
            {
                span?.SetError(ex, null);
                Log?.Error(ex.Message);

                if (dv != null)
                {
                    // 收到无法识别的Topic
                    var ev = new EventModel
                    {
                        Type = "error",
                        Name = "非法发布",
                        Remark = ex.Message,
                        Data = topic,
                        Time = DateTime.UtcNow.ToLong(),
                    };

                    _deviceService.WriteHistory(dv, ev.Name, false, ex.Message, ip);
                    _thingService.PostEvent(dv, ev, ip);
                }

                return null;
            }
        }
        else if (dvSession.Type == 3) // 系统端
        {

        }
        else if (dvSession.Type == 4) // 网关端
        {
            var deviceSession = (MqttUserSession)dvSession.MqttSession;

            var topic = message.Topic;
            var msg = message.Payload.ToStr();
            span?.SetTag(topic + Environment.NewLine + msg);

            try
            {
                var segs = topic.Split('/', StringSplitOptions.RemoveEmptyEntries);

                if (segs == null || segs.Length < 4)
                {
                    XTrace.WriteException(new Exception($"收到过短Topic[{topic}]，Payload={msg}"));
                    session.TryDispose();
                    return null;
                }

                var topic2 = segs.Skip(3).Join("/");

                // Ping
                if (topic2.EqualIgnoreCase("event/ping/post"))
                {
                    var model = msg.ToJsonEntity<PingInfo>();

                    var rs = new HlktechIoT.Core.Models.PingResponse
                    {
                        Time = model?.Time ?? 0,
                        ServerTime = DateTime.UtcNow.ToLong(),
                    };

                    _ = PublishAsync($"sys/GateWay/{segs[2]}/event/ping/post_reply", rs, true);
                }
            }
            catch (JsonReaderException ex)
            {
                span?.SetError(ex, null);
                Log?.Error(ex.Message);

                return null;
            }
            catch (Exception ex)
            {
                span?.SetError(ex, null);
                Log?.Error(ex.Message);

                return null;
            }
        }

        return base.OnPublish(message);
    }

    private void CheckAllMessage(Device dv, String ip)
    {
        //var _cache = _cacheProvider.Cache;
        //var key = $"MqttMessage:{dv.Code}";
        //if (_cache.Get<Int32>(key) == 0)
        //{
        //    _cache.Increment()
        //}

        var dTime = DateTime.Now.Date;
        var model = MessageCount.FindByIotIdAndMTime(dv.Code, dTime);
        if (model == null)
        {
            model = new MessageCount();
            model.AllCount++;
            model.IotId = dv.Code;
            model.MTime = dTime;
            model.UpdateIP = ip;
            model.CreateIP = ip;

            model.Insert();
        }
        else
        {
            model.AllCount++;
            model.IotId = dv.Code;
            model.MTime = dTime;
            model.UpdateIP = ip;
            model.SaveAsync();
        }
    }

    private void CheckMessage(Device dv, String ip)
    {
        //var _cache = _cacheProvider.Cache;
        //var key = $"MqttMessage:{dv.Code}";
        //if (_cache.Get<Int32>(key) == 0)
        //{
        //    _cache.Increment()
        //}

        var dTime = DateTime.Now.Date;
        var model = MessageCount.FindByIotIdAndMTime(dv.Code, dTime);
        if (model == null)
        {
            model = new MessageCount();
            model.Count++;
            model.IotId = dv.Code;
            model.MTime = dTime;
            model.UpdateIP = ip;
            model.CreateIP = ip;
            model.Insert();
        }
        else
        {
            model.Count++;
            model.IotId = dv.Code;
            model.MTime = dTime;
            model.UpdateIP = ip;
            model.SaveAsync();
        }
    }

    /// <summary>当收到客户端的心跳时</summary>
    /// <param name="session"></param>
    /// <param name="message"></param>
    /// <returns></returns>
    protected override NewLife.MQTT.Messaging.PingResponse OnPing(PingRequest message)
    {
        var session = Session;
        var dvSession = CheckLogin(session);
        if (dvSession.MqttSession == null || dvSession.Type == 0) return null;

        if (dvSession.Type == 1)  // 设备端
        {
            var deviceSession = (MqttDeviceSession)dvSession.MqttSession;

            //var clientId = dvSession.ClientId;
            _deviceService.Ping(deviceSession.Device, null, null, "Mqtt", session.Remote.Host);

            //if (_Logins[clientId] >= 1)
            //    _Logins[clientId]--;
        }
        else if (dvSession.Type == 2)  // 用户端
        {
            var userSession = (MqttUserSession)dvSession.MqttSession;
            XTrace.WriteLine($"APP心跳：{userSession.IdentityId}");

            var user = userSession.User;
            if (user != null)
            {
                user.Online = true;
                user.LastActive = DateTime.Now;
                user.Update();
            }
        }
        else
        {

        }

        return base.OnPing(message);
    }

    /// <summary>当收到客户端的订阅时</summary>
    /// <param name="session"></param>
    /// /// <param name="message"></param>
    /// <returns></returns>
    protected override SubAck OnSubscribe(SubscribeMessage message)
    {
        var session = Session;
        //WriteLog("客户端[{0}]订阅主题[{1}]", session.Remote, String.Join(", ", message.Requests.Select(p => p.TopicFilter)));

        var dvSession = CheckLogin(session);
        if (dvSession.MqttSession == null || dvSession.Type == 0) return null;

        if (dvSession.Type == 1)  // 设备端
        {
            var deviceSession = (MqttDeviceSession)dvSession.MqttSession;

            var ip = session.Remote.Host;
            var ev = new EventModel
            {
                Type = "info",
                Name = "订阅",
                Remark = message.Requests.ToJson(),
                Time = DateTime.UtcNow.ToLong(),
            };

            _thingService.PostEvent(deviceSession.Device, ev, ip);
        }
        else
        {

        }

        return base.OnSubscribe(message);
    }

    /// <summary>取消订阅</summary>
    /// <param name="session"></param>
    /// <param name="message"></param>
    /// <returns></returns>
    protected override UnsubAck OnUnsubscribe(UnsubscribeMessage message)
    {
        var session = Session;
        //WriteLog("客户端[{0}]取消订阅主题[{1}]", session.Remote, String.Join(", ", message.TopicFilters));

        var dvSession = CheckLogin(session);
        if (dvSession.MqttSession == null || dvSession.Type == 0) return null;

        if (dvSession.Type == 1)  // 设备端
        {
            var deviceSession = (MqttDeviceSession)dvSession.MqttSession;

            var ip = session.Remote.Host;
            var ev = new EventModel
            {
                Type = "info",
                Name = "取消订阅",
                Remark = message.TopicFilters.ToJson(),
                Time = DateTime.UtcNow.ToLong(),
            };

            _thingService.PostEvent(deviceSession.Device, ev, ip);
        }
        else
        {

        }

        return base.OnUnsubscribe(message);
    }
    #endregion

    #region 业务处理
    private void OnPostProperty(String msg, Device dv, String productKey, String deviceCode, String ip)
    {
        var model = msg.ToJsonEntity<DataModels>();
        if (model != null && model.Items != null)
        {
            _thingService.PostData(dv, model, "MqttPost", ip);

            _ = PublishAsync($"sys/{productKey}/{deviceCode}/thing/property/post_reply", "ok");
        }
    }

    private void OnServiceReply(String msg, Device dv, String productKey, String deviceCode, String ip)
    {
        var model = msg.ToJsonEntity<ServiceReplyModel>();

        _thingService.ServiceReply(dv, model);
    }

    private void OnPostEvent(String msg, Device dv, String productKey, String deviceCode, String ip, String eventType)
    {
        var model = msg.ToJsonEntity<EventModel>();

        _thingService.PostEvent(dv, model, ip);

        var rs = new EventResponse()
        {
            Time = model?.Time ?? 0,
            ServerTime = DateTime.UtcNow.ToLong(),
            EventType = eventType,
            Status = "ok",
            Name = model?.Name
        };

        _ = PublishAsync($"sys/{productKey}/{deviceCode}/thing/event/{eventType}/post_reply", rs);
    }

    private void OnPing(String msg, Device dv, String productKey, String deviceCode, String ip)
    {
        var model = msg.ToJsonEntity<PingInfo>();

        _deviceService.Ping(dv, model, null, "Mqtt", ip);

        var modelMessageCount = MessageCount.FindByIotIdAndMTime(deviceCode, DateTime.Now.Date);

        var rs = new HlktechIoT.Core.Models.PingResponse
        {
            Time = model?.Time ?? 0,
            ServerTime = DateTime.UtcNow.ToLong(),

            MesLimitMin = DHMqttSetting.Current.LimitMin,
            MesLimitMax = DHMqttSetting.Current.LimitMax,
            AlreadyMesCount = modelMessageCount?.Count ?? 0,
            HasLimit = !dv.Enable,
        };

        if (!rs.HasLimit)
        {
            if (dv.Product?.EnableLimitTime == true && dv.ExpiredTime <= DateTime.Now)
            {
                rs.HasLimit = true;
            }
        }

        _ = PublishAsync($"sys/{productKey}/{deviceCode}/thing/event/ping/post_reply", rs);
    }

    private void OnUpgrade(String msg, Device dv, String productKey, String deviceCode, String ip)
    {
        // 应用过滤规则，使用最新的一个版本
        var pv = _deviceService.Upgrade(dv, ip);
        if (pv == null) return;

        //todo 需要处理url为完整http地址
        var rs = new UpgradeInfo
        {
            Version = pv.Version,
            Source = pv.Source,
            Executor = pv.Executor,
            Force = pv.Force,
            FileSize = pv.Size,
            FileHash = pv.FileHash,
            Description = pv.Remark,
        };

        _ = PublishAsync($"sys/{productKey}/{deviceCode}/thing/ota/firmware/get_reply", rs);
    }

    /// <summary>
    /// 升级进度上报
    /// </summary>
    /// <param name="msg"></param>
    /// <param name="dv"></param>
    /// <param name="productKey"></param>
    /// <param name="deviceCode"></param>
    /// <param name="ip"></param>
    private void OnUpgrade1(String msg, Device dv, String productKey, String deviceCode, String ip)
    {
        var options = new JsonDocumentOptions
        {
            AllowTrailingCommas = true
        };

        using (JsonDocument document = JsonDocument.Parse(msg, options))
        {
            var jsonElement = document.RootElement;
            var Code = jsonElement.GetProperty("Code").GetInt32();
            var MsgId = jsonElement.GetProperty("MsgId").GetInt32();

            // 写详细日志
            var ml = new UpgradeALogs
            {
                LId = MsgId,
                IotId = dv.Code,
                Content = msg
            };
            ml.SaveAsync();

            if (Code != 0)
            {
                dv.UpStatus = 0;
                dv.Update();

                var modelUpgradeLogs = UpgradeLogs.FindById(MsgId);
                if (modelUpgradeLogs != null)
                {
                    modelUpgradeLogs.Status = 3;
                    modelUpgradeLogs.Remark = Code == 1 ? "版本不匹配" : Code == 2 ? "Flash校验失败" : Code == 3 ? "url不可用" : Code == 5 ? "不在升级" : "";
                    modelUpgradeLogs.Update();
                }
            }
            else
            {
                var Step = jsonElement.GetProperty("Step").GetInt32();
                if (Step == 100)
                {
                    dv.UpStatus = 0;
                    dv.Update();

                    var modelUpgradeLogs = UpgradeLogs.FindById(MsgId);
                    if (modelUpgradeLogs != null)
                    {
                        //var Message = jsonElement.GetProperty("Message").GetString();
                        var Module = jsonElement.GetProperty("Module").GetString();
                        var Version = jsonElement.GetProperty("Version").GetString();

                        //if (Module == dv.Module && Version != dv.Version)
                        //{
                        //    modelUpgradeLogs.Status = 2;
                        //    modelUpgradeLogs.Update();
                        //}

                        modelUpgradeLogs.Status = 2;
                        modelUpgradeLogs.Update();
                    }
                }
                else
                {
                    dv.UpStatus = 1;
                    dv.Update();

                    var modelUpgradeLogs = UpgradeLogs.FindById(MsgId);
                    if (modelUpgradeLogs != null)
                    {
                        modelUpgradeLogs.Status = 1;
                        modelUpgradeLogs.Update();
                    }
                }
            }
        }
    }

    /// <summary>
    /// 设备发送消息请求重置云端信息。
    /// </summary>
    /// <param name="msg"></param>
    /// <param name="dv"></param>
    /// <param name="productKey"></param>
    /// <param name="deviceCode"></param>
    /// <param name="ip"></param>
    private void OnReset(String msg, Device dv, String productKey, String deviceCode, String ip)
    {
        var options = new JsonDocumentOptions
        {
            AllowTrailingCommas = true
        };

        if (!msg.Contains("Reply", StringComparison.OrdinalIgnoreCase))
        {
            var DeviceReset = 0;
            var ConfigClean = 0;
            var ServiceId = 0L;

            using (JsonDocument document = JsonDocument.Parse(msg, options))
            {
                var jsonElement = document.RootElement;
                var Reset = jsonElement.GetProperty("Reset").GetInt32();
                
                if (jsonElement.TryGetProperty("Id", out var serviceId))
                {
                    ServiceId = serviceId.GetInt64();
                }

                if (Reset == 1)  // 重置
                {
                    dv.Online = false;
                    dv.Update();

                    DeviceReset = 1;
                    ConfigClean = 1;

                    var modelDeviceAssociatedUsers = DeviceAssociatedUsers.FindByDeviceName(deviceCode);
                    if (modelDeviceAssociatedUsers != null)
                    {
                        var modelDeviceBindLogs = new DeviceBindLogs
                        {
                            DeviceName = deviceCode,
                            Operating = $"设备重置解绑：OnReset",
                            DType = 2
                        };
                        modelDeviceBindLogs.SaveAsync();

                        var list = new List<String> { modelDeviceAssociatedUsers.IdentityId };

                        if (!modelDeviceAssociatedUsers.SubUsers.IsNullOrWhiteSpace())
                        {
                            var SubUsers = modelDeviceAssociatedUsers.SubUsers.Trim(',').Split(',');
                            SubUsers.ForEach(list.Add);
                        }

                        var searchList = AppUser.FindAllByIdentityId(list);  // 获取所有与设备有关的用户
                        foreach (var item in searchList)
                        {
                            var hashDevices = item.Devices?.Split(',').ToList();
                            hashDevices ??= [];
                            var listProductKeys = item.ProductKeys?.Split(',').ToList();
                            listProductKeys ??= [];

                            var index = hashDevices.IndexOf(deviceCode);
                            if (index != -1)
                            {
                                hashDevices.RemoveAt(index);
                                listProductKeys.RemoveAt(index);

                                item.Devices = hashDevices.Join();
                                item.ProductKeys = listProductKeys.Join();
                                item.Update();
                            }
                        }

                        modelDeviceAssociatedUsers.Delete();
                    }

                    // 以服务的方式给Web推送设备重置信息
                    _queue.PublishWeb(new DelayQueue
                    {
                        Type = "OnLine",
                        InputData = $"Status=98;Time={UnixTime.ToTimestamp()};ProductKey={productKey};DeviceCode={deviceCode}",
                    });
                }
                else if (Reset == 2)  // 配网
                {
                    // 更新最后活跃时间
                    dv.LastActive = DateTime.Now;

                    dv.Online = true;
                    dv.Update();

                    DeviceReset = 0;
                    ConfigClean = 0;

                    DeviceHistory.Create(dv, "上线", true, $"设备上线。配网", null, ip, null);

                    var modelDeviceAssociatedUsers = DeviceAssociatedUsers.FindByDeviceName(deviceCode);
                    if (modelDeviceAssociatedUsers != null)
                    {
                        var modelDeviceBindLogs = new DeviceBindLogs
                        {
                            DeviceName = deviceCode,
                            Operating = $"设备配网解绑：OnReset",
                            DType = 2
                        };
                        modelDeviceBindLogs.SaveAsync();

                        var list = new List<String> { modelDeviceAssociatedUsers.IdentityId };

                        if (!modelDeviceAssociatedUsers.SubUsers.IsNullOrWhiteSpace())
                        {
                            var SubUsers = modelDeviceAssociatedUsers.SubUsers.Trim(',').Split(',');
                            SubUsers.ForEach(list.Add);
                        }

                        var searchList = AppUser.FindAllByIdentityId(list);  // 获取所有与设备有关的用户
                        foreach (var item in searchList)
                        {
                            var hashDevices = item.Devices?.Split(',').ToList();
                            hashDevices ??= [];
                            var listProductKeys = item.ProductKeys?.Split(',').ToList();
                            listProductKeys ??= [];

                            var index = hashDevices.IndexOf(deviceCode);
                            if (index != -1)
                            {
                                hashDevices.RemoveAt(index);
                                listProductKeys.RemoveAt(index);

                                item.Devices = hashDevices.Join();
                                item.ProductKeys = listProductKeys.Join();
                                item.Update();
                            }
                        }

                        modelDeviceAssociatedUsers.Delete();
                    }

                    // 以服务的方式给Web推送设备配网信息
                    _queue.PublishWeb(new DelayQueue
                    {
                        Type = "OnLine",
                        InputData = $"Status=99;Time={UnixTime.ToTimestamp()};ProductKey={productKey};DeviceCode={deviceCode}",
                    });
                }
            }

            var rs1 = new
            {
                DeviceName = deviceCode,
                Status = 99,
                Time = UnixTime.ToTimestamp()
            };
            _ = PublishAsync($"sys/{productKey}/{deviceCode}/thing/status/update", rs1, true);

            var rs = new
            {
                Id = ServiceId,
                DeviceReset,
                ConfigClean,
                Time = UnixTime.ToTimestamp()
            };
            _deviceService.WriteHistory(dv, "云端重置", true, $"云端重置:{msg}", ip);
            _ = PublishAsync($"sys/{productKey}/{deviceCode}/thing/event/reset_reply", rs);
        }
        else
        {
            using (JsonDocument document = JsonDocument.Parse(msg, options))
            {
                var jsonElement = document.RootElement;
                var Reply = jsonElement.GetProperty("Reply").GetInt32();

                var ServiceId = 0L;
                if (jsonElement.TryGetProperty("Id", out var serviceId))
                {
                    ServiceId = serviceId.GetInt64();
                }

                XTrace.WriteLine($"开始测试是否收到Reply:{Reply}");

                if (Reply == 1) // 重置
                {
                    var modelDeviceBindLogs = new DeviceBindLogs
                    {
                        DeviceName = deviceCode,
                        Operating = $"设备回应重置解绑：OnReset",
                        DType = 2
                    };
                    modelDeviceBindLogs.SaveAsync();

                    // 以服务的方式推送设备重置数据
                    _queue.Publish(new ServiceModel
                    {
                        Type = "Reset",
                        InputData = $"Msg={msg};ProductKey={productKey};DeviceCode={deviceCode};Id={ServiceId};Dv={dv.ToJson()}",
                    });
                }
                else if (Reply == 2)  // 配网
                {
                    var modelDeviceBindLogs = new DeviceBindLogs
                    {
                        DeviceName = deviceCode,
                        Operating = $"设备回应配网解绑：OnReset",
                        DType = 2
                    };
                    modelDeviceBindLogs.SaveAsync();

                    // 以服务的方式推送设备配网数据
                    _queue.Publish(new ServiceModel
                    {
                        Type = "Network",
                        InputData = $"Msg={msg};ProductKey={productKey};DeviceCode={deviceCode};Id={ServiceId};Dv={dv.ToJson()}",
                    });
                }
            }
        }
    }

    private async Task ConsumeMessage1(CancellationTokenSource source)
    {
        XTrace.WriteLine($"启动了吗？ConsumeMessage1");
        DefaultSpan.Current = null;
        var cancellationToken = source.Token;
        var queue = _queue.GetQueue();
        try
        {
            while (!cancellationToken.IsCancellationRequested && !Session.Disposed)
            {
                ISpan span = null;
                var mqMsg = await queue.TakeOneAsync(30, cancellationToken).ConfigureAwait(false);
                if (mqMsg != null)
                {
                    // 埋点
                    span = Tracer?.NewSpan($"mqtt:CommonQueue", mqMsg);
                    if (Log != null && Log.Level <= LogLevel.Debug) WriteLog("消费到下发指令消息：{0}", mqMsg);

                    XTrace.WriteLine($"消费到下发指令消息：ConsumeMessage1:{mqMsg}");

                    // 解码
                    var dic = JsonParser.Decode(mqMsg);
                    span?.Detach(dic);
                    var msg = JsonHelper.Convert<ServiceModel>(dic);

                    switch (msg.Type.ToLower())
                    {
                        case "online":  // 离线服务
                            {
                                var data = msg.InputData.SplitAsDictionary();
                                var ProductKey = data["ProductKey"];
                                var DeviceCode = data["DeviceCode"];

                                var topic = $"sys/{ProductKey}/{DeviceCode}/thing/status/update";

                                var rs = new
                                {
                                    DeviceName = DeviceCode,
                                    Status = data["Status"].ToInt(),
                                    Time = data["Time"].ToLong()
                                };

                                await PublishAsync(topic, rs, true).ConfigureAwait(false);
                            }
                            break;

                        case "reset":  // 设备重置，使用队列，防止业务没有处理完就设备端断开链接了
                            {
                                var options = new JsonDocumentOptions
                                {
                                    AllowTrailingCommas = true
                                };

                                var data = msg.InputData.SplitAsDictionary();
                                var Msg = data["Msg"];
                                var DeviceCode = data["DeviceCode"];
                                var ProductKey = data["ProductKey"];
                                var dv = data["Dv"].ToJsonEntity<Device>();
                                var ServiceId = data["Id"].ToLong();

                                var replyModel = new ServiceReplyModel { Id = ServiceId };

                                using (JsonDocument document = JsonDocument.Parse(Msg, options))
                                {
                                    var jsonElement = document.RootElement;
                                    var Reply = jsonElement.GetProperty("Reply").GetInt32();

                                    if (Reply == 1) // 重置
                                    {
                                        //var modelDeviceAssociatedUsers = DeviceAssociatedUsers.FindByDeviceName(DeviceCode);
                                        //if (modelDeviceAssociatedUsers != null)
                                        //{
                                        //    var modelDeviceBindLogs = new DeviceBindLogs
                                        //    {
                                        //        DeviceName = DeviceCode,
                                        //        Operating = $"设备重置解绑-队列",
                                        //        DType = 2
                                        //    };
                                        //    modelDeviceBindLogs.SaveAsync();
                                            
                                        //    var list = new List<String> { modelDeviceAssociatedUsers.IdentityId };

                                        //    if (!modelDeviceAssociatedUsers.SubUsers.IsNullOrWhiteSpace())
                                        //    {
                                        //        var SubUsers = modelDeviceAssociatedUsers.SubUsers.Trim(',').Split(',');
                                        //        SubUsers.ForEach(list.Add);
                                        //    }

                                        //    var searchList = AppUser.FindAllByIdentityId(list);  // 获取所有与设备有关的用户
                                        //    foreach (var item in searchList)
                                        //    {
                                        //        var hashDevices = item.Devices?.Split(',').ToList();
                                        //        hashDevices ??= [];
                                        //        var listProductKeys = item.ProductKeys?.Split(',').ToList();
                                        //        listProductKeys ??= [];

                                        //        var index = hashDevices.IndexOf(DeviceCode);
                                        //        if (index != -1)
                                        //        {
                                        //            hashDevices.RemoveAt(index);
                                        //            listProductKeys.RemoveAt(index);

                                        //            item.Devices = hashDevices.Join();
                                        //            item.ProductKeys = listProductKeys.Join();
                                        //            item.Update();
                                        //        }
                                        //    }

                                        //    modelDeviceAssociatedUsers.Delete();
                                        //}

                                        dv.Online = false;
                                        dv.Update();

                                        var rs1 = new
                                        {
                                            DeviceName = DeviceCode,
                                            Status = 99,
                                            Time = UnixTime.ToTimestamp()
                                        };
                                        _ = PublishAsync($"sys/{ProductKey}/{DeviceCode}/thing/status/update", rs1, true);

                                        replyModel.Status = ServiceStatus.已完成;
                                        _queue.PublishReply(replyModel);
                                    }
                                }
                            }
                            break;

                        case "network":  // 配网
                            {
                                var options = new JsonDocumentOptions
                                {
                                    AllowTrailingCommas = true
                                };

                                var data = msg.InputData.SplitAsDictionary();
                                var Msg = data["Msg"];
                                var DeviceCode = data["DeviceCode"];
                                var ProductKey = data["ProductKey"];
                                var dv = data["Dv"].ToJsonEntity<Device>();
                                var ServiceId = data["Id"].ToLong();

                                var replyModel = new ServiceReplyModel { Id = ServiceId };

                                using (JsonDocument document = JsonDocument.Parse(Msg, options))
                                {
                                    var jsonElement = document.RootElement;
                                    var Reply = jsonElement.GetProperty("Reply").GetInt32();

                                    if (Reply == 1)
                                    {
                                        //var modelDeviceAssociatedUsers = DeviceAssociatedUsers.FindByDeviceName(DeviceCode);
                                        //if (modelDeviceAssociatedUsers != null)
                                        //{
                                        //    var modelDeviceBindLogs = new DeviceBindLogs
                                        //    {
                                        //        DeviceName = DeviceCode,
                                        //        Operating = $"设备配网解绑-队列",
                                        //        DType = 2
                                        //    };
                                        //    modelDeviceBindLogs.SaveAsync();

                                        //    var list = new List<String> { modelDeviceAssociatedUsers.IdentityId };

                                        //    if (!modelDeviceAssociatedUsers.SubUsers.IsNullOrWhiteSpace())
                                        //    {
                                        //        var SubUsers = modelDeviceAssociatedUsers.SubUsers.Trim(',').Split(',');
                                        //        SubUsers.ForEach(list.Add);
                                        //    }

                                        //    var searchList = AppUser.FindAllByIdentityId(list);  // 获取所有与设备有关的用户
                                        //    foreach (var item in searchList)
                                        //    {
                                        //        var hashDevices = item.Devices?.Split(',').ToList();
                                        //        hashDevices ??= [];
                                        //        var listProductKeys = item.ProductKeys?.Split(',').ToList();
                                        //        listProductKeys ??= [];

                                        //        var index = hashDevices.IndexOf(DeviceCode);
                                        //        if (index != -1)
                                        //        {
                                        //            hashDevices.RemoveAt(index);
                                        //            listProductKeys.RemoveAt(index);

                                        //            item.Devices = hashDevices.Join();
                                        //            item.ProductKeys = listProductKeys.Join();
                                        //            item.Update();
                                        //        }
                                        //    }

                                        //    modelDeviceAssociatedUsers.Delete();
                                        //}

                                        dv.Online = false;
                                        dv.Update();

                                        var rs1 = new
                                        {
                                            DeviceName = DeviceCode,
                                            Status = 99,
                                            Time = UnixTime.ToTimestamp()
                                        };
                                        _ = PublishAsync($"sys/{ProductKey}/{DeviceCode}/thing/status/update", rs1, true);

                                        replyModel.Status = ServiceStatus.已完成;
                                        _queue.PublishReply(replyModel);
                                    }
                                }
                            }
                            break;

                        case "usertransmission":  // 给用户发指令
                            {
                                var data = msg.InputData.ToJsonEntity<UserTransmissionModel>();
                                XTrace.WriteLine($"给用户发指令：{msg.InputData}");

                                var topic = $"sys/user/{data.IdentityId}/event/set";

                                await PublishAsync(topic, data, true).ConfigureAwait(false);
                            }
                            break;
                    }
                }
                else
                {
                    await Task.Delay(100, cancellationToken).ConfigureAwait(false);
                }
                span?.Dispose();
            }
        }
        catch (TaskCanceledException) { }
        catch (Exception ex)
        {
            XTrace.WriteException(ex);
        }
        finally
        {
            source.Cancel();
        }
    }

    private async Task ConsumeMessage(Device device, String ip, CancellationTokenSource source)
    {
        DefaultSpan.Current = null;
        var cancellationToken = source.Token;
        var queue = _queue.GetQueue(device.Code);

        var mqMsg = String.Empty;
        try
        {
            while (!cancellationToken.IsCancellationRequested && !Session.Disposed)
            {
                ISpan span = null;
                mqMsg = await queue.TakeOneAsync(30, cancellationToken).ConfigureAwait(false);
                if (mqMsg != null)
                {
                    // 埋点
                    span = Tracer?.NewSpan($"mqtt:ServiceQueue", mqMsg);
                    if (Log != null && Log.Level <= LogLevel.Debug) WriteLog("消费到下发指令消息：{0}", mqMsg);

                    // 解码
                    var dic = JsonParser.Decode(mqMsg);
                    span?.Detach(dic);

                    if (dic.TryGetValue("Type", out object value))
                    {
                        var type = value.SafeString();
                        XTrace.WriteLine($"消费到下发指令消息ConsumeMessage：{mqMsg}:{Session.ID}");

                        //if (Forward)
                        //{
                        //    _queue.Publish(device.Code, mqMsg);
                        //    XTrace.WriteLine($"转发消息：{Session.ID}:{mqMsg}");
                        //    return;
                        //}

                        switch (type)
                        {
                            case null:
                            case "":  // 服务队列
                                {
                                    var msg = JsonHelper.Convert<ServiceModel>(dic);

                                    if (msg == null || msg.Id == 0 || msg.Expire.Year > 2000 && msg.Expire < DateTime.Now)
                                        _deviceService.WriteHistory(device, "Mqtt发送", false, "消息无效。" + mqMsg, ip);
                                    else
                                    {
                                        _deviceService.WriteHistory(device, "Mqtt发送", true, mqMsg, ip);

                                        // 向客户端传递埋点信息，构建完整调用链
                                        msg.TraceId = span + "";

                                        var log = DeviceServiceLog.FindById(msg.Id);
                                        if (log != null)
                                        {
                                            if (log.TraceId.IsNullOrEmpty()) log.TraceId = span?.TraceId;
                                            log.Status = ServiceStatus.处理中;
                                            log.Update();
                                        }

                                        var topic = $"sys/{device.Product.Code}/{device.Code}/thing/service/post";
                                        var data = msg.ToDictionary();
                                        data["id"] = msg.Id.ToString();

                                        data.Remove("Type");

                                        //if (!msg.InputData.IsNullOrWhiteSpace() && msg.InputData.)
                                        //{
                                        //    var inputData = msg?.InputData;
                                        //    if (!inputData.IsNullOrWhiteSpace())
                                        //    {
                                        //        var s = JsonParser.Decode(inputData);
                                        //        data["InputData"] = s;
                                        //    }
                                        //}

                                        await PublishAsync(topic, data).ConfigureAwait(false);
                                    }
                                }

                                break;

                            case "Upgrade":  // 固件升级推送
                                {
                                    if (NewLife.Setting.Current.Debug)
                                        XTrace.WriteLine($"固件升级数据：{mqMsg}");

                                    var msg = JsonHelper.Convert<ServiceModel>(dic);

                                    if (msg == null || msg.Expire.Year > 2000 && msg.Expire < DateTime.Now)
                                        _deviceService.WriteHistory(device, "Mqtt发送", false, "消息无效。" + mqMsg, ip);
                                    else
                                    {
                                        _deviceService.WriteHistory(device, "Mqtt发送", true, mqMsg, ip);

                                        // 向客户端传递埋点信息，构建完整调用链
                                        msg.TraceId = span + "";

                                        var topic = $"sys/{device.Product.Code}/{device.Code}/thing/ota/get";

                                        var data = msg.InputData.SplitAsDictionary();
                                        var ProductKey = data["ProductKey"];
                                        var DeviceCode = data["DeviceCode"];

                                        var rs = new
                                        {
                                            MsgId = data["MsgId"].ToInt(),
                                            FId = data["FId"].ToInt(),
                                            Version = data["Version"],
                                            Source = data["Source"],
                                            FileHash = data["FileHash"],
                                            Executor = data["Executor"],
                                            Force = data["Force"].ToBool(),
                                            Description = data["Description"],
                                            FileSize = data["FileSize"].ToLong()
                                        };

                                        await PublishAsync(topic, rs).ConfigureAwait(false);
                                    }
                                }
                                break;

                            case "Reset":  // 设备重置
                                {
                                    var msg = JsonHelper.Convert<ServiceModel>(dic);
                                    if (msg == null || msg.Expire.Year > 2000 && msg.Expire < DateTime.Now)
                                        _deviceService.WriteHistory(device, "Mqtt发送", false, "消息无效。" + mqMsg, ip);
                                    else
                                    {
                                        _deviceService.WriteHistory(device, "Mqtt发送", true, mqMsg, ip);

                                        // 向客户端传递埋点信息，构建完整调用链
                                        msg.TraceId = span + "";

                                        var topic = $"sys/{device.Product.Code}/{device.Code}/thing/event/reset_reply";

                                        var data = msg.InputData.SplitAsDictionary();
                                        var DeviceReset = data["DeviceReset"];
                                        var ConfigClean = data["ConfigClean"];

                                        var rs = new
                                        {
                                            msg.Id,
                                            DeviceReset = DeviceReset.ToInt(),
                                            ConfigClean = ConfigClean.ToInt(),
                                            Time = UnixTime.ToTimestamp()
                                        };

                                        await PublishAsync(topic, rs).ConfigureAwait(false);
                                    }
                                }
                                break;

                            case "Reboot":  // 设备重启
                                {
                                    var msg = JsonHelper.Convert<ServiceModel>(dic);
                                    if (msg == null || msg.Expire.Year > 2000 && msg.Expire < DateTime.Now)
                                        _deviceService.WriteHistory(device, "Mqtt发送", false, "消息无效。" + mqMsg, ip);
                                    else
                                    {
                                        _deviceService.WriteHistory(device, "Mqtt发送", true, mqMsg, ip);

                                        // 向客户端传递埋点信息，构建完整调用链
                                        msg.TraceId = span + "";

                                        var topic = $"sys/{device.Product.Code}/{device.Code}/thing/event/reset_reply";

                                        var data = msg.InputData.SplitAsDictionary();
                                        var DeviceReset = data["DeviceReset"];
                                        var ConfigClean = data["ConfigClean"];

                                        var rs = new
                                        {
                                            msg.Id,
                                            DeviceReset = DeviceReset.ToInt(),
                                            ConfigClean = ConfigClean.ToInt(),
                                            Time = UnixTime.ToTimestamp()
                                        };

                                        await PublishAsync(topic, rs).ConfigureAwait(false);
                                    }
                                }
                                break;

                            case "Network":  // 设备配网
                                {
                                    var msg = JsonHelper.Convert<ServiceModel>(dic);
                                    if (msg == null || msg.Expire.Year > 2000 && msg.Expire < DateTime.Now)
                                        _deviceService.WriteHistory(device, "Mqtt发送", false, "消息无效。" + mqMsg, ip);
                                    else
                                    {
                                        _deviceService.WriteHistory(device, "Mqtt发送", true, mqMsg, ip);

                                        // 向客户端传递埋点信息，构建完整调用链
                                        msg.TraceId = span + "";

                                        var topic = $"sys/{device.Product.Code}/{device.Code}/thing/event/reset_reply";

                                        var data = msg.InputData.SplitAsDictionary();
                                        var DeviceReset = data["DeviceReset"];
                                        var ConfigClean = data["ConfigClean"];

                                        var rs = new
                                        {
                                            msg.Id,
                                            DeviceReset = DeviceReset.ToInt(),
                                            ConfigClean = ConfigClean.ToInt(),
                                            Time = UnixTime.ToTimestamp()
                                        };

                                        await PublishAsync(topic, rs).ConfigureAwait(false);
                                    }
                                }
                                break;

                            case "Transmission":  // 透传 单属性
                                {
                                    try
                                    {
                                        var msg = JsonHelper.Convert<TransmissionModel>(dic);
                                        if (msg == null || msg.Expire.Year > 2000 && msg.Expire < DateTime.Now)
                                            _deviceService.WriteHistory(device, "Mqtt发送", false, "消息无效。" + mqMsg, ip);
                                        else
                                        {
                                            _deviceService.WriteHistory(device, "Mqtt发送", true, mqMsg, ip);

                                            // 向客户端传递埋点信息，构建完整调用链
                                            msg.TraceId = span + "";

                                            var topic = $"sys/{device.Product.Code}/{device.Code}/thing/property/set";

                                            var data = msg.InputData;

                                            var rs = new TransmissionModel
                                            {
                                                Id = msg.Id,
                                                Name = "SetProperty",
                                                InputData = data,
                                                DeviceCode = device.Code,
                                                // 向客户端传递埋点信息，构建完整调用链
                                                TraceId = msg.TraceId,
                                            };

                                            await PublishAsync(topic, rs).ConfigureAwait(false);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        XTrace.WriteException(ex);
                                    }
                                }
                                break;

                            case "Transmissions":  // 透传 多属性
                                {
                                    try
                                    {
                                        var msg = JsonHelper.Convert<TransmissionsModel>(dic);
                                        if (msg == null || msg.Expire.Year > 2000 && msg.Expire < DateTime.Now)
                                            _deviceService.WriteHistory(device, "Mqtt发送", false, "消息无效。" + mqMsg, ip);
                                        else
                                        {
                                            _deviceService.WriteHistory(device, "Mqtt发送", true, mqMsg, ip);

                                            // 向客户端传递埋点信息，构建完整调用链
                                            msg.TraceId = span + "";

                                            var topic = $"sys/{device.Product.Code}/{device.Code}/thing/property/set";

                                            var data = msg.InputData;

                                            var rs = new TransmissionsModel
                                            {
                                                Id = msg.Id,
                                                Name = "SetProperty",
                                                InputData = data,
                                                DeviceCode = device.Code,
                                                // 向客户端传递埋点信息，构建完整调用链
                                                TraceId = msg.TraceId,
                                            };

                                            await PublishAsync(topic, rs).ConfigureAwait(false);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        XTrace.WriteException(ex);
                                    }
                                }
                                break;

                            //case "CloseQueue":
                            //    {
                            //        var SId = dic["SessionId"].ToInt();
                            //        XTrace.WriteLine($"处理队列：{Session.ID}:{SId}:{dic["DeviceCode"]}");

                            //        if (SId != Session.ID)
                            //        {
                            //            Forward = true;
                            //        }
                            //    }
                            //    break;
                        }
                    }
                }
                else
                {
                    await Task.Delay(100, cancellationToken).ConfigureAwait(false);
                }
                span?.Dispose();
            }
        }
        catch (TaskCanceledException) { }
        catch (Exception ex)
        {
            XTrace.WriteLine($"队列报错了。");
            XTrace.WriteException(ex);
        }
        finally
        {
            source.Cancel();
        }
    }
    #endregion

    #region 辅助
    ///// <summary>写日志</summary>
    ///// <param name="format"></param>
    ///// <param name="args"></param>
    //private void WriteLog(String format, params Object[] args) => Log?.Info($"[MqttServer]{format}", args);
    private static String doSign(String toSignString, String secret)
    {
        var secretKey = Encoding.UTF8.GetBytes(secret);
        var hamc = new HMACSHA256(secretKey);
        hamc.Initialize();

        var bytes = Encoding.UTF8.GetBytes(toSignString);
        var rawHamc = hamc.ComputeHash(bytes);
        return rawHamc.ToHex().ToLower();
    }
    #endregion

    //public override void Close(string reason)
    //{
    //    _source?.Cancel();

    //    XTrace.WriteLine($"关闭了吗？Close:{reason}");

    //    base.Close(reason);
    //}
}