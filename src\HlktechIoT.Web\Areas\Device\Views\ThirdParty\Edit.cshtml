﻿@{
    Html.AppendTitleParts(T("编辑合作公司").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        padding-top: 30px;
        padding-left: 30px;
    }

    .layui-form-item.btn {
        padding-top: 10px;
        padding-left: 115px;
    }

    .label-width span {
        color: #f00;
    }

    .layui-form-label {
        width: 94px;
    }

    .upload {
        cursor: pointer;
        max-width: 120px;
        max-height: 90px;
    }
    .select{
        max-width: 420px !important;
        border: 1px solid #e6e6e6;
    }
    .uploadImage{
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
    #uploadDemoView{
        position: relative;
    }
    .close{
        content: "×";
        position: absolute;
        top: 10px;
        right: 0;
        width: 20px;
        height: 20px;
        font-size: 20px;
        line-height: 20px;
        color: #009688;
        background-color: rgba(0, 0, 0, 0.2);
        text-align: center;
        border-radius: 2px;
        cursor: pointer;
        z-index: 1000000;
        transition: all 1s;
    }
    .close:hover{
        color: red;
        transform: scale(1.2);
    }
</style>
<div class="containers">
    <form class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("公司名称")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="Name" value="@Model.Name" placeholder="@T("请输入公司名称")" autocomplete="off" class="layui-input" lay-filter="Versions">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("商户号")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="AccessId" value="@Model.AccessId" placeholder="@T("请输入商户号")" autocomplete="off" class="layui-input" lay-filter="Versions" disabled>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("商户密钥")</label>
            <div class="layui-input-inline" style="min-width:270px">
                <input type="text" name="AccessKey" value="@Model.AccessKey" id="AccessKey" placeholder="@T("请输入商户密钥")" autocomplete="off" class="layui-input" lay-filter="Versions" >
            </div>
            <input type="hidden" name="people" value="" />
            <div class="layui-input-inline" style="width:80px;">
                <button type="button" class="layui-btn layui-btn-normal butsj">@T("生成密钥")</button>
            </div>
            <div class="layui-form-mid layui-word-aux"></div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("第三方服务器地址")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="OfficialUrl" value="@Model.OfficialUrl" placeholder="@T("请输入第三方服务器地址")"autocomplete="off" class="layui-input" lay-filter="Versions" >
            </div>
        </div>

        <div class="layui-form-item">
             <label class="layui-form-label">@T("是否启用")</label>
             <div class="layui-input-inline">
                <input type="checkbox" lay-filter="switch" name="Enabled" lay-skin="switch" lay-text="@T("是")|@T("否")"  @(Model.Enabled ? Html.Raw("checked") : "")>
             </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("联系人")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="UName" value="@Model.UName" placeholder="@T("请输入联系人")" autocomplete="off" class="layui-input" lay-filter="Versions" >
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("联系电话")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="Phone" value="@Model.Phone" placeholder="@T("请输入联系电话")" autocomplete="off" class="layui-input" lay-filter="Versions" >
            </div>
        </div>

         <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("所属销售")</label>
            <div class="layui-input-inline" style="width: 300px;">
                <div id="demo1" style=" width: 100%;"></div>
            <input type="text" name="UId" value="@Model.UId" style="display:none" />
            </div>
        </div>

        <div class="layui-form-item btn">
            <input type="hidden" value="@Model.Id" name="ID" />
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" value="@T("保存")" />
        </div>
    </form>
</div>

<script asp-location="Footer">
    var data1 = $.parseJSON('@Html.Raw(ViewBag.PList)');
    var demo1 = xmSelect.render({
        el: '#demo1',
        radio: true,
        paging: true,
        pageSize: 10,
        filterable: true,
        pageEmptyShow: false,
        clickClose: true,
        data: data1,
        on: function (data) {
            if (data.arr.length == 0) {
                $("[name=UId]").val("");
            }
            else {
                $("[name=UId]").val(data.arr[0].value);
            }
        },
    });
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        form.on('submit(Submit)', function (data) {
             if (data.field.Name.length == 0) {
                abp.notify.warn("@T("公司名称不能为空")");
                return;
            }
            if (data.field.AccessId == '' || data.field.AccessId == null || data.field.AccessId == undefined ||  data.field.AccessId  == 'undefined ') {
                abp.notify.warn("@T("商户号不能为空")");
                return;
            }
            if (data.field.AccessKey == '' || data.field.AccessKey == null || data.field.AccessKey == undefined ||  data.field.AccessKey  == 'undefined ') {
                abp.notify.warn("@T("商户密钥不能为空")");
                return;
            }
            var field = data.field;

            var waitIndex = parent.layer.load(2);
            abp.ajax({
                url: "@Url.Action("EditThirdParty")",
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                data: field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                dg.reload('tables');
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });
         $(".butsj").click(() => {
                    var len = 32
                    var rdmString = "";
                    for (; rdmString.length < len; rdmString += Math.random().toString(36).substr(2));
                    $("#AccessKey").val(rdmString.substr(0, len));
                })
    });
</Script>