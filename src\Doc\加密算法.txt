1、签名算法
假设传送的参数如下：
header:
Lng: cn/en  (可空)
Id:  惟一值。请求标识值
Nonce：随机数
TimeStamp：13位时间戳（带毫秒）
AccessId：设备DeviceName

"accessId":"yhWO3VWA00N","sn":"HLK-Admin0000000006","page":1,"pageSize":20,"nonceStr":"H8NDPR","timeStamp":1720686710661

1). 对指定参数按照key=value的格式，并按照参数名ASCII字典序排序得到字符串A：    	stringA="accessId=yhWO3VWA00N&nonceStr=H8NDPR&timeStamp=1720686710661";

2). 拼接API密钥，得到字符串B：
	stringB=stringA+"&key=X9jrVZ8RRE2fXpRw";

3). 以md5方式加密字符串B，并将所有字符大写，得到最后的签名串： 
sign=md5(stringB).toUpperCase()="6DB5A598BB2A056F33D04BC3E17D8BB9";

注意：
1)、timeStamp字段存在时效性，目前为2分钟,且同一接口校验值只能用一次。时间戳要使用毫秒级的。
2)、参考签名算法的只有accessId、nonceStr和timeStamp三个字段

2、接口

1)、模组固件升级
主题：/thing/ota/get
Source值如果是文件的直接下载文件，如果不是文件而是接口的，则以Post的方式请求Source接口，参数如下
header:
Lng: cn/en  (可空)
Id:  惟一值。请求标识值
Nonce：随机数
TimeStamp：13位时间戳（带毫秒）
AccessId：设备DeviceName
Signature:   加密值

body表单:
Code String  对应的升级Id和固件Id。格式为收到数据的{MsgId}_{FId}
Start  Int64    起始位置
Size   Int64    长度