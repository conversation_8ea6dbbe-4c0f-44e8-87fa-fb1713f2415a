﻿using HlktechIoT.Core.Models;
using HlktechIoT.IoTClientMqttTest;

using NewLife;
using NewLife.Data;
using NewLife.IoT.ThingModels;
using NewLife.Log;
using NewLife.MQTT;
using NewLife.MQTT.Messaging;
using NewLife.Security;
using NewLife.Serialization;
using NewLife.Threading;

using System.Diagnostics;
using System.Net;
using System.Net.NetworkInformation;

using ServiceModel = NewLife.IoT.ThingModels.ServiceModel;

namespace HlktechIoT.IoTClientMqtt;

/// <summary>MQTT协议设备</summary>
public class MqttDevice {
    #region 属性
    /// <summary>服务器地址</summary>
    public String? Server { get; set; }

    /// <summary>设备编码。从IoT管理平台获取（需提前分配），或者本地提交后动态注册</summary>
    public String? DeviceCode { get; set; }

    /// <summary>密钥。设备密钥或产品密钥，分别用于一机一密和一型一密，从IoT管理平台获取</summary>
    public String? Secret { get; set; }

    /// <summary>产品编码。从IoT管理平台获取</summary>
    public String? ProductKey { get; set; }

    /// <summary>项目编码。从IoT管理平台获取</summary>
    public String? ProjectKey { get; set; }

    /// <summary>密码散列提供者。避免密码明文提交</summary>
    public IPasswordProvider PasswordProvider { get; set; } = new SaltPasswordProvider { Algorithm = "md5", SaltTime = 60 };

    /// <summary>链路追踪器</summary>
    public ITracer? Tracer { get; set; }

    public MqttClient? _mqttClient;
    private TimerX? _timerPost;
    private TimerX? _timerPing;
    private Int32 _delay;
    #endregion

    /// <summary>
    /// 登录
    /// </summary>
    /// <param name="inf"></param>
    /// <returns></returns>
    public async Task LoginAsync()
    {
        var mi = MachineInfo.GetCurrent();
        var ip = NetHelper.MyIP() + "";
        var client = new MqttClient
        {
            Server = Server,
            ClientId = $"{ProductKey}|{ClientSettings.Current.ProjectKey}|{ClientSettings.Current.DType}|{DeviceCode}|{ClientSettings.Current.DType}",
            UserName = $"{DeviceCode}|{DateTime.UtcNow.ToLong()}|{ip}|{mi.UUID}",
            Password = PasswordProvider.Hash(Secret!),
            KeepAlive = 60,

            Tracer = Tracer,
            Log = XTrace.Log
        };

#if DEBUG
        client.Log.Level = LogLevel.Debug;
#endif

        XTrace.WriteLine($"打印连接参数    Server:{client.Server} ClientId:{client.ClientId} UserName:{client.UserName}  Password:{client.Password}");

        client.Received += OnReceived;

        var res = await client.ConnectAsync().ConfigureAwait(false);

        // 订阅感兴趣的主题
        await client.SubscribeAsync(new String[]
        {
            $"sys/{ProductKey}/{DeviceCode}/thing/event/ping/post_reply",
            $"sys/{ProductKey}/{DeviceCode}/thing/property/post_reply",
            $"sys/{ProductKey}/{DeviceCode}/thing/service/property/set",
            $"device/{ProductKey}/{DeviceCode}/upgrade",
            $"sys/{ProductKey}/{DeviceCode}/thing/status/update",
        }).ConfigureAwait(false);

        _mqttClient = client;

        //_timerPost = new TimerX(async s => await PostDataAsync().ConfigureAwait(false), null, 3_000, 60_000, "Device") { Async = true };
        _timerPing = new TimerX(async s => await PingAsync().ConfigureAwait(false), null, 1_000, 60_000, "Device") { Async = true };
    }

    /// <summary>心跳</summary>
    /// <returns></returns>
    public virtual async Task PingAsync()
    {
        if (Tracer != null) DefaultSpan.Current = null;
        using var span = Tracer?.NewSpan("Ping");
        try
        {
            var inf = GetHeartInfo();

            var publishTopic = $"sys/{ProductKey}/{DeviceCode}/thing/event/ping/post";
            await _mqttClient.PublishAsync(publishTopic, inf).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            span?.SetError(ex, null);

            throw;
        }
    }

    /// <summary>获取心跳信息</summary>
    public PingInfo GetHeartInfo()
    {
        var mi = MachineInfo.GetCurrent();
        mi.Refresh();

        var properties = IPGlobalProperties.GetIPGlobalProperties();
        var connections = properties.GetActiveTcpConnections();

        var mcs = NetHelper.GetMacs().Select(e => e.ToHex("-")).OrderBy(e => e).Join(",");
        var driveInfo = new DriveInfo(Path.GetPathRoot(".".GetFullPath()));
        var ip = NetHelper.GetIPs().Where(ip => ip.IsIPv4() && !IPAddress.IsLoopback(ip) && ip.GetAddressBytes()[0] != 169).Join();
        var ext = new PingInfo
        {
            Memory = mi.Memory,
            AvailableMemory = mi.AvailableMemory,
            TotalSize = (UInt64)driveInfo.TotalSize,
            AvailableFreeSpace = (UInt64)driveInfo.AvailableFreeSpace,
            CpuRate = (Single)Math.Round(mi.CpuRate, 4),
            Temperature = mi.Temperature,
            Battery = mi.Battery,
            Uptime = Environment.TickCount / 1000,

            IP = ip,

            Time = DateTime.UtcNow.ToLong(),
            Delay = _delay,

            Module = "Test",
            Version = "TestV1.0",
        };
        // 开始时间 Environment.TickCount 很容易溢出，导致开机24天后变成负数。
        // 后来在 netcore3.0 增加了Environment.TickCount64
        // 现在借助 Stopwatch 来解决
        if (Stopwatch.IsHighResolution) ext.Uptime = (Int32)(Stopwatch.GetTimestamp() / Stopwatch.Frequency);

        return ext;
    }

    public async Task PostDataAsync()
    {
        if (Tracer != null) DefaultSpan.Current = null;

        using var span = Tracer?.NewSpan("PostData");
        try
        {
            var items = new List<DataModel>();
            items.Add(new DataModel
            {
                Time = DateTime.UtcNow.ToLong(),
                Name = "TestValue",
                Value = Rand.Next(0, 100) + ""
            });

            var data = new DataModels { DeviceCode = DeviceCode, Items = items.ToArray() };

            await _mqttClient.PublishAsync($"sys/{ProductKey}/{DeviceCode}/thing/property/post", data).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            span?.SetError(ex, null);

            throw;
        }
    }

    private void OnReceived(Object? sender, EventArgs<PublishMessage> e)
    {
        var topic = e.Arg.Topic;
        var message = e.Arg.Payload?.ToStr();

        _mqttClient.WriteLog($"收到: topic={topic} message={message}");

        //var obj = message.ToJsonEntity<Dictionary<String, Object>>();

        if (topic == $"sys/{ProductKey}/{DeviceCode}/thing/event/ping/post_reply")
        {
            var rs = message.ToJsonEntity<HlktechIoT.Core.Models.PingResponse>();
            if (rs != null)
            {
                var dt = rs.Time.ToDateTime();
                if (dt.Year > 2000)
                {
                    // 计算延迟
                    var ts = DateTime.UtcNow - dt;
                    var ms = (Int32)ts.TotalMilliseconds;
                    _delay = (_delay + ms) / 2;
                }
            }
        }
        else if (topic == $"device/{ProductKey}/{DeviceCode}/upgrade")
        {
            //var data = message.ToJsonEntity<UpgradeInfo>();

            // 升级
            //UpgradeReply(data);
        }
        else if (topic == $"sys/{ProductKey}/{DeviceCode}/thing/property/post_reply")
        {
            // 属性设置
        }
        else if (topic == $"sys/{ProductKey}/{DeviceCode}/thing/service/post")
        {
            // 服务调用
            var data = message.ToJsonEntity<ServiceModel>();
            _ = ServiceReply(data);
        }
    }

    /// <summary>
    /// 服务调用
    /// </summary>
    /// <param name="data"></param>
    private async Task ServiceReply(ServiceModel data)
    {
        // 执行服务...
        _mqttClient.WriteLog("执行[{0}]: {1}", data.Name, data.InputData);

        // 返回执行结果
        var topic = $"sys/{ProductKey}/{DeviceCode}/thing/service/post_reply";

        var replyData = new ServiceReplyModel
        {
            Id = data.Id,
            Status = ServiceStatus.已完成,
            Data = "ok"
        };

        await _mqttClient.PublishAsync(topic, replyData).ConfigureAwait(false);
    }
}