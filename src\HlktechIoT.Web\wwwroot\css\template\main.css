* {
    margin: 0;
    padding: 0;
    list-style: none;
    font-family: 微软雅黑, sans-serif;
    outline: none;
    color: #555;
}

body .toast-message{
    color:white !important;
}

textarea::placeholder,
input::placeholder {
    color: #777;
}

/* 头部样式 */
.header {
    height: 0vh;
}

/* 工具栏 */
.toolbar-list {
    flex-basis: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #ccc;
}

    .toolbar-list * {
        font-size: 0.7em;
    }

.toolbar-list-left {
    display: flex;
    justify-content: flex-start;
    align-items: stretch;
    padding-left: 10px;
}

.toolbar-list-rig {
    display: flex;
    justify-content: flex-end;
    align-items: stretch;
    padding-right: 10px;
}

.toolbar-list .name-li .tmplate-moreset {
    z-index: 1;
    position: absolute;
    top: calc(100% + 10px);
    left: -40px;
    box-shadow: 1px 1px 4px #999;
    display: none;
    padding: 5px;
    border-radius: 3px;
    background-color: white;
}

    .toolbar-list .name-li .tmplate-moreset textarea {
        width: 300px;
        min-height: 100px;
        resize: none;
        border-color: #bbb;
        padding: 3px;
        box-sizing: border-box;
    }

    .toolbar-list .name-li .tmplate-moreset .title {
        margin-bottom: 5px;
    }

.toolbar-list li {
    margin-right: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    user-select: none;
    position: relative;
}

    .toolbar-list li button,
    .toolbar-list li input,
    .toolbar-list li select {
        background-color: transparent;
        border: 1px solid #ccc;
        border-radius: 3px;
        box-sizing: border-box;
        height: 25px;
    }

    .toolbar-list li input {
        padding-left: 5px;
        position: relative;
    }

        .toolbar-list input::-webkit-outer-spin-button,
        .toolbar-list li input::-webkit-inner-spin-button {
            -webkit-appearance: none;
        }

        .toolbar-list li input[type="number"] {
            -moz-appearance: none;
        }


    .toolbar-list li button {
        cursor: pointer;
        width: 60px;
    }

        .tbtn:hover,
        .toolbar-list li button:hover {
            color: deepskyblue;
            border-color: deepskyblue;
        }

/* 主体区域 */
main {
    display: flex;
    justify-content: space-between;
    align-items: stretch;
    flex-direction: column;
    height: 100vh;
}

.main-body {
    display: flex;
    justify-content: space-between;
    align-items: stretch;
    flex-grow: 1;
}

    .main-body .contro-panel {
        flex-basis: 280px;
        padding: 0px 5px;
    }

        .main-body .contro-panel li {
            font-size: 0.8em;
            box-sizing: border-box;
        }

        .main-body .contro-panel .title {
            font-size: 0.7em;
            /* border-bottom: 1px solid #ccc; */
            color: #666;
        }

        .main-body .contro-panel .contro-box .contro-list {
            margin-top: 5px;
        }

            .main-body .contro-panel .contro-box .contro-list .contro-item {
                display: inline-block;
                border-radius: 3px;
                height: 20px;
                line-height: 20px;
                padding: 0 8px;
                border: 1px solid #ccc;
                cursor: pointer;
                user-select: none;
                margin-bottom: 10px;
                margin-right: 8px;
                width: 70px;
                text-align: center;
                white-space: nowrap;
            }

        /* 集合面板 */
        .main-body .contro-panel .list-box {
            min-height: 500px;
        }

            .xbtn,
            .main-body .contro-panel .list-box li {
                display: block;
                height: 25px;
                line-height: 25px;
                padding-left: 5px;
                border: 1px solid #ccc;
                border-radius: 3px;
                margin-bottom: 2px;
                background-color: transparent;
                cursor: pointer;
                text-align: center;
            }

                .xbtn,
                .main-body .contro-panel .list-box li:hover {
                    background-color: #eee;
                    border-color: #aaa;
                }

            .main-body .contro-panel .list-box .active {
                background-color: #ddd;
                border: 1px solid rgb(211, 210, 210);
            }

                .main-body .contro-panel .list-box .active:hover {
                    background-color: #ddd;
                    border: 1px solid rgb(211, 210, 210);
                }

    /* 设计区 */
    .main-body .view-panel {
        flex-grow: 1;
        background-color: #eee;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        padding: 20px;
        overflow: hidden;
    }

#view {
    width: 296px;
    height: 128px;
    position: relative;
    border: 1px solid #aaa;
    background-color: white;
    overflow: hidden;
    box-sizing: border-box;
}

/* 属性面板 */
.main-body .prop-panel {
    flex-basis: 315px;
    padding: 5px;
}

.setting-block {
    /* border: 1px solid #ccc; */
    border-radius: 5px;
    padding: 5px;
    box-shadow: 0 0 5px #ccc;
    background-color: #fff;
    margin-bottom: 20px;
}

    .setting-block::before {
        content: attr(tit);
        font-size: 0.9em;
        height: 20px;
        /* background-color: rgb(237, 236, 236); */
        text-align: left;
        padding-left: 5px;
        display: block;
        margin-bottom: 5px;
        border-bottom: 1px solid rgb(196, 193, 193);
    }

.prop-group .input-block {
    display: flex;
    width: auto;
    min-height: 40px;
    line-height: 40px;
    align-items: center;
}

.prop-group .input-block-inline {
    display: inline-flex;
    margin-right: 5px;
}

.prop-group .input-block > label {
    width: 60px;
    text-align: right;
    box-sizing: border-box;
    padding-right: 3px;
    /* font-weight: 100; */
    font-size: 0.7em;
}

.prop-group .input-textarea-block {
    flex-direction: column;
    align-items: flex-start;
    justify-content: start;
}

    .prop-group .input-textarea-block > label {
        text-align: left;
        height: 20px;
        line-height: 20px;
    }

.prop-group .input-block select,
.prop-group .input-block .prop-ipt {
    display: inline-block;
    box-sizing: border-box;
    width: 60px;
}

.prop-group .input-block input[type="number"] {
    display: inline-block;
    box-sizing: border-box;
    width: 60px;
    font-size: 0.7em;
}

.prop-group .input-block input[type="checkbox"] {
    text-align: left;
    width: 18px;
    height: 18px;
    border: 1px solid #aaa;
    border-radius: 3px;
    background-color: #fff;
    cursor: pointer;
    margin-right: 5px;
}

.prop-group .input-block input[type="number"]::-webkit-outer-spin-button,
.prop-group .input-block input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
}

.prop-group .input-block .prop-ipt-textArea {
    display: block;
    width: auto;
    height: 60px;
    font-size: 00.8em;
    width: 100%;
    border: 1px solid rgb(157, 155, 155);
    border-radius: 3px;
    font-size: 0.8em;
    padding: 3px;
    box-sizing: border-box;
    resize: none;
}



/*图像控件快速选择*/
.main-body .imgfile-upper {
    display: flex;
    justify-content: flex-start;
    align-items: stretch;
    height: 25px;
    line-height: 25px;
    width: 100%;
}

    .main-body .imgfile-upper .img-linkipt {
        flex-grow: 1;
        padding-left: 3px;
    }

        .main-body .imgfile-upper .img-linkipt::placeholder {
            font-size: 0.8em;
        }

    .main-body .imgfile-upper .img-upbtn {
        display: inline-block;
        height: inherit;
        line-height: inherit;
        border: 1px solid #ccc;
        border-radius: 3px;
        background-color: transparent;
        cursor: pointer;
        text-align: center;
        box-sizing: border-box;
        font-size: 0.8em;
        flex-basis: 60px;
    }

        .main-body .imgfile-upper .img-upbtn:hover {
            color: deepskyblue;
            border-color: deepskyblue;
        }

.main-body .cache-imglist {
    display: none;
    background-color: white;
    position: absolute;
    top: calc(100% + 5px);
    left: 0;
    width: 98%;
    background-color: white;
    box-shadow: 1px 1px 5px #999;
    padding: 5px;
}

    .main-body .cache-imglist li {
        display: block;
        width: 100%;
        cursor: pointer;
        background-color: transparent;
        transition: all linear 0.2s;
        border: 0.5px solid white;
        position: relative;
        padding-left: 5px;
        box-sizing: border-box;
        background-position: center;
        background-blend-mode: normal;
        margin-bottom: 3px;
    }

    .main-body .cache-imglist > li:hover {
        border-color: #ccc;
    }

    .main-body .cache-imglist li .del {
        display: inline-block;
        position: absolute;
        width: 20px;
        height: 20px;
        line-height: 20px;
        right: 5px;
        top: calc(50% - 10px);
        text-align: center;
        border-radius: 3px;
        transition: all linear 0.3s;
        cursor: pointer;
    }

        .main-body .cache-imglist li .del:hover {
            background-color: #e83636;
            color: #fff;
        }
