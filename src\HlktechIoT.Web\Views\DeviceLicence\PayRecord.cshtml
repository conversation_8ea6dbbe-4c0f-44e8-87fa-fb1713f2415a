﻿@{
    Layout = null;
}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>支付记录</title>
    <link rel="stylesheet" href="~/libs/pear/css/pear.css" />
    <style>
        body, html {
            margin: 0;
            padding: 0;
            overflow-x: hidden;
            width: 100%;
            height: 100%;
            box-sizing: border-box;
        }

        * {
            box-sizing: border-box;
        }

        .pay-record {
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
            max-width: 100%; 
        }

        .nav {
            width: 100%;
            height: 50px;
            line-height: 50px;
            background-color: #357fff;
            font-size: 16px;
            color: white;
            display: flex;
            position: relative;
            overflow: hidden;
            flex-shrink: 0; 
        }
        .nav-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: 1;
        }
        .nav-title {
            flex: 1;
            text-align: center;
            z-index: 2;
        }
        .nav-link {
            position: absolute;
            top: 15px;
            left: 15px;
            height: 24px;
            width: 24px;
            cursor: pointer;
            z-index: 3;
        }
        .record-list {
            padding: 15px;
            overflow-y: auto;
            background-color: #f3f2f2;
        }
        
        .record-item {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 15px;
            padding: 15px;
        }
        
        .record-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .record-label {
            color: #2B2B2B;
            flex: 1;
        }
        
        .record-value {
            color: #2B2B2B;
            font-weight: 500;
            text-align: right;
            flex: 1;
            white-space: normal;
            word-wrap: break-word;
            word-break: break-all;
        }

        .amount {
            font-weight: 500;
            font-size: 18px;
            color: #E60611;
        }
        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .record-header-label {
            font-weight: bold;
            font-size: 14px;
        }
        .record-header-label {
            font-weight: 500;
            font-size: 14px;
            color: #2B2B2B;
        }

        .record-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        
        .record-status {
            font-weight: 700;
        }
        
        .status-pending {
            color: #6AC141;
        }
        
        .status-success {
            color: #357BFF
        }
        
        .status-failed {
            color: #E60611;
        }

        .status-cancel {
            color: #8b8b8b;
        }
        
        .cancel-btn {
            width: 76px;
            height: 30px;
            line-height: 26px;
            font-size: 14px;
            border-radius: 15px;
            background-color: #fff;
            border: 1px solid #8B8B8B;
            color: #181818;
            cursor: pointer;
        }
        
        .empty-list {
            text-align: center;
            padding: 30px 0;
            color: #999;
            background-color: #fff;
            border-radius: 8px;
            margin: 15px;
        }

        .loading-indicator, .no-more-data, .load-error {
            text-align: center;
            padding: 15px;
            color: #999;
            font-size: 14px;
        }

        .load-error {
            color: #E60611;
        }

        .record-list {
            padding: 15px;
            overflow-y: auto;
            background-color: #f3f2f2;
            flex: 1;
        }

        .btns {
            display: flex;
            
        }
        .go-pay {
            margin-right: 5px;
            width: 76px;
            height: 30px;
            line-height: 26px;
            font-size: 14px;
            border-radius: 15px;
            background-color: #fff;
            border: 1px solid #8B8B8B;
            color: #181818;
            cursor: pointer;
        }
    </style>
    <script>
        var pleaseSelect = '@T("请选择")';
        var layuiPrint = '@T("打印")';
        var layuiExport = '@T("导出")';
        var layuiFilterColumn = '@T("筛选列")';
        var layuiArticlePage = '@T("条/页")';
        var layuiTotal = '@T("共")';
        var layuiBtn = '@T("确定")';
        var layuiGoPage = '@T("到第")';
        var layuiPage = '@T("页")';
        var layuiPrev = '@T("上一页")';
        var layuiNext = '@T("下一页")';
        var layuiNoData = '@T("无数据")';
        var layuiAsc = '@T("升序")';
        var layuiDesc = '@T("降序")';
        var layuiCloseCurrent = '@T("关 闭 当 前")';
        var layuiCloseOther = '@T("关 闭 其 他")';
        var layuiCloseAll = '@T("关 闭 全 部")';
        var layuiMenuStyle = '@T("菜单风格")';
        var layuiTopStyle = '@T("顶部风格")';
        var layuiThemeColor = '@T("主题配色")';
        var layuiMoreSettings = '@T("更多设置")';
        var layuiOpen = '@T("开")';
        var layuiClose = '@T("关")';
        var layuiMenu = '@T("菜单")';
        var layuiView = '@T("视图")';
        var layuiBanner = '@T("通栏")';
        var layuiThroughColor = '@T("通色")';
        var layuiFooter = '@T("页脚")';
        var layuiSelectAll = '@T("全选")';
        var layuiClear = '@T("清空")';
        var layuiReverseSelection = '@T("反选")';
        var layuiPeeling = '@T("换肤")';
        var layuiNoDataYet = '@T("暂无数据")';
        var layuiSearch = '@T("搜索")';
        var layuiPrevious = '@T("上一页")';
        var layuiNext = '@T("下一页")';
        var layuiNotAllowClose = '@T("前页面不允许关闭")';
        var layuiOpenAtMost = '@T("最多打开")';
        var layuiTabs = '@T("个标签页")';
    </script>
</head>
<body>
    <div class="pay-record">
        <div class="nav">
            <img class="nav-background" src="@Url.Content("~/images/renewPay/dingbuditu.png")" alt="" />    
            <a id="backLink">
                <img id="backPay" src="~/images/renewPay/back.png" alt="" class="nav-link" />
            </a>
            <div class="nav-title">@T("支付记录")</div>
        </div>
        <div class="record-list">

        </div>
    </div>
</body>


<script src="~/js/jquery.min.js"></script>
<script src="~/libs/layui/layui.js"></script>
<script>
const layer = layui.layer;
const searchQuery = sessionStorage.getItem('search') || JSON.parse(localStorage.getItem('paymentData')).searchQuery;;
console.log('searchQuery => ', searchQuery);
    if (searchQuery) {
        const baseUrl = '@Url.Action("Index")';
        // 检查baseUrl是否已经包含域名
        if (baseUrl.startsWith('http')) {
            $('#backLink').attr('href', baseUrl + searchQuery);
        } else {
            $('#backLink').attr('href', window.location.origin + baseUrl + searchQuery);
        }
    } else {
        $('#backLink').click(function () {
            window.history.back();
        });
    }
    let currentPage = 1;
    const pageSize = 10;
    let loading = false;
    let hasMoreData = true;
    let payRecords = [];
    
    // 监听滚动事件
    $('.record-list').on('scroll', function() {
        if (loading || !hasMoreData) return;
        
        const scrollTop = $(this).scrollTop();
        const scrollHeight = $(this).prop('scrollHeight');
        const clientHeight = $(this).height();
        
        if (scrollHeight - scrollTop - clientHeight < 100) {
            loadMoreRecords();
        }
    });
    // 初始加载
    loadMoreRecords();
    function loadMoreRecords() {
        if (loading || !hasMoreData) return;
        
        loading = true;
        if (currentPage > 1) {
            $('.record-list').append('<div class="loading-indicator">加载中...</div>');
        }
        
        $.ajax({
            url: '@Url.Action("QueryRenewalPayList")',
            type: 'GET',
            data: { page: currentPage, limit: pageSize },
            success: res => {
                $('.loading-indicator').remove();
                if (res.data && res.data.length > 0) {
                    payRecords = payRecords.concat(res.data);
                    renderNewRecords(res.data);
                    currentPage++;
                } else {
                    hasMoreData = false;
                    if (currentPage === 1) {
                        $('.record-list').html('<div class="empty-list"><p>暂无支付记录</p></div>');
                    } else if (res.data && res.data.length === 0) {
                        $('.record-list').append('<div class="no-more-data">没有更多记录了</div>');
                    }
                }
                loading = false;
            },
            error: function() {
                $('.loading-indicator').remove();
                $('.record-list').append('<div class="load-error">加载失败，请重试</div>');
                loading = false;
            }
        });
    }
    
    // 只渲染新加载的记录
    function renderNewRecords(records) {
        if (!records || records.length === 0) return;
        
        let html = '';
        for (let i = 0; i < records.length; i++) {
            const item = records[i];
            const devices = item.Devices.split(',');
            html += '<div class="record-item">' +
                    '<div class="record-header">' +
                    '<div class="record-header-label">下单时间</div>' +
                    '<div class="record-header-value">' + item.CreateTime + '</div>' +
                    '</div>' +
                    '<div class="record-row">' +
                    '<div class="record-label">设备号</div>' +
                    '<div class="record-value">' + devices.map((device) => '<div class="device-item">' + device + '</div>').join('') + '</div>' +
                    '</div>' +
                    '<div class="record-row">' +
                    '<div class="record-label">订单编号</div>' +
                    '<div class="record-value">' + item.Id + '</div>' +
                    '</div>' +
                    '<div class="record-row">' +
                    '<div class="record-label">支付方式</div>' +
                    '<div class="record-value">' + getPayMethodText(item.PayWay) + '</div>' +
                    '</div>' +
                    '<div class="record-row">' +
                    '<div class="record-label">支付金额</div>' +
                    '<div class="record-value"><span style="color: #E60611">¥</span> <span class="amount">' + formatAmount(item.Amount) + '</span></div>' +
                    '</div>' +
                    '<div class="record-footer">' +
                    '<div class="record-status ' + getStatusTextClass(item.Status).class + '">' + getStatusTextClass(item.Status).text + '</div>';
            
                    if (item.Status === 0) {
                        html += '<div class="btns"><button class="go-pay" data-item=\'' + 
                        JSON.stringify(item) + '\' onclick="goPayment(this.getAttribute(\'data-item\'))">去支付</button>' +
                        '<button class="cancel-btn" onclick="cancelPayment(\'' + item.Id + '\')">取消</button></div>';
                    }
            
            html += '</div></div>';
        }
        
        // 追加新内容而不是替换
        $('.record-list').append(html);
    }

    function formatDateTime(dateTimeStr) {
        // 处理日期时间格式
        if (!dateTimeStr) return '';
        const date = new Date(dateTimeStr);
        return date.getFullYear() + '-' + 
            padZero(date.getMonth() + 1) + '-' + 
            padZero(date.getDate()) + ' ' + 
            padZero(date.getHours()) + ':' + 
            padZero(date.getMinutes()) + ':' + 
            padZero(date.getSeconds());
    }

    function padZero(num) {
        return num < 10 ? '0' + num : num;
    }

    function formatAmount(amount) {
        // 格式化金额为两位小数
        return parseFloat(amount).toFixed(2);
    }

    function getPayMethodText(payWay) {
        // 根据支付方式代码返回文本
        switch(parseInt(payWay)) {
            case 1: return '支付宝';
            case 2: return '微信';
            default: return '未知';
        }
    }

    function getStatusTextClass(status) {
        // 根据状态代码返回文本
        if (typeof status === 'string') return status;
        switch(parseInt(status)) {
            case 0:
                return {
                    text: '待支付',
                    class: 'status-pending'
                }
            case 1: 
                return {
                    text: '支付成功',
                    class: 'status-success'
                };
            case 2:                
                return {
                    text: '支付失败',
                    class: 'status-failed'
                };
            case 3: {
                return {
                    text: '已取消',
                    class: 'status-cancel'
                };
            };
            default: return '';
        }
    }

    // 刷新
    function refresh() {
        currentPage = 1;
        hasMoreData = true;
        payRecords = [];
        // 清空列表内容
        $('.record-list').empty();
        // 重新加载数据
        loadMoreRecords();
    }

    function cancelPayment(orderNo) {
        layer.confirm('确定要取消此订单吗？', {icon: 3, title:'提示'}, function(index){
            //do something
            $.ajax({
                url: '@Url.Action("PayClose", "DeviceLicence")',
                type: 'POST',
                data: { Id: orderNo },
                success: function(result) {
                    if (result.success) {
                        layer.msg('取消成功')
                        setTimeout(() => {
                            layer.close(index);
                            refresh();
                        }, 500)
                    } else {
                        layer.msg(result.msg)
                    }
                },
                error: function() {
                    alert('网络错误，请稍后重试');
                }
            });
        });
        return;

    }

    function goPayment(item) {
        const info = JSON.parse(item);
        const payParams = {
            deviceNames: info.Devices,
            payWay: info.PayWay,
            year: info.RenewalYear,
            id: info.Id
        };
        // 5. 调用支付接口
        $.ajax({
            url: '@Url.Action("CreatePayUrl", "DeviceLicence")',
            type: 'POST',
            data: payParams,
            success: (res) => {
                if (res.success) {
                    if (+payParams.payWay === 1) { // 支付宝
                        // 跳转到支付宝支付页面
                        window.location.href = res.data;
                        @* window.open(res.data); *@
                        // var form = $(response.data);
                        // $('body').append(form);
                        // form.submit();

                        @* window.open(response.data) *@
                    } else if (+payParams.payWay === 2) { // 微信
                        // 显示微信支付二维码
                
                    }
                } else {
                }
            },
            error: function() {
                console.error('网络错误，请稍后重试')
            }
        });
    }
    
</script>
</html>