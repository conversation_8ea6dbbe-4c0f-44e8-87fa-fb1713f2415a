﻿@{
    Html.AppendTitleParts(T("选择角色扩展").Text);

    Int32 Id = ViewBag.Id;

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }
</style>
<form class="layui-form" lay-filter="organization-form" style="padding: 15px 0 0 0; height: 300px;">
    <div class="layui-form-item">
        <label class="layui-form-label">@T("权限选择")</label>
        <div class="layui-input-inline">
            <div id="demo1" style=" width: 100%;"></div>
        </div>
        <label class="layui-form-label" style="padding:1px;">
            <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add" id="add">
                <i class="layui-icon layui-icon-add-1"></i>
                @T("新增")
            </button>
        </label>
    </div>
    <div class="layui-form-item layui-hide">
        <div class="layui-input-block">
            <button class="layui-btn layui-hide" lay-submit lay-filter="organization-submit" id="organization-submit">@T("提交")</button>
        </div>
    </div>
</form>
<script asp-location="Footer">
    layui.use(['abp', 'form'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        window.submitForm = function () {
            $("#organization-submit").click();
        }

        var demo1 = xmSelect.render({
            el: '#demo1',
            //radio: true,
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            clickClose: true,
            pageRemote: true,  // 分页
            remoteMethod: function (val, cb, show, pageIndex) {  // 远程方法
                var obj = [];

                // 接口数据
                $.post('@Url.Action("SearchRoleItem")', { keyword: val, page: pageIndex }, function (res) {
                    if (res.success) {
                        if (res.data != null) {
                            cb(res.data, res.extdata);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });

            },
            on: function (data) {  // 监听选择
                console.log(data);
                if (data.arr.length > 0) {
                    var a = "";
                    for (var i = 0; i < data.arr.length; i++) {
                        if (i == 0) {
                            a = data.arr[i].value;
                        }
                        else {
                            a += "," + data.arr[i].value;
                        }
                    }
                }
            }
        });

        form.on('submit(organization-submit)', function (data) {
            var field = data.field;

            var waitIndex = parent.layer.load(2);
            abp.ajax({
                url: "@Url.Action("SelectRole", new { Id = Id })",
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                data: field,
                abpHandleError: false
            }).done(function (data) {
                if (data.success) {
                    data.index = index;

                    parent.saveCallback(data);
                } else {
                    parent.warning(data.msg);
                }
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });


        $("#add").on("click", function () {
            parent.layer.close(index);

            parent.add();
        })
    });
</script>