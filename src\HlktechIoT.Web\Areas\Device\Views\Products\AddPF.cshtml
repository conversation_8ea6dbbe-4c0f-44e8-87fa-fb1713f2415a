@model Product
@{
    Html.AppendTitleParts(T("新增功能定义").Text);
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        display: flex;
        flex-direction: column;
        padding-top: 30px;
        padding-left: 0px;
    }
    .layui-form-item{
        width:100%;
    }
    .layui-input-inline{
        width:73% !important;
    }

    .layui-form-item.btn {
        padding-top: 10px;
        padding-left: 125px;
    }

    .label-width span {
        color: #f00;
    }

    .layui-form-label {
        width: 94px;
    }

    .upload {
        cursor: pointer;
        max-width: 120px;
        max-height: 90px;
    }
    .select{
        max-width: 420px !important;
        border: 1px solid #e6e6e6;
    }
</style>
<div class="containers">
   <form class="layui-form">  
   
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("名称")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="Name" placeholder="@T("属性名、事件名、服务名")" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("类型")</label>
                <div class="layui-input-inline" style="width: 300px;">
                <select name="Kind">
                    <option value="">@T("请选择")</option>
                    <option value="1">@T("属性")</option>
                    <option value="2">@T("事件")</option>
                    <option value="3">@T("服务")</option>
                </select>
                </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("标识")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="Identifier" placeholder="@T("功能唯一标识，如Temperature")" autocomplete="off" class="layui-input" >
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("访问模式")</label>
            <div class="layui-input-inline" style="width: 300px;">
                <select name="AccessMode">
                    <option value="">@T("请选择")</option>
                    <option value="0">@T("只读")</option>
                    <option value="1">@T("读写")</option>
                    <option value="2">@T("只写")</option>
                </select>
            </div>
        </div>

         <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("数据类型")</label>
            <div class="layui-input-inline" style="width: 300px;">
                <select name="DataType" Id="DataType" lay-filter="DataType">
                    <!option value="">@T("请选择")</!option>
                    <!option value="short">@T("短整数")</!option>
                    <!option value="int">@T("整数")</!option>
                    <!option value="float">@T("小数")</!option>
                    <!option value="bool">@T("布尔型")</!option>
                    <!option value="byte">@T("字节")</!option>
                    <!option value="long">@T("长整数")</!option>
                    <!option value="double">@T("双精度")</!option>
                    <!option value="text">@T("文本")</!option>
                    <!option value="time">@T("时间")</!option>
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("长度")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="number" name="Length" placeholder="@T("字节数或字符串长度，Modbus寄存器一般占2个字节")" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item show">
            <label class="layui-form-label label-width">@T("最小值")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="Min" placeholder="@T("如-40")" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item show">
            <label class="layui-form-label label-width">@T("最大值")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="Max" placeholder="@T("如120")" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item show">
            <label class="layui-form-label label-width">@T("步长")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="Step" placeholder="@T("最小间隔，数据精度")" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item show">
            <label class="layui-form-label label-width">@T("最大间隔")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="MaxStep" placeholder="@T("相邻数据超过该值时抛弃，剔除突发异常数据")" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("单位")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="Unit" placeholder="@T("例如°C")" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("单位名称")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="UnitName" placeholder="@T("例如摄氏度")" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("点位地址")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <textarea name="Address" id="Address" placeholder="@T("常规地址6，Modbus地址 4x0023，位域地址D12.05，虚拟点位地址#")" autocomplete="off" class="layui-textarea" style="height:100px"></textarea>
            </div>
        </div>

        <div class="layui-form-item show">
            <label class="layui-form-label label-width">@T("缩放因子")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="ScalingFactor" placeholder="@T("不能是0，默认1，n*scaling+constant")" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item show">
            <label class="layui-form-label label-width">@T("常量因子")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="ConstantFactor" placeholder="@T("默认0，n*scaling+constant")" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item show1">
            <label class="layui-form-label label-width">@T("枚举映射")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <textarea name="Mapping" placeholder="@T("例如“0=关,1=开”，又如“1=东,2=南,3=西,4=北”")" autocomplete="off" class="layui-textarea"  style="height:100px"></textarea>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("调用方法")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <textarea name="CallMethod" id="CallMethod" placeholder="@T("EventPropertyMethod.Post/ServicePropertyMethod.Get/ServicePropertyMethod.Set")" autocomplete="off" class="layui-textarea" style="height:100px"></textarea>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("事件类型")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="EventType" placeholder="@T("info/alert/error")" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item btn">
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" value="@T("保存")" />
        </div>

    </form>
</div>

<script asp-location="Footer">

    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;

        var iframeName = window.name;
        var iframeArray = iframeName.split('_');
        var index = iframeArray[1];
        var parentIframe = iframeArray[0];

        var targetIframe = null;

        var parentIframes = window.parent.document.getElementsByTagName('iframe');
        for (var i = 0; i < parentIframes.length; i++) {
            if (parentIframes[i].name == parentIframe) {
                targetIframe = parentIframes[i];
                break;
            }
        }

        form.on("select(DataType)", function (data) {
            switch (data.value) {
                case "short":
                case "int":
                case "float":
                case "long":
                case "double":
                case "byte":
                    $(".show").removeClass('layui-hide');
                    $(".show1").removeClass('layui-hide');
                    break;

                case "bool":
                    $(".show").addClass('layui-hide');
                    $(".show1").removeClass('layui-hide');
                    break;

                default:
                    $(".show").addClass('layui-hide');
                    $(".show1").addClass('layui-hide');
                    break;
            }
        });

        form.on('submit(Submit)', function (data) {
            data.field.Id = @Model.Id

            if (data.field.Name.length == 0) {
                abp.notify.warn("@T("名称不能为空")");
                return;
            }

            if (data.field.Kind.length == 0) {
                abp.notify.warn("@T("请选择类型")");
                return;
            }

            if (data.field.Identifier.length == 0) {
                abp.notify.warn("@T("请输入唯一标识")");
                return;
            }

            if (data.field.AccessMode.length == 0) {
                abp.notify.warn("@T("请选择访问模式")");
                return;
            }

            if (data.field.DataType.length == 0) {
                abp.notify.warn("@T("请选择数据类型")");
                return;
            }

            if (data.field.Length.length == 0) {
                abp.notify.warn("@T("请输入长度")");
                return;
            }

            var waitIndex = parent.layer.load(2);

            abp.ajax({
                url: "@Url.Action("AddPF")",
                contentType : "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }

                // 关闭当前编辑页面
                parent.layer.close(index);

                targetIframe.contentWindow.active.reload();
                targetIframe.contentWindow.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

    });
</Script>