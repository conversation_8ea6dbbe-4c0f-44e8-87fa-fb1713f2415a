﻿using HlktechIoT.Core.Models;
using HlktechIoT.Data;
using HlktechIoT.IoTServer.Services;

using IoTServer.Models;

using NewLife;
using NewLife.Serialization;

using Pek;
using Pek.Helpers;
using Pek.Timing;

namespace IoTMqtt.Services;

public class MqttService {
    private readonly MyDeviceService _deviceService;
    private readonly MyUserService _userService;

    public MqttService(MyDeviceService deviceService, MyUserService userService)
    {
        _deviceService = deviceService;
        _userService = userService;
    }

    /// <summary>
    /// Mqtt方式登录
    /// </summary>
    /// <param name="mqttAuthReq"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public MqttDeviceSession MqttLogin(MqttAuthReq mqttAuthReq)
    {
        var clientId = mqttAuthReq.ClientId;
        var username = mqttAuthReq.Username;
        var password = mqttAuthReq.Password;

        if (clientId.IsNullOrEmpty())
            throw new Exception($"MqttLogin:clientId为空:{mqttAuthReq.ToJson()}");

        if (username.IsNullOrEmpty())
            throw new Exception($"MqttLogin:username为空:{mqttAuthReq.ToJson()}");

        //if (productKey.IsNullOrWhiteSpace())
        //    throw new Exception($"mqtt_auth:productKey为空:{mqttAuthReq.ToJson()}");

        //if (password.IsNullOrWhiteSpace())
        //    throw new Exception($"mqtt_auth:password为空:{mqttAuthReq.ToJson()}");

        var deviceCode = username;
        var productKey = mqttAuthReq.ProductCode;
        //var productSecret = "";
        var ip = mqttAuthReq.RemoteIp;
        var localIp = mqttAuthReq.RemoteIp;
        var uuid = "";
        var netType = "";
        var moduleType = "";
        var projectId = "";
        var time = 0L;

        // 分解
        if (!clientId.IsNullOrEmpty())
        {
            var ss = clientId.Split('|');
            if (ss.Length >= 1) productKey = ss[0];
            if (ss.Length >= 2) projectId = ss[1];
            if (ss.Length >= 3) moduleType = ss[2];
            if (ss.Length >= 4) deviceCode = ss[3];
            if (ss.Length >= 5) netType = ss[4];
        }

        if (!username.IsNullOrEmpty())
        {
            var ss = username.Split('|');
            if (ss.Length >= 4)
            {
                deviceCode = ss[0];
                time = ss[1].ToDGLong();
                localIp = ss[2];
                uuid = ss[3];
            }
        }

        if (deviceCode == "0" || deviceCode.IsNullOrWhiteSpace()) return null;

        // 后续要还原，暂时注释掉
        //var now = UnixTime.ToTimestamp();
        //var fiveMinutesInMilliseconds = 5 * 60 * 1000; // 5分钟转换为毫秒
        //if (Math.Abs(time - now) > fiveMinutesInMilliseconds) return null;




        //if (productSecret == deviceCode) productSecret = null;

        // MQTT 设备可能一机一密登录，也可能一型一密登录
        var loginInfo = new LoginInfo
        {
            Code = deviceCode,
            //Name = deviceCode,
            Secret = password,
            ProductKey = productKey,
            ProductSecret = password,
            IP = localIp,
            UUID = uuid,
        };

        var rs = _deviceService.Login(loginInfo, "Mqtt", ip);

        var dv = Device.FindByCode(rs.Code);

        // 增加产品NetType
        if (dv != null)
        {
            if (!netType.IsNullOrWhiteSpace())
            {
                dv.NetType = netType;
                dv.Update();
            }
        }

        if (dv.Product != null) productKey = dv.Product?.Code;

        dv.Login(null, ip);

        //SetChildOnline(dv, ip);

        var session = new MqttDeviceSession
        {
            Device = dv,
            ProductKey = productKey,
            ClientId = clientId,
        };

        return session;
    }

    /// <summary>
    /// Mqtt方式用户登录
    /// </summary>
    /// <param name="mqttAuthReq"></param>
    /// <param name="Length">内容长度</param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public MqttUserSession MqttUserLogin(MqttAuthReq mqttAuthReq, Int32 Length)
    {
        var clientId = mqttAuthReq.ClientId;
        var username = mqttAuthReq.Username;
        var password = mqttAuthReq.Password;

        if (clientId.IsNullOrEmpty())
            throw new Exception($"MqttLogin:clientId为空:{mqttAuthReq.ToJson()}");

        if (username.IsNullOrEmpty())
            throw new Exception($"MqttLogin:username为空:{mqttAuthReq.ToJson()}");

        if (password.IsNullOrEmpty())
            throw new Exception($"MqttLogin:password为空:{mqttAuthReq.ToJson()}");

        var UId = 0;
        var IdentityId = String.Empty;
        var Name = String.Empty;
        var localIp = String.Empty;
        var Type = 0;  // 0为未知，1为Anroid，2为IOS，3为PC，4为微信小程序
        var ip = mqttAuthReq.RemoteIp;
        var SType = 0;  // 用于兼容非密码登录,0为默认密码登录，1为OpenId登录
        var time = 0L;

        if (!clientId.IsNullOrWhiteSpace())
        {
            var ss = clientId.Split('|');
            if (ss.Length >= 1)
            {
                if (!UId.IsInt())
                    throw new Exception($"ClientId格式不对");
                UId = ss[0].ToInt();
            }
            if (ss.Length >= 2)
            {
                IdentityId = ss[1];
            }
            if (ss.Length >= 3)
            {
                localIp = ss[2];
            }
            if (ss.Length >= 4)
            {
                Type = ss[3].ToInt();
            }
        }

        if (!username.IsNullOrEmpty())
        {
            var ss = username.Split('|');
            if (ss.Length >= 2)
            {
                Name = ss[0];
                time = ss[1].ToDGLong();
            }
            if (ss.Length >= 3)
            {
                SType = ss[2].ToInt();
            }
        }

        var now = UnixTime.ToTimestamp();
        var fiveMinutesInMilliseconds = 5 * 60 * 1000; // 5分钟转换为毫秒
        if (Math.Abs(time - now) > fiveMinutesInMilliseconds) return null;

        // MQTT 用户登录
        var loginInfo = new UserLoginInfo
        {
            Name = Name,
            PassWord = password,
            IP = localIp,
            ClientId = clientId,
            IdentityId = IdentityId,
        };

        var rs = _userService.Login(loginInfo, "Mqtt", ip, SType);

        var user = AppUser.FindByIdentityId(rs.IdentityId);

        var session = new MqttUserSession
        {
            UId = UId,
            IdentityId = IdentityId,
            ClientId = clientId,
            User = user,
        };

        return session;
    }
}
