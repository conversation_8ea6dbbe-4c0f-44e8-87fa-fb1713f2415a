﻿@{
    Html.AppendTitleParts(T("添加应用系统").Text);

    
}
<style asp-location="true">
    html {
        background-color: #f2f2f2;
        background-color: transparent !important;
        color: #666;
    }

    body {
        height: 100%;
    }

    .pear-container {
        background-color: white;
    }

    .container {
        padding: 20px
    }
    
    .red {
        color: red
    }

    .uploadImage{
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
    #uploadDemoView{
        position: relative;
    }
    .close{
        content: "×";
        position: absolute;
        top: 10px;
        right: 0;
        width: 20px;
        height: 20px;
        font-size: 20px;
        line-height: 20px;
        color: #009688;
        background-color: rgba(0, 0, 0, 0.2);
        text-align: center;
        border-radius: 2px;
        cursor: pointer;
        z-index: 1000000;
        transition: all 1s;
    }
    .close:hover{
        color: red;
        transform: scale(1.2);
    }

    @if (language.UniqueSeoCode == "en")
    {
        <text>
        .layui-form-label {
            width: 100px !important;
        }
        
        .layui-textarea {
            width: 90% !important;
        }
        
        .layui-input-block.btndiv {
            padding-right: 40px;
        }
        </text>
    }
    .layui-input-block.btndiv {
        text-align: center;
    }
</style>
<div class="container">
    <form class="layui-form" action="">
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span class="red">*</span>@T("名称")</label>
            <div class="layui-input-inline">
                <input type="text" name="name" id="name" autocomplete="off" class="layui-input text-width">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span class="red">*</span>@T("显示名")</label>
            <div class="layui-input-inline">
                <input type="text" name="displayName" id="displayName" autocomplete="off" class="layui-input text-width">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span class="red">*</span>@T("密钥")</label>
            <div class="layui-input-inline">
                <input type="text" name="secret" id="secret" autocomplete="off" class="layui-input text-width2">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">
                @T("图标")
            </label>
            <div class="layui-input-inline">
                <div class="layui-upload-drag" id="upload">
                    <i class="layui-icon"></i>
                    <p>@T("点击上传，或将文件拖拽到此处")</p>
                    <div class="layui-hide" id="uploadDemoView">
                        <hr>
                        <label id="excel" class="layui-form-label-left">
                            <img class="uploadImage" src="" id="uploadImage" alt="">
                            <div class="close" id="close">×</div>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("是否启用")</label>
            <div class="layui-input-inline">
                <select name="enable" id="enable">
                    <option value="true" selected="">@T("是")</option>
                    <option value="false">@T("否")</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("白名单")</label>
            <div class="layui-input-inline">
                <input type="text" name="white" id="white" autocomplete="off" class="layui-input text-width3">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("黑名单")</label>
            <div class="layui-input-inline">
                <input type="text" name="black" id="black" autocomplete="off" class="layui-input text-width3">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("回调地址")</label>
            <div class="layui-input-inline">
                <input type="text" name="urls" id="urls" autocomplete="off" class="layui-input text-width">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("能力集合")</label>
            <div class="layui-input-block">
                <input type="text" name="scopes" id="scopes" autocomplete="off" class="layui-input text-width">
                <span style="margin-left: 10px;">@T("逗号分隔,password,client_credentials")</span>
            </div>
        </div>

        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label label-width">@T("备注")</label>
            <div class="layui-input-block">
                <textarea placeholder="@T("请输入内容")" name="remark" id="remark" class="layui-textarea"></textarea>
            </div>
        </div>

        <div class="layui-form-item btn">
        <input hidden name="FileUrl" id="FileUrl" />
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" value="@T("保存")" />
        </div>
    </form>
</div>

<script asp-location="Footer">
       layui.use(['abp', 'dg', 'form', 'dgcommon', 'upload'], function () {
            var $ = layui.jquery;
            var abp = layui.abp;
            var form = layui.form;
            var os = layui.dgcommon;
            var dg = layui.dg;

            var upload = layui.upload;

            $("#close").click(function () {
            //阻止打开文件选择器
            layui.$('#uploadDemoView').addClass('layui-hide');
            $("#FileUrl").val(" ");
            return false;
        });

        //拖拽阈值表上传
        upload.render({
            elem: '#upload'
            , url: '@Url.Action("UploadFiles")' //改成您自己的上传接口
            , done: function (res) {
                if (!res.success) { //失败打印
                    layui.common.warning(res.msg);
                    return;
                }
                os.success('@T("上传成功")');

                layui.$('#uploadDemoView').removeClass('layui-hide');
                $("#FileUrl").val(res.data.FileUrl);
                console.log("url"+res.data.FileUrl);
                $("#uploadImage").attr("src", '/'+ res.data.FileUrl);
            }
            , accept: 'images' //允许上传的文件类型
            , exts: 'jpg|png|gif|bmp|jpeg|svg' //只允许上传jpg|png|gif|bmp|jpeg|svg 文件,以|分隔多个文件后缀
        });
       
            form.on('submit(Submit)', function (data) {
                if (data.field.name.length == 0) {
                    abp.notify.warn("@T("名称不能为空")");
                    return;
                } else if (data.field.displayName.length == 0) {
                    abp.notify.warn("@T("显示名不能为空")");
                    return;
                } else if (data.field.secret.length == 0) {
                    abp.notify.warn("@T("密钥不能为空")");
                    return;
                }

                var waitIndex = parent.layer.load(2);

                abp.ajax({
                    url: "@Url.Action("AppAdd")",
                    contentType : "application/x-www-form-urlencoded; charset=utf-8",
                    data: data.field,
                    abpHandleError: false
                }).done(function (data) {
                    if (!data.success) {
                        abp.notify.error(data.msg);
                        return false;
                    }
                    parent.layer.closeAll();
                    dg.reload('tables');
                    parent.layui.abp.notify.success(data.msg);
                }).fail(function (jqXHR) {
                    parent.layer.msg(jqXHR.message, { icon: 5 });
                }).always(function () {
                    parent.layer.close(waitIndex);
                });

                return false;
            });

       });
</script>