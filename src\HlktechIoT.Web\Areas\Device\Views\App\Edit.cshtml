﻿@model ProjectApp
@{
    Html.AppendTitleParts(T("编辑APP管理").Text);
        // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        padding-top: 30px;
        padding-left: 30px;
    }

    .layui-form-item.btn {
        padding-top: 10px;
        padding-left: 115px;
    }

    .label-width span {
        color: #f00;
    }
    .label-width{
        white-space:nowrap;
    }

    .layui-form-label {
        width: 110px;
    }

    .upload {
        cursor: pointer;
        max-width: 120px;
        max-height: 90px;
    }
    .select{
        max-width: 420px !important;
        border: 1px solid #e6e6e6;
    }
</style>
<div class="containers">
    <form class="layui-form">

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("名称")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="Name" placeholder="@T("请输入APP名称")" autocomplete="off" class="layui-input" lay-filter="Versions" value="@Model.Name">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("关联项目")</label>
            <div class="layui-input-inline" style="width: 300px;">
                <div id="demo2" style=" width: 100%;"></div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("关联产品")</label>
            <div class="layui-input-inline" style="width: 300px;">
                <div id="demo1" style=" width: 100%;"></div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("关联其他项目产品")</label>
            <div class="layui-input-inline" style="width: 300px;">
                <div id="demo3" style=" width: 100%;"></div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("Android应用包名")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="AndroidPackName" placeholder="@T("请输入AndroidPackName")" autocomplete="off" class="layui-input" lay-filter="Versions"  value="@Model.AndroidPackName">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("iOS应用包名")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="IosPackName" placeholder="@T("请输入IosPackName")" autocomplete="off" class="layui-input" lay-filter="Versions"  value="@Model.IosPackName">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("安卓AppKey")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="AndroidAppKey" placeholder="@T("请输入AndroidAppKey")" autocomplete="off" class="layui-input" lay-filter="Versions"  value="@Model.AndroidAppKey" disabled>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("安卓AppSecret")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="AndroidAppSecret" placeholder="@T("请输入AndroidAppSecret")" autocomplete="off" class="layui-input" lay-filter="Versions"  value="@Model.AndroidAppSecret" disabled>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("苹果AppKey")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="IosAppKey" placeholder="@T("请输入IosAppKey")" autocomplete="off" class="layui-input" lay-filter="Versions"  value="@Model.IosAppKey" disabled>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("苹果AppSecret")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="IosAppSecret" placeholder="@T("请输入IosAppSecret")" autocomplete="off" class="layui-input" lay-filter="Versions"  value="@Model.IosAppSecret" disabled>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("小程序AppKey")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="IosAppKey" placeholder="@T("请输入小程序AppKey")" autocomplete="off" class="layui-input" lay-filter="Versions"  value="@Model.MiniProgramAppKey" disabled>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("小程序AppSecret")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="IosAppSecret" placeholder="@T("请输入小程序AppSecret")" autocomplete="off" class="layui-input" lay-filter="Versions"  value="@Model.MiniProgramAppSecret" disabled>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("极光推送Key")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="JPushKey" placeholder="@T("请输入JPushKey")" autocomplete="off" class="layui-input" lay-filter="Versions"  value="@Model.JPushKey">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("极光推送Secret")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="JPushSecret" placeholder="@T("请输入JPushSecret")" autocomplete="off" class="layui-input" lay-filter="Versions"  value="@Model.JPushSecret">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("极光推送Activity")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="URIAction" placeholder="@T("请输入URIAction")" autocomplete="off" class="layui-input" lay-filter="Versions"  value="@Model.URIAction">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("Apns推送环境")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="checkbox" lay-filter="switch" name="ApnsProduction" lay-skin="switch" lay-text="@T("是")|@T("否")" @(Model.ApnsProduction ? Html.Raw("checked") : "")>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("是否限制多端功能")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="checkbox" lay-filter="switch" name="LimitMultiLogin" lay-skin="switch" lay-text="@T("是")|@T("否")" @(Model.LimitMultiLogin ? Html.Raw("checked") : "")>
            </div>
        </div>

        <div class="layui-form-item btn">
            <input hidden name="Id" value="@Model.Id" />
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" value="@T("保存")" />
        </div>
    </form>
</div>

<script asp-location="Footer">
        var demo2 = xmSelect.render({
            el: '#demo2',
            radio: true,
            name: 'DId',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            clickClose: true,
            pageRemote: true,   // 分页
            disabled:true,
            remoteMethod: function (val, cb, show, pageIndex) {  // 远程方法
                var obj = [];

                // 接口数据
                $.post('@Url.Action("SearchInProject")', { keyword:val, PId:'@Model.ProjectId', page: pageIndex }, function (res) {
                    console.log('搜索后的数据',res);
                    if (res.success) {
                        if (res.data != null) {
                            cb(res.data, res.extdata);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });

            },
             on: function (data) {  // 监听选择
                var a = 0;
                if (a == 0) {
                    a++;
                    return;
                } else {
                    if (data.arr.length > 0) {
                    var a = "";
                    for (var i = 0; i < data.arr.length; i++) {
                        if (i == 0) {
                            a = data.arr[i].value;
                            //demo2.data = []
                            $(".xm-icon-close").click()
                        }
                        else {
                            a += "," + data.arr[i].value;
                        }
                    }
                }
                }
            }
        });
       
        demo1 = xmSelect.render({
                el: '#demo1',
                // radio: true,
                name:'PId' ,
                paging: true,  // 是否翻页
                pageSize: 10,  // 每页数量
                filterable: true,  // 搜索模式
                remoteSearch: true,  // 远程搜索
                // clickClose: true,
                pageRemote: true,  // 分页
                remoteMethod: function (val, cb, show, pageIndex) {  // 远程方法
                    var obj = [];
                    var PId = '@Model.PId'
                    // 接口数据
                    $.post('@Url.Action("SearchProduct")', { keyword:val, Id:'@Model.ProjectId',PId,  page: pageIndex }, function (res) {
                        // console.log('搜索后的数据',res);
                        if (res.success) {
                            if (res.data != null) {
                                 var json = JSON.parse('@Html.Raw(ViewBag.ProductList)');
                               if (json.length==0) {
                                    demo1.update({ disabled: false, remoteSearch: false, pageRemote: false });
                                }
                                 demo1.setValue(json); // 传入一个包含默认值的数组
                                cb(res.data,res.extdata);
                            }
                        }
                        else {
                            cb(obj, 0);
                            os.error(res.data.msg);
                        }
                    });

                },
                on: function (data) {  // 监听选择
                    if (data.arr.length > 0) {
                        var a = "";
                        for (var i = 0; i < data.arr.length; i++) {
                            if (i == 0) {
                            
                                a = data.arr[i].value;
                            }
                            else {
                                a += "," + data.arr[i].value;
                            }
                        }
                    }
                }
            });

     demo3 = xmSelect.render({
                el: '#demo3',
                // radio: true,
                name:'OtherPId' ,
                paging: true,  // 是否翻页
                pageSize: 10,  // 每页数量
                filterable: true,  // 搜索模式
                remoteSearch: true,  // 远程搜索
                // clickClose: true,
                pageRemote: true,  // 分页
                remoteMethod: function (val, cb, show, pageIndex) {  // 远程方法
                    var obj = [];
                    var PId = '@Model.OtherPId';
                    var ProjectId='@Model.ProjectId'
                    // 接口数据
                    $.post('@Url.Action("SearchOrProduct")', { keyword:val,PId,ProjectId,  page: pageIndex }, function (res) {
                        // console.log('搜索后的数据',res);
                        if (res.success) {
                            if (res.data != null) {
                                var json = JSON.parse('@Html.Raw(ViewBag.ProductList2)');
                               if (json.length==0) {
                                    demo3.update({ disabled: false, remoteSearch: false, pageRemote: false });
                                }
                                 demo3.setValue(json); // 传入一个包含默认值的数组
                                cb(res.data,res.extdata);
                            }
                        }
                        else {
                            cb(obj, 0);
                            os.error(res.data.msg);
                        }
                    });

                },
                on: function (data) {  // 监听选择
                    if (data.arr.length > 0) {
                        var a = "";
                        for (var i = 0; i < data.arr.length; i++) {
                            if (i == 0) {
                            
                                a = data.arr[i].value;
                            }
                            else {
                                a += "," + data.arr[i].value;
                            }
                        }
                    }
                }
            });

    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;
        var layer = layui.layer
        var index = parent.layer.getFrameIndex(window.name);

        form.on('submit(Submit)', function (data) {
             if (data.field.Name.length == 0) {
                abp.notify.warn("@T("名称不能为空")");
                return;
            }
             if (data.field.DId == '' || data.field.DId == null || data.field.DId == undefined ||  data.field.DId  == 'undefined ') {
                abp.notify.warn("@T("关联项目不能为空")");
                return;
            }
            if (data.field.PId == '' || data.field.PId == null || data.field.PId == undefined ||  data.field.PId  == 'undefined ') {
                abp.notify.warn("@T("关联产品不能为空")");
                return;
            }
            if (data.field.AndroidAppSecret.length == 0) {
                abp.notify.warn("@T("安卓检验Secret不能为空")");
                return;
            }
            if (data.field.AndroidAppKey.length == 0) {
                abp.notify.warn("@T("安卓AppKey不能为空")");
                return;
            }

            if (data.field.IosAppKey.length == 0) {
                abp.notify.warn("@T("苹果AppKey不能为空")");
                return;
            }
            if (data.field.IosAppSecret.length == 0) {
                abp.notify.warn("@T("苹果检验Secret不能为空")");
                return;
            }

            var waitIndex = parent.layer.load(2);

            abp.ajax({
                url: "@Url.Action("Edit")",
                contentType : "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                dg.reload('tables');
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

    });
</Script>