﻿@{
    Html.AppendTitleParts(T("编辑单页文章").Text);

    var localizationSettings = LocalizationSettings.Current;

    // Css
    Html.AppendCssFileParts(ResourceLocation.Head, "/js/wangeditor/style.css");

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "/js/wangeditor/index.js");

    var modelSingleArticle = Model.Model as SingleArticle;

    var list = SingleArticleSetting.GetAll();
}
<style asp-location="true">
    .container .table > tbody > tr > td {
        padding: 5px 5px;
    }

    html {
        background-color: transparent !important;
    }

    body {
        height: 100%;
    }

    .containers {
        width: 100%;
        /* padding-top: 20px;*/
        height: 100%;
        position: relative
    }

    .bala {
        /* float: right; */
        /* padding-right: 66px; */
        padding-bottom: 20px;
        padding-top: 10px;
        text-align: center;
    }

    .container.form-horizontal {
        width: 100%;
    }

    body {
        background-color: #FFFFFF
    }

    input[type=checkbox], input[type=radio] {
        vertical-align: sub !important;
    }

    .containers .form-horizontal .form-group {
        margin-right: 15px;
        margin-left: 15px;
    }

    input[type=checkbox], input[type=radio] {
        margin-left: 10px !important;
        height: 17px;
    }

    .form-group .tables {
        width: 80%
    }

    .input-group.col-sm-9.tables {
        padding-right: 20px;
    }

    .layui-tab.layui-tab-card {
        width: 85%;
        margin: 0 auto;
        margin-bottom: 10px
    }

    .layui-tab.layui-tab-brief {
        width: 85%;
        margin: 0 auto;
        margin-bottom: 10px
    }

    .layui-form-item {
        margin-top: 20px;
    }

    .layui-input-block {
        margin-left: 80px;
    }

    table#tables {
        margin: 0 auto;
        width: 653px;
        border-width: 1px;
        border-style: solid;
        border-radius: 2px;
        box-shadow: 0 2px 5px 0 rgba(0,0,0,.1);
        border-color: #e6e6e6;
    }

        table#tables td {
            border-width: 1px;
            border-style: solid;
            border-radius: 2px;
            border-color: #e6e6e6;
            line-height: 35px;
            padding-left: 10px
        }

        table#tables th {
            border-width: 1px;
            border-style: solid;
            border-radius: 2px;
            border-color: #e6e6e6;
            line-height: 35px;
            text-align: center;
            min-width: 55px;
        }

        table#tables td.childpf {
            padding-left: 0px !important;
        }

    .layui-textarea {
        resize: none;
        /*width: 90%*/
    }

    .layui-tab.layui-tab-brief {
        margin: 0 auto;
        width: 653px;
    }

    .layui-form-label {
        text-align: left;
        padding: 0;
        padding-right: 20px;
        font-size: 13px;
        height: 36px;
        line-height: 36px;
    }

    .layui-input-block {
        margin-left: 0;
    }

    .layui-textarea {
        min-height: 80px;
    }

    .layui-tab-content {
        padding: 0;
    }

    .layui-input, .layui-textarea {
        border-radius: 5px;
        white-space: nowrap;
    }

    .layui-tab-brief > .layui-tab-title .layui-this {
        color: black;
    }

        .layui-tab-brief > .layui-tab-title .layui-this:after {
            border-bottom: 2px solid #1E88E5;
        }

    .editor—wrapper {
        border: 1px solid #ccc;
        z-index: 100; /* 按需定义 */
        margin-top: 20px;
    }

    .toolbar-container {
        border-bottom: 1px solid #ccc;
    }

    .editor-container {
        height: 300px;
    }

    .w-e-modal {
        top: -350px !important
    }

        .w-e-modal textarea {
            min-height: 450px;
        }
</style>
<script asp-location="Footer">
    var E = window.wangEditor; // 全局变量
    // 切换语言
    const LANG = location.href.indexOf('lang=en') > 0 ? 'en' : 'zh-CN'
    E.i18nChangeLanguage(LANG)

    // // 扩展Menu
    // class MyMenu {
    //     constructor() {
    //         this.title = 'Html'
    //         // this.iconSvg = '<svg >...</svg>'
    //         this.tag = 'button'
    //         this.active = false
    //     }
    //     // 获取编辑器内容源码
    //     getValue(editor) {
    //         return editor.getHtml();
    //     }
    //     // 菜单是否需要激活，当切换为源码时菜单激活
    //     isActive(editor) {
    //         return this.active;
    //     }
    //     isDisabled(editor) {
    //         return false // or true
    //     }
    //     exec(editor, value) {
    //         this.active = !this.active;
    //         if (this.isDisabled(editor)) return;
    //         // editor.insertText(value) // value 即 this.getValue(editor) 的返回值
    //         editor.emit('clickSource', this.active);
    //     }
    // }
    // const myMenuConf = {
    //     key: 'source',
    //     factory() {
    //         return new MyMenu()
    //     }
    // }
    // E.Boot.registerMenu(myMenuConf)

    // Extend menu
    class MyMenu {
        constructor() {
            this.title = 'HTML'
            // this.iconSvg = '<svg >...</svg>'
            this.tag = 'button'
            this.showModal = true
            this.modalWidth = window.innerWidth * 0.8;
        }
        getValue(editor) {
            return '';
        }
        isActive(editor) {
            return false // or true
        }
        isDisabled(editor) {
            return false // or true
        }
        exec(editor, value) {
            // do nothing 什么都不用做
        }
        getModalPositionNode(editor) {
            return null // modal 依据选区定位
        }
        getModalContentElem(editor) {
            const $container = $('<div></div>');

            const inputId = `input-${Math.random().toString(16).slice(-8)}`
            const buttonId = `button-${Math.random().toString(16).slice(-8)}`

            const $inputContainer = $(`<label class="babel-container">
                    <span>Source Code</span>
                    <textarea type="text" id="${inputId}">${editor.getHtml()}</textarea>
                  </label>`)
            const $buttonContainer = $(`<div class="button-container">
                    <button id="${buttonId}">@T("保存")</button>
                  </div>`)

            $container.append($inputContainer).append($buttonContainer)

            $container.on('click', `#${buttonId}`, e => {
                e.preventDefault()

                const text = $(`#${inputId}`).val()
                if (!text) return

                editor.restoreSelection() // 恢复选区
                editor.clear();

                editor.dangerouslyInsertHtml(text)
            })

            setTimeout(() => {
                $(`#${inputId}`).focus()
            })

            return $container[0]

            // PS：也可以把 $container 缓存下来，这样不用每次重复创建、重复绑定事件，优化性能
        }
    }
    const myMenuConf = {
        key: 'source',
        factory() {
            return new MyMenu()
        }
    }
    E.Boot.registerMenu(myMenuConf)

    const toolbarConfig = {
        insertKeys: {
            index: 0,
            keys: ['source'], // 在工具栏中显示菜单
        }
    }

    const uploadImage = {
        server: '@(Pek.Helpers.DHWeb.GetSiteUrl() + Url.Action("UploadImg"))',  // 上传图片地址

        fieldName: 'fileupload',

        // 上传之前触发
        // onBeforeUpload(file: File) { // TS 语法
        onBeforeUpload(file) {    // JS 语法
            // file 选中的文件，格式如 { key: file }
            return file

            // 可以 return
            // 1. return file 或者 new 一个 file ，接下来将上传
            // 2. return false ，不上传这个 file
        },

        // 上传进度的回调函数
        // onProgress(progress: number) {  // TS 语法
        onProgress(progress) {       // JS 语法
            // progress 是 0-100 的数字
            console.log('progress', progress)
        },

        // 单个文件上传成功之后
        // onSuccess(file: File, res: any) {  // TS 语法
        onSuccess(file, res) {          // JS 语法
            console.log(`${file.name} 上传成功`, res)
        },

        // 单个文件上传失败
        // onFailed(file: File, res: any) {   // TS 语法
        onFailed(file, res) {           // JS 语法
            console.log(`${file.name} 上传失败`, res)
        },

        // 上传错误，或者触发 timeout 超时
        // onError(file: File, err: any, res: any) {  // TS 语法
        onError(file, err, res) {               // JS 语法
            console.log(`${file.name} 上传出错`, err, res)
        },
    };

    // // 源码点击
    // function clickSource(active, editor) {
    //     let value = editor.getHtml();
    //     // 先将编辑器内容清空
    //     editor.clear();

    //     if (active) {
    //         // 将html代码转换为html代码块 dangerouslyInsertHtml是插入html不是重置html，如果使用setHtml也会报节点异常
    //         editor.dangerouslyInsertHtml(parseEditorCode(value));
    //     } else {
    //         // 将html代码块转换为editor的html
    //         editor.dangerouslyInsertHtml(parseCodeEditor(value));
    //         value = parseCodeEditor(value);
    //     }
    // }

    // // 将编辑器html转换为代码块内容
    // function parseEditorCode(html) {
    //     let code = html
    //         .replace(/&nbsp;/g, '')
    //         .replace(new RegExp('<p><br></p>', 'g'), '');
    //     let data = parserHtmlCode(code).trim();
    //     let textCode = data
    //         .replace(/</g, "&lt;")
    //         .replace(/>/g, "&gt;")
    //         .replace(/ /g, "&nbsp;");
    //     return `<pre><code class="language-html">${textCode}</code></pre>`;
    // }

    // // 将代码块转换为编辑器html
    // function parseCodeEditor(preCode) {
    //     // 转码
    //     let data = encodeURI(preCode);
    //     // 将&nbsp;转换为空格
    //     data = data.replace(/%C2%A0/g, '%20');
    //     // 解码
    //     data = decodeURI(data);
    //     let htmlStr = data
    //         .replace('<pre><code class="language-html">', '')
    //         .replace('</code></pre>', '')
    //         .replace(/&lt;/ig, "<")
    //         .replace(/&gt;/ig, ">");
    //     return htmlStr
    //         .replace(new RegExp('\\n', 'g'), '')
    //         .replace(new RegExp('<p><br></p>', 'g'), '')
    //         .trim();
    // }

    // // 编辑器中得到的html源码是没有格式的html字符串
    // // 所以需要格式化展示代码
    // // 格式化html代码
    // function parserHtmlCode(code) {
    //     try {
    //         return code;
    //         // return prettier.format(code, {
    //         //     parser: 'html',
    //         //     plugins: [parserHtml],
    //         //     // 格式化的标签不换行 例如span标签等>格式化后会换行
    //         //     htmlWhitespaceSensitivity: 'ignore'
    //         // });
    //     } catch (e) {
    //         console.error('格式化代码错误', e);
    //         return code;
    //     }
    // }
</script>
<div class="containers">
    <form class="layui-form">
        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
            @if (localizationSettings.IsEnable)
            {
                <ul class="layui-tab-title">
                    <li class="layui-this">@T("标准")</li>

                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        <li>@item.DisplayName</li>
                    }
                </ul>
            }
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <div class="layui-form-item">
                        <label class="layui-form-label"><span style="color: red;">*</span>@T("文章标题"):</label>
                        <div class="layui-input-block">
                            <input type="text" name="Name" lay-verify="title" value="@modelSingleArticle!.Name" autocomplete="off" placeholder="@T("请输入文章标题")" class="layui-input">
                        </div>
                    </div>
                    @if (list.Any(e => e.Name == "Ex1" && e.IsEnabled == true))
                    {
                        var model = list.Find(e => e.Name == "Ex1");
                        <div class="layui-form-item">
                            <label class="layui-form-label"><span style="color: red;">*</span>@T(model?.DisplayName):</label>
                            <div class="layui-input-block">
                                <input type="text" name="Ex1" lay-verify="title" value="@modelSingleArticle.Ex1" autocomplete="off" placeholder="@T($"请输入{model?.DisplayName}")" class="layui-input">
                            </div>
                        </div>
                    }

                    <div class="layui-form-item">
                        <label class="layui-form-label label-width">
                            @T("文件")
                        </label>
                        <div class="layui-input-inline">
                            <div class="layui-upload-drag" id="upload">
                                <i class="layui-icon"></i>
                                <p>@T("点击上传，或将文件拖拽到此处")</p>
                                <div class="layui-hide" id="uploadDemoView">
                                    <hr>
                                    <label id="excel" class="layui-form-label-left">@modelSingleArticle.FileUrl</label>
                                    <input hidden id="FilePath" name="FilePath" value="@modelSingleArticle.FileUrl" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <script asp-location="Footer">
                        layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
                            var $ = layui.$;
                            var abp = layui.abp;
                            var form = layui.form;
                            var os = layui.dgcommon;
                            var dg = layui.dg;
                            var upload = layui.upload;

                            //拖拽阈值表上传
                            upload.render({
                                elem: '#upload'
                                , url: '@Url.Action("UploadFiles")' //改成您自己的上传接口
                                , done: function (res) {
                                    if (!res.success) { //失败打印
                                        os.warning(res.msg);
                                        return;
                                    }
                                    os.success('@T("上传成功")');

                                    layui.$('#uploadDemoView').removeClass('layui-hide');
                                    $("#excel").text(res.data.OriginFileName);
                                    $("#FilePath").val(res.data.FilePath);
                                },
                                before: function () {
                                    this.data = {
                                        //Versions: $('#Versions').val(),
                                        //expirationDate: $('#expirationDate').val()
                                    }
                                }
                                // , accept: 'file' //允许上传的文件类型
                                // , exts: 's19|bin' //只允许上传s19文件
                            });
                        });
                    </script>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">@T("文章内容"):</label><br />
                        <div class="layui-input-block">
                            <textarea style="display: none;" name="Content" id="Content">@Html.Raw(modelSingleArticle.Content)</textarea>
                            <div class="editor—wrapper">
                                <div id="toolbar-container" class="toolbar-container"></div>
                                <div id="editor-container" class="editor-container"></div>
                            </div>
                            <script asp-location="Footer">
                                // 创建编辑器
                                const editor = E.createEditor({
                                    selector: '#editor-container',
                                    html: $("#Content").val(),
                                    config: {
                                        placeholder: '@T("请填写文章内容...")',
                                        MENU_CONF: { 'uploadImage': uploadImage },
                                        onChange(editor) {
                                            const html = editor.getHtml()
                                            $("#Content").val(html);
                                        }
                                    },
                                    mode: 'default' // 'default' or 'simple'
                                })

                                // 创建工具栏
                                const toolbar = E.createToolbar({
                                    editor: editor,
                                    selector: '#toolbar-container',
                                    config: toolbarConfig,
                                    mode: 'default' // 'default' or 'simple'
                                })
                            </script>
                        </div>
                    </div>
                </div>
                @if (localizationSettings.IsEnable)
                {
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        var ModelLan = SingleArticleLan.FindBySIdAndLId(modelSingleArticle?.Id ?? 0, item.Id, false);

                        <div class="layui-tab-item">
                            <div class="layui-form-item">
                                <label class="layui-form-label">@T("文章标题"):</label>
                                <div class="layui-input-block">
                                    <input type="text" name="[@item.Id].Name" lay-verify="title" value="@ModelLan.Name" autocomplete="off" placeholder="@T("请输入文章标题")" class="layui-input">
                                </div>
                            </div>

                            @if (list.Any(e => e.Name == "Ex1" && e.IsEnabled == true))
                            {
                                var model = list.Find(e => e.Name == "Ex1");

                                <div class="layui-form-item">
                                    <label class="layui-form-label">@T(model?.DisplayName):</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="[@item.Id].Ex1" lay-verify="title" value="@ModelLan.Ex1" autocomplete="off" placeholder="@T($"请输入{model?.DisplayName}")" class="layui-input">
                                    </div>
                                </div>
                            }

                            <div class="layui-form-item">
                                <label class="layui-form-label label-width">
                                    @T("文件")
                                </label>
                                <div class="layui-input-inline">
                                    <div class="layui-upload-drag" id="upload@(item.Id)">
                                        <i class="layui-icon"></i>
                                        <p>@T("点击上传，或将文件拖拽到此处")</p>
                                        <div class="layui-hide" id="uploadDemoView@(item.Id)">
                                            <hr>
                                            <label id="excel@(item.Id)" class="layui-form-label-left">@ModelLan.FileUrl</label>
                                            <input hidden id="FilePath@(item.Id)" name="[@item.Id].FilePath" value="@ModelLan.FileUrl" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <script asp-location="Footer">
                                layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
                                    var $ = layui.$;
                                    var abp = layui.abp;
                                    var form = layui.form;
                                    var os = layui.dgcommon;
                                    var dg = layui.dg;
                                    var upload = layui.upload;

                                    //拖拽阈值表上传
                                    upload.render({
                                        elem: '#upload@(item.Id)'
                                        , url: '@Url.Action("UploadFiles")' //改成您自己的上传接口
                                        , done: function (res) {
                                            if (!res.success) { //失败打印
                                                os.warning(res.msg);
                                                return;
                                            }
                                            os.success('@T("上传成功")');

                                            layui.$('#uploadDemoView@(item.Id)').removeClass('layui-hide');
                                            $("#excel@(item.Id)").text(res.data.OriginFileName);
                                            $("#FilePath@(item.Id)").val(res.data.FilePath);
                                        },
                                        before: function () {
                                            this.data = {
                                                //Versions: $('#Versions').val(),
                                                //expirationDate: $('#expirationDate').val()
                                            }
                                        }
                                        // , accept: 'file' //允许上传的文件类型
                                        // , exts: 's19|bin' //只允许上传s19文件
                                    });
                                });
                            </script>

                            <div class="layui-form-item layui-form-text">
                                <label class="layui-form-label">@T("文章内容"):</label><br />
                                <div class="layui-input-block">
                                    <textarea style="display: none;" name="[@item.Id].Content" id="Content@(item.Id)">@Html.Raw(ModelLan.Content)</textarea>
                                    <div class="editor—wrapper">
                                        <div id="toolbar-container@(item.Id)" class="toolbar-container"></div>
                                        <div id="editor-container@(item.Id)" class="editor-container"></div>
                                    </div>
                                    <script asp-location="Footer">
                                        // 创建编辑器
                                        const editor@(item.Id) = E.createEditor({
                                            selector: '#editor-container@(item.Id)',
                                            html: $("#Content@(item.Id)").val(),
                                            config: {
                                                placeholder: '@T("请填写文章内容...")',
                                                MENU_CONF: { 'uploadImage': uploadImage },
                                                onChange(editor) {
                                                    const html = editor.getHtml()
                                                    $("#Content@(item.Id)").val(html);
                                                }
                                            },
                                            mode: 'default' // 'default' or 'simple'
                                        })
                                        // 创建工具栏
                                        const toolbar@(item.Id) = E.createToolbar({
                                            editor: editor@(item.Id),
                                            selector: '#toolbar-container@(item.Id)',
                                            config: toolbarConfig,
                                            mode: 'default' // 'default' or 'simple'
                                        })
                                    </script>
                                </div>
                            </div>
                        </div>

                    }
                }
            </div>
        </div>
        <div class="layui-form-item" style="margin-left: 50px; margin-right: 50px;">
            <label class="layui-form-label"><span style="color: red;">*</span>@T("调用别名"):</label>
            <div class="layui-input-block">
                <input type="text" name="Code" placeholder="@T("请输入调用别名")" autocomplete="off" class="layui-input width" value="@modelSingleArticle?.Code">
            </div>
        </div>
        <div class="bala">
            <div class="col-sm-offset-2 col-sm-10">
                <input type="hidden" value="@modelSingleArticle?.Id" name="Id" />
                <button type="button" lay-submit lay-filter="Submit" id="Submit" class="layui-btn layui-btn-normal">@T("保存")</button>
            </div>
        </div>
    </form>
</div>

<script asp-location="Footer">
    $(function () {
        $('#Name').focus();
    });

    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;
        var upload = layui.upload;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);


        form.on('submit(Submit)', function (data) {
            var name = $("[name='Name']").val();
            if (!name) {
                os.warning('@("标准下的文章名称不能为空")');
                return;
            }

            var code = $("[name='Code']").val();
            if (!code) {
                os.warning('@("调用别名不能为空")');
                return;
            }

            var waitIndex = parent.layer.load(2);

            abp.ajax({
                url: "@Url.Action("Edit")",
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                dg.reload('tables');
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;

        });
    });
</script>