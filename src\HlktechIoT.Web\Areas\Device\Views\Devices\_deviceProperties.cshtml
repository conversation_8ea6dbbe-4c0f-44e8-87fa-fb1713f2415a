﻿@{

}
<form class="layui-form dg-form">
    <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width: auto">@T("关键词")：</label>
            <div class="layui-input-inline">
                <input type="text" name="key" id="key" placeholder="@T("属性名称")" autocomplete="off" class="layui-input" lay-filter="key">
            </div>
        </div>
    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script type="text/html" id="DataType">
    {{# if(d.DataType == "short") { }}
    @T("短整数")
    {{# } else if(d.DataType == "int") { }}
    @T("整数")
    {{# } else if(d.DataType == "float" || d.DataType == "Single") { }}
    @T("小数")
    {{# } else if(d.DataType == "bool") { }}
    @T("布尔型")
    {{# } else if(d.DataType == "byte") { }}
    @T("字节")
    {{# } else if(d.DataType == "long" || d.DataType == "UInt64") { }}
    @T("长整数")
    {{# } else if(d.DataType == "double" || d.DataType == "Double") { }}
    @T("双精度")
    {{# } else if(d.DataType == "text" || d.DataType == "String") { }}
    @T("文本")
    {{# } else if(d.DataType == "time" || d.DataType == "DateTime") { }}
    @T("时间")
    {{# } }}
</script>
<script type="text/html" id="ReadOnly">
    {{# if(d.Readonly) { }}
      <i class="layui-icon layui-icon-ok" style="color: #16b777"></i>
    {{# } else { }}
      <i class="layui-icon layui-icon-close" style="color: #ff5722"></i>
    {{# } }}
</script>
<script type="text/html" id="tool">
    <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="psetting"> @T("设置")</a>
    <a class="pear-btn pear-btn-warming pear-btn-xs" lay-event="transmission"> @T("透传")</a>
    <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="readValue"> @T("查看数值")</a>
</script>