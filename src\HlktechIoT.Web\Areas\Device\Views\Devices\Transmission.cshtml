﻿@model DeviceProperty
@{
    Html.AppendTitleParts(T("调试设备").Text);
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .layui-form-switch {
        margin-left: -10px !important;
        margin-top: 2px;
        width: 80px;
        height: 30px;
        margin-left: 10px;
        font-size: 18px;
    }

        .layui-form-switch > i {
            margin-top: 2px;
            width: 18px;
            height: 18px;
        }

        .layui-form-switch > div {
            width: 50px;
            height: 30px;
            text-align: left !important;
            text-indent: 7px;
            font-size: 16px;
            line-height: 30px;
        }
        textarea{
            overflow-y: scroll !important;
        }
</style>
<form class="layui-form" lay-filter="organization-form" style="padding: 10px 0 0 0;">
    <div class="layui-form-item" style="display:flex;place-items:center;width:100%;justify-content:center;">
        @if (Model.Type == "bool")  // 布尔型
        {
            <input type="checkbox" name="Value" class="switch" lay-skin="switch" lay-text="ON|OFF" >
        }
        else if (Model.Type == "byte" || Model.Type == "short" || Model.Type == "int" || Model.Type == "long")
        {
            <textarea name="Value" placeholder="@T("指令下发")" class="layui-textarea" onkeyup="temperatureInput(this)" ></textarea>
        }
        else if (Model.Type == "float" || Model.Type == "double")
        {
            <textarea name="Value" placeholder="@T("指令下发")" class="layui-textarea" onkeyup="sanitizeInput(this)" ></textarea>
        }
        else
        {
            <textarea name="Value" placeholder="@T("指令下发")" class="layui-textarea" ></textarea>
        }
    </div>

    <div class="layui-form-item layui-hide">
        <div class="layui-input-block">
            <button class="layui-btn layui-hide" lay-submit lay-filter="organization-submit" id="organization-submit">@T("提交")</button>
        </div>
    </div>
</form>
<script asp-location="Footer">
    layui.use(['abp', 'form','layer'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var layer = layui.layer;

        // 自定义数据

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        window.submitForm =  ()=> {
            $("#organization-submit").click();
        }

        form.on('submit(organization-submit)',(data)=> {
            var Value = data.field.Value
            if ("@Model.Type" != "bool") {
                if (!Value) {
                    layer.msg('请输入正确的下发内容!');
                    return false;
                }
                Value = formatJson(Value);
            }
            // console.log('这里',Value);
            var waitIndex = parent.layer.load(2);
            abp.ajax({
                method: 'POST',
                url: "@Url.Action("Transmission")",
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                data: {
                    Id: "@Model.Id",
                    Value
                },
                abpHandleError: false
            }).done(function (data) {
                // console.log('这里',JSON.parse(Value));
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                data.index = index;
                parent.saveCallback(data);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

    });
 
    function formatJson(input) {
        try {
            // 尝试解析输入的内容
            const jsonObject = JSON.parse(input);

            // 如果解析成功，使用 JSON.stringify 将其格式化为一行
            return JSON.stringify(jsonObject);
        } catch (e) {
            // 如果解析失败，返回原始输入
            return input;
        }
    }
    function sanitizeInput(input) {
        input.value = input.value.replace(/[^-?\d.]/g, ''); // 只保留数字、正负号和小数点
        input.value = input.value.replace(/(\..*)\./g, '$1'); // 防止多个小数点
        input.value = input.value.replace(/(-.*)-/g, '$1'); // 防止多个负号
        //input.value = input.value.replace(/(-)?(\d+\.\d{2}).*/g, '$1$2'); // 限制为两位小数
    }

    function temperatureInput(input) {
        input.value = input.value.replace(/[^-?\d]/g, ''); // 只保留数字和负号
        input.value = input.value.replace(/(-.*)-/g, '$1'); // 防止多个负号
    }
</script>