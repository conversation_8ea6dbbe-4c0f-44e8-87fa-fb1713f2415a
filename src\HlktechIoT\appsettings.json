{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    },
    "Console": {
      "LogLevel": {
        "Default": "Information"
      },
      "FormatterName": "systemd", // json
      "FormatterOptions": {
        "JsonWriterOptions": {
          "Indented": true
        },
        "ExtraCustomProperty": "modified",
        "IncludeScopes": true,
        "TimestampFormat": "HH:mm:ss",
        "UseUtcTimestamp": true,
        "SingleLine": true
      }
    }
  },
  "RedisCache": "server=127.0.0.1;password=;db=2",
  "AllowedHosts": "*",
  //"ConnectionStrings": {
  //  "DH": {
  //    "connectionString": "Server=denghao.rwlb.rds.aliyuncs.com;Port=3306;Database=HlktechIoT;Uid=hlktechcom;Pwd=****************;SslMode=None;Migration=Full;",
  //    "providerName": "MySql.Data.MySqlClient"
  //  },
  //  "DG": {
  //    "connectionString": "Server=denghao.rwlb.rds.aliyuncs.com;Port=3306;Database=HlktechIoT;Uid=hlktechcom;Pwd=****************;SslMode=None;Migration=Full;",
  //    "providerName": "MySql.Data.MySqlClient"
  //  },
  //  "Membership": {
  //    "connectionString": "Server=denghao.rwlb.rds.aliyuncs.com;Port=3306;Database=HlktechIoT;Uid=hlktechcom;Pwd=****************;SslMode=None;;Migration=Full;",
  //    "providerName": "MySql.Data.MySqlClient"
  //  }
  //}
}
