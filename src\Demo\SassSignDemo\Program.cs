﻿using DH.Iot.Constant;
using DH.Iot.Util;

using NewLife.Log;
using NewLife.Serialization;

using System.Net;

using System.Text;

namespace SassSignDemo;

internal class Program {
    static void Main(string[] args)
    {
        XTrace.UseConsole();

        var result = GetApi("/Api/Cloud/V1/Token/GetToken", "{\"id\": \"1509086454180\"}", "https://localhost:9143", "cn", "测试");

        XTrace.WriteLine($"获取到的数据：{result?.ToJson()}");

        Console.ReadKey();
    }

    /// <summary>
    /// 通用请求接口
    /// </summary>
    /// <param name="Content">请求的内容</param>
    /// <param name="path">接口路径</param>
    /// <param name="From">来源</param>
    /// <param name="Host">网关Url</param>
    /// <returns></returns>
    public static IotResult? GetApi(String path, String Content, String Host, String Lng, String From = "")
    {
        try
        {
            Dictionary<string, string> headers = new Dictionary<string, string>();
            Dictionary<string, string> querys = new Dictionary<string, string>();
            Dictionary<string, string> bodys = new Dictionary<string, string>();
            List<string> signHeader = new List<string>();

            // 设置api版本和参数
            var bobyContent = Content;

            //设定Content-Type，根据服务器端接受的值来设置
            headers.Add(HttpHeader.HTTP_HEADER_CONTENT_TYPE, ContentType.CONTENT_TYPE_STREAM);
            //设定Accept，根据服务器端接受的值来设置
            headers.Add(HttpHeader.HTTP_HEADER_ACCEPT, ContentType.CONTENT_TYPE_JSON);

            //注意：如果有非Form形式数据(body中只有value，没有key)；如果body中是key/value形式数据，不要指定此行
            headers.Add(HttpHeader.HTTP_HEADER_CONTENT_MD5, MessageDigestUtil.Base64AndMD5(bobyContent));
            //如果是调用测试环境请设置
            //headers.Add(SystemHeader.X_CA_STAGE, "TEST");

            headers.Add("Lng", Lng);

            //注意：业务body部分
            bodys.Add("", bobyContent);

            //指定参与签名的header            
            signHeader.Add(SystemHeader.X_CA_TIMESTAMP);
            signHeader.Add("Lng");

            using (HttpWebResponse response = HttpUtils.HttpPost(Host, path, "24915135", "e7acd5b53dd068790de006b6cbd4c664", 5000, headers, querys, bodys, signHeader, From))
            {
                if (response == null)
                {
                    XTrace.WriteLine($"获取到的response为Null：{response == null}");
                }

                //Console.WriteLine(response.StatusCode);
                //Console.WriteLine(response.Method);
                //Console.WriteLine(response.Headers);
                var st = response?.GetResponseStream();
                StreamReader reader = new StreamReader(st, Encoding.GetEncoding("utf-8"));

                var responsecontent = reader.ReadToEnd().Trim('\n');

                return new IotResult { StatusCode = response.StatusCode, Content = responsecontent };

                //Console.WriteLine(Constants.LF);
            }
        }
        catch (Exception ex)
        {
            XTrace.WriteException(ex);
            XTrace.WriteLine($"接口出错检测：{Content}_{path}_{Host}_24915135_e7acd5b53dd068790de006b6cbd4c664");
        }
        return null;
    }
}
