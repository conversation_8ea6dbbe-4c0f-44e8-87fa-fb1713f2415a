﻿@{
    Html.AppendTitleParts(T("新增APP").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        padding-top: 30px;
        padding-left: 30px;
    }

    .layui-form-item.btn {
        padding-top: 10px;
        padding-left: 115px;
    }

    .label-width span {
        color: #f00;
    }

    .layui-form-label {
        width: 94px;
    }

    .upload {
        cursor: pointer;
        max-width: 120px;
        max-height: 90px;
    }
    .select{
        max-width: 420px !important;
        border: 1px solid #e6e6e6;
    }
    .uploadImage{
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
    #uploadDemoView{
        position: relative;
    }
    .close{
        content: "×";
        position: absolute;
        top: 10px;
        right: 0;
        width: 20px;
        height: 20px;
        font-size: 20px;
        line-height: 20px;
        color: #009688;
        background-color: rgba(0, 0, 0, 0.2);
        text-align: center;
        border-radius: 2px;
        cursor: pointer;
        z-index: 1000000;
        transition: all 1s;
    }
    .close:hover{
        color: red;
        transform: scale(1.2);
    }
</style>
<div class="containers">
    <form class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("名称")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="Name" placeholder="@T("请输入APP名称")" autocomplete="off" class="layui-input" lay-filter="Versions">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("关联项目")</label>
            <div class="layui-input-inline" style="width: 300px;">
                <div id="demo2" style=" width: 100%;"></div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("关联产品")</label>
            <div class="layui-input-inline" style="width: 300px;">
                <div id="demo1" style=" width: 100%;"></div>
            </div>
        </div>

        @* <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("关联其他项目产品")</label>
            <div class="layui-input-inline" style="width: 300px;">
                <div id="demo3" style=" width: 100%;"></div>
            </div>
        </div> *@

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("Android应用包名")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="AndroidPackName" placeholder="@T("请输入AndroidPackName")" autocomplete="off" class="layui-input" lay-filter="Versions">
            </div>
        </div>



        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("iOS应用包名")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="IosPackName" placeholder="@T("请输入IosPackName")" autocomplete="off" class="layui-input" lay-filter="Versions" >
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("极光推送Key")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="JPushKey" placeholder="@T("请输入JPushKey")" autocomplete="off" class="layui-input" lay-filter="Versions" >
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("极光推送Secret")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="JPushSecret" placeholder="@T("请输入JPushSecret")" autocomplete="off" class="layui-input" lay-filter="Versions" >
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("极光推送Activity")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="URIAction" placeholder="@T("请输入URIAction")" autocomplete="off" class="layui-input" lay-filter="Versions" >
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("Apns推送环境")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="checkbox" lay-filter="switch" name="ApnsProduction" lay-skin="switch" lay-text="@T("是")|@T("否")">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("是否限制多端功能")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="checkbox" lay-filter="switch1" name="LimitMultiLogin" lay-skin="switch" lay-text="@T("是")|@T("否")">
            </div>
        </div>

        <div class="layui-form-item btn">
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" value="@T("保存")" />
        </div>
    </form>
</div>

<script asp-location="Footer">
    var demo2 = xmSelect.render({
            el: '#demo2',
            radio: true,
            name: 'DId',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            clickClose: true,
            pageRemote: true,  // 分页
            remoteMethod: function (val, cb, show, pageIndex) {  // 远程方法
                var obj = [];

                // 接口数据
                $.post('@Url.Action("SearchProject")', { keyword:val, page: pageIndex }, function (res) {
                    console.log('搜索后的数据',res);
                    if (res.success) {
                        if (res.data != null) {
                            cb(res.data, res.extdata);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });

            },
            on: function (data) {  // 监听选择
                if (data.arr.length > 0) {
                    var a = "";
                    for (var i = 0; i < data.arr.length; i++) {
                        if (i == 0) {
                            a = data.arr[i].value;
                            reloadSelect('#demo1','PId',a,'@Url.Action("SearchProduct")','');
                        }
                        else {
                            a += "," + data.arr[i].value;
                        }
                    }
                }
            }
        });
    //reloadSelect('#demo1','PId',0);
    //reloadSelect('#demo3','OtherPId',0);
    reloadSelect('#demo1','PId',0,'@Url.Action("SearchProduct")','');
    //reloadSelect('#demo3','OtherPId',0,'@Url.Action("SearchOrProduct")','');
    function reloadSelect(str,names,id,methods,ids){ 
        var demo1 = xmSelect.render({
            el: str,
            // radio: true,
            name:names,
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            //clickClose: true,
            pageRemote: true,  // 分页
            remoteMethod: function (val, cb, show, pageIndex) {  // 远程方法
                var obj = [];
                var PId = ids
                // 接口数据
                $.post(methods, { keyword:val,Id:id,PId, page: pageIndex }, function (res) {
                    console.log('搜索后的数据',res);
                    if (res.success) {
                        if (res.data != null) {
                            cb(res.data, res.extdata);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });

            },
            on: function (data) {  // 监听选择
                if (data.arr.length > 0) {
                    var a = "";
                    for (var i = 0; i < data.arr.length; i++) {
                        if (i == 0) {
                            
                            a = data.arr[i].value;
                        }
                        else {
                            a += "," + data.arr[i].value;
                        }
                    }
                }
            }
        });
    }
        
       
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        form.on("switch(switch)", function (data) {
            let checked = data.elem.checked;
            $("input[name='ApnsProduction']").val(checked?'on':'off');
        });

        form.on("switch(switch1)", function (data) {
            let checked = data.elem.checked;
            $("input[name='LimitMultiLogin']").val(checked?'on':'off');
        });
        
        form.on('submit(Submit)', function (data) {
             if (data.field.Name.length == 0) {
                abp.notify.warn("@T("名称不能为空")");
                return;
            }
            if (data.field.DId == '' || data.field.DId == null || data.field.DId == undefined ||  data.field.DId  == 'undefined ') {
                abp.notify.warn("@T("关联项目不能为空")");
                return;
            }
            if (data.field.PId == '' || data.field.PId == null || data.field.PId == undefined ||  data.field.PId  == 'undefined ') {
                abp.notify.warn("@T("关联产品不能为空")");
                return;
            }
            var field = data.field;

            var waitIndex = parent.layer.load(2);
            abp.ajax({
                url: "@Url.Action("Add")",
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                data: field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                dg.reload('tables');
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });
    });
</Script>