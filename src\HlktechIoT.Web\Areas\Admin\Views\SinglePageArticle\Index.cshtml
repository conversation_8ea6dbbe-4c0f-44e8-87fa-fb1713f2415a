﻿@{
    Html.AppendTitleParts(T("单页文章管理").Text);
}

<style asp-location="true">
    .totalcolor {
        font-size: 18px;
        color: #2db7f5;
    }

    .onlinecolor {
        font-size: 18px;
        color: #19be6b;
    }

    .noactivecolor {
        font-size: 18px;
        color: #f90;
    }

    .layui-card-header {
        height: auto;
    }
</style>

<form class="layui-form dg-form">
    <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width: auto">@T("关键词")：</label>
            <div class="layui-input-inline">
                <input type="text" name="name" id="name" placeholder="@T("请输入别名/文章标题")" autocomplete="off" class="layui-input" lay-filter="name">
            </div>
        </div>
    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    function reload() {
        var topLayui = parent === self ? layui : top.layui;
        var tabsBody = topLayui.admin.tabsBody(topLayui.admin.tabsPage.index).find('.layadmin-iframe');
        var iframe = tabsBody[0].contentWindow;

        iframe.actives.success('@T("保存成功")');

        iframe.actives.reload();
    }

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetList")'
            , page: false //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 120
            , cols: [[
                { field: 'Id', width: 80, title: '@T("编号")' }
                , { field: 'Name', width: 120, title: '@T("标题")' }
                , { field: 'Code', width: 80, title: '@T("调用别名")', width: 92 }
                , { field: 'CreateTime', title: '@T("创建时间")', minWidth: 120 }
                , { field: 'CreateUser', title: '@T("创建者")', minWidth: 150 }
                , { fixed: 'right', title: '@T("操作")', templet: '#tool', width: 140 }
            ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-100'
            , id: 'tables'
        });

        window.active = {
            reload: function () {
                table.reload('tables',
                    {
                        where: {
                            name: $("#name").val(),
                        }
                    });
            }
        }

        $("#name").on("input", function (e) {
            active.reload();
        });

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === "edit") {
                top.layui.dg.popupRight({
                    id: 'ThingsDetail'
                    , title: ' @T("编辑单页文章")'
                    , closeBtn: 1
                    , area: ['750px']
                    , success: function () {
                        $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Edit")?id=' + data.Id + '" frameborder="0" class="layadmin-iframe"></iframe>');
                    }
                });
            } else if (obj.event === "del") {
                parent.layer.confirm('@T("确认删除吗")?', { icon: 3, btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("Delete")', { Id: data.Id }, function (data) {
                        if (data.success) {
                            abp.notify.success('@T("删除成功")');
                            active.reload();
                        } else {
                            //alert(data.msg);
                            //os.success(data.msg);
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            }
        });

        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'add') {
                top.layui.dg.popupRight({
                    id: 'FileAdd'
                    , title: ' @T("新增单页文章")'
                    , closeBtn: 1
                    , area: ['780px']
                    , success: function () {
                        $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Add")" frameborder="0" class="layadmin-iframe"></iframe>');
                    }
                });
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });

    });
</script>

<script type="text/html" id="user-toolbar">
    @if (this.Has((PermissionFlags)2))
    {
    <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        @T("新增")
    </button>
    }
</script>

<script type="text/html" id="tool">
    @if (this.Has((PermissionFlags)4))
    {
         <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="edit">@T("编辑")</a>
    }
    @if (this.Has((PermissionFlags)8))
    {
         <a class="pear-btn pear-btn-danger pear-btn-xs" lay-event="del">@T("删除")</a>
    }
</script>