﻿@{
    Html.AppendTitleParts(T("量产管理").Text);

    // Script - 引入动态操作列组件
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/components/dynamic-operation-column.js");

    // Css - 引入动态操作列样式
    Html.AppendCssFileParts(ResourceLocation.Head, "~/css/components/dynamic-operation-column.css");
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    .layui-layer-title {
        background-color: #fff;
    }

    .dtree-nav-item {
        padding-left: 0;
    }

    .layui-table-tool {
        z-index: 1;
    }

    /* 固定列样式优化 */
    .layui-table-fixed-r .layui-table-body {
        overflow-x: hidden !important;
    }

    .layui-table-fixed {
        height: auto !important;
    }

    .layui-table-fixed-r {
        right: 0 !important;
    }

    .layui-table-fixed-r .layui-table-cell {
        padding: 0;
        overflow: visible;
    }

    /* 确保表格行高一致 */
    .layui-table tbody tr {
        height: 38px;
    }

    .layui-table .layui-table-cell {
        height: 38px;
        line-height: 38px;
    }
</style>

<form class="layui-form dg-form">
    <div class="layui-form-item" id="search" style="margin-bottom: 3px;padding: 3px;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width: auto">@T("ProductId")：</label>
            <div class="layui-input-inline">
                <input type="text" name="key" id="key" placeholder="@T("请输入ProductId/产品名称等")" autocomplete="off" class="layui-input" lay-filter="key">
            </div>
        </div>
    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    $("body").on("click", function (event) {
        $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
        $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon', 'dtree'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;
        var dtree = layui.dtree;

        // 定义操作按钮配置
        var operationButtons = [
            { 
                text: '@T("编辑")', 
                event: 'edit', 
                class: 'pear-btn pear-btn-primary', 
                condition: function(d) { return d.State == 0; },
                alwaysShow: true
            },
            @if (ViewBag.HasRole == true)
            {
                @:{ 
                @:    text: '@T("审核")', 
                @:    event: 'audit', 
                @:    class: 'pear-btn pear-btn-warming', 
                @:    condition: function(d) { return d.State == 0; },
                @:    alwaysShow: true
                @:},
            }
            { 
                text: '@T("删除")', 
                event: 'del', 
                class: 'pear-btn pear-btn-danger', 
                condition: function(d) { return d.State == 0; },
                alwaysShow: true
            },
            { 
                text: '@T("导出")', 
                event: 'export', 
                class: 'pear-btn pear-btn-success', 
                condition: function(d) { return d.State == 2; },
                alwaysShow: true
            }
        ];

        // 初始化动态操作列组件
        var operationColumnWidth = window.dynamicOperationColumn.init({
            buttons: operationButtons,
            tableId: 'tablist',
            debug: true  // 开启调试模式
        });

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetList")'
            , page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 60
            , cols: [[
                { field: 'Id', title: '@T("ID")',minWidth:60 }
                , { field: 'ProductName', title: '@T("产品名称")', minWidth: 160 }
                , { field: 'ProductCode', title: '@T("ProductId")', minWidth: 120 }
                , { field: 'Count', title: '@T("数量")' ,minWidth:60 }
                , { field: 'Status', title: '@T("状态")',minWidth:100 }
                , { field: 'CreateUser', title: '@T("申请人")',minWidth:180 }
                , { field: 'CreateTime', title: '@T("申请时间")', width: 180 }
                , { field: 'Remark', title: '@T("Remark")', minWidth: 350 }
                , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: operationColumnWidth }
            ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-100'
            , id: 'tables'
            , done: function (res, curr, count) {
                try {
                    // 使用通用组件应用操作列宽度
                    window.dynamicOperationColumn.delayApplyWidth('tablist', 300, true);
                    console.log('量产列表表格渲染完成，已应用动态操作列宽度');
                } catch (error) {
                    console.error('表格done回调中出错:', error);
                }
            }
        });

        window.active = {
            reload: function () {
                table.reload('tables',
                    {
                        where: {
                            key: $("#key").val(),
                        },
                        page: {
                            curr: 1
                        },
                        done: function(res, curr, count) {
                            try {
                                // 使用通用组件重新应用操作列宽度
                                window.dynamicOperationColumn.delayApplyWidth('tablist', 300, true);
                                console.log('量产列表表格重载完成，已重新应用动态操作列宽度');
                            } catch (error) {
                                console.error('表格重载done回调中出错:', error);
                            }
                        }
                    });
            }
        }

        // 监听输入
        $("#key").on("input", function (e) {
            active.reload();
        });

        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            
            // 检查按钮是否被禁用
            if (obj.event === 'disabled') {
                abp.notify.warn('@T("当前状态下不可操作")');
                return false;
            }
            
            if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("Delete")', { Id: data.Id }, function (data) {
                        if (data.success) {
                            abp.notify.success(data.msg);
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            } else if (obj.event === 'edit') {
                window.edit(data);
            } else if (obj.event === 'export') {
                console.log("123456")
                var exportBtn = $(this);
                if (exportBtn.data('exporting')) {
                    return; // 如果正在导出，则直接返回，不执行后续代码
                }
                // 设置导出状态标志
                exportBtn.data('exporting', true);

                const originalText = exportBtn.html();
                const originalStyle = exportBtn.attr('style') || '';
                // 显示加载状态
                exportBtn.html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> @T("导出")');
                
                // 禁用点击事件
                exportBtn.css({
                    'background-color': '#F7F7F7',
                    'color': '#C9C9C9',
                    'border-color': '#E6E6E6',
                    'cursor': 'not-allowed',
                    'opacity': '0.4',
                    'pointer-events': 'none' // 禁用点击事件
                });

                var link = document.createElement('a');
                link.href = '@Url.Action("Export")?id=' + data.Id;

                // 模拟点击下载
                link.click();
                setTimeout(function() {
                    // 恢复按钮状态
                    exportBtn.html(originalText);
                    exportBtn.attr('style', originalStyle); 
                    exportBtn.data('exporting', false);
                }, 1000); 
                var href = '@Url.Action("Export")?id=' + data.Id;
                $("#export").attr("href", href);
            } else if (obj.event === 'audit') {
                parent.layer.confirm('@T("确认审核吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("Audit")', { Id: data.Id }, function (data) {
                        if (data.success) {
                            abp.notify.success(data.msg);
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            }
        });

        window.saveCallback = function (data) {
            parent.layer.close(data.index);
            abp.notify.success(data.msg);
            table.reload("tables");
        }

        window.add = function () {
            parent.layer.open({
                type: 2,
                title: "@T("申请量产")",
                content: "@Url.Action("Add")",
                area: ["710px", "536px"],
                shade: 0.1,
                btn: ['@T("确定")', '@T("取消")'],
                yes: function (index, layero) {
                    parent.window['layui-layer-iframe' + index].submitForm();
                }
            });
        }

        window.edit = function (data) {
            console.log(data.Id);
            parent.layer.open({
                type: 2,
                title: "@T("编辑量产订单")",
                content: "@Url.Action("Edit")" + abp.utils.formatString("?id={0}", data.Id),
                area: ["710px", "436px"],
                shade: 0.1,
                btn: ['@T("确定")', '@T("取消")'],
                yes: function (index, layero) {
                    parent.window['layui-layer-iframe' + index].submitForm();
                }
            });
        }

        //监听开关事件
        form.on('switch(statusedit)', function (data) {
            //开关是否开启，true或者false
            var checked = data.elem.checked;
            //获取所属属性值
            var id = data.elem.attributes['data-id'].nodeValue;
            $.post('@Url.Action("ModifyState")', { id: id, status: checked }, function (res) {
                if (res.success) {
                    abp.notify.success(res.msg);
                }
                else {
                    abp.notify.error(res.msg);
                }
            });
        });

        window.warning = function (msg) {
            os.warning(msg);
        }
    });
</script>

<script type="text/html" id="tool">
    <div class="operation-column">
        {{#  layui.each(window.operationButtons, function(index, button){ }}
            {{#  var isEnabled = button.condition ? button.condition(d) : true; }}
            {{#  var buttonClass = button.class + ' pear-btn-xs'; }}
            {{#  if(!isEnabled){ }}
                {{#  buttonClass += ' disabled-button'; }}
            {{#  } }}
            <a class="{{buttonClass}}" lay-event="{{isEnabled ? button.event : 'disabled'}}" 
               title="{{!isEnabled ? '当前状态下不可操作' : ''}}"
               data-enabled="{{isEnabled}}">{{button.text}}</a>
        {{#  }); }}
    </div>
</script>

<script type="text/html" id="user-toolbar">
    @if (this.Has((PermissionFlags)2)){
    <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon  layui-icon-add-1"></i>
        @T("申请")
    </button>
    }
</script>