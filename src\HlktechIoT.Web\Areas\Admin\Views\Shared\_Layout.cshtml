﻿@inject IWorkContext workContext
@{
    var language = workContext.WorkingLanguage;

    var addDefaultTitle = false;

    if (ViewBag.AddDefaultTitle != null)
    {
        addDefaultTitle = ViewBag.AddDefaultTitle;
    }

    Html.AppendCssFileParts(ResourceLocation.Head, "~/css/dg.css");
}
<!DOCTYPE html>
<html lang="@language.LanguageCulture" @(this.ShouldUseRtlTheme() ? Html.Raw(" dir=\"rtl\"") : null) @Html.DGPageCssClasses()>
<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <title>@Html.DGTitle(addDefaultTitle)</title>
    @Html.DGHeadCustom()
    <!-- 依 赖 样 式 -->
    <link rel="stylesheet" href="~/libs/pear/css/pear.css" asp-append-version="true" />
    @Html.DGCssFiles(ResourceLocation.Head)
    <script asp-location="Head">
        var pleaseSelect = '@T("请选择")';
        var layuiPrint = '@T("打印")';
        var layuiExport = '@T("导出")';
        var layuiFilterColumn = '@T("筛选列")';
        var layuiArticlePage = '@T("条/页")';
        var layuiTotal = '@T("共")';
        var layuiBtn = '@T("确定")';
        var layuiGoPage = '@T("到第")';
        var layuiPage = '@T("页")';
        var layuiNumber = '@T("个")';
        var layuiPrev = '@T("上一页")';
        var layuiNext = '@T("下一页")';
        var layuiNoData = '@T("无数据")';
        var layuiAsc = '@T("升序")';
        var layuiDesc = '@T("降序")';
        var layuiCloseCurrent = '@T("关 闭 当 前")';
        var layuiCloseOther = '@T("关 闭 其 他")';
        var layuiCloseAll = '@T("关 闭 全 部")';
        var layuiMenuStyle = '@T("菜单风格")';
        var layuiTopStyle = '@T("顶部风格")';
        var layuiThemeColor = '@T("主题配色")';
        var layuiMoreSettings = '@T("更多设置")';
        var layuiOpen = '@T("开")';
        var layuiClose = '@T("关")';
        var layuiMenu = '@T("菜单")';
        var layuiView = '@T("视图")';
        var layuiBanner = '@T("通栏")';
        var layuiThroughColor = '@T("通色")';
        var layuiFooter = '@T("页脚")';
        var layuiSelectAll = '@T("全选")';
        var layuiClear = '@T("清空")';
        var layuiReverseSelection = '@T("反选")';
        var layuiPeeling = '@T("换肤")';
        var layuiNoDataYet = '@T("暂无数据")';
        var layuiSearch = '@T("搜索")';
        var layuiPrevious = '@T("上一页")';
        var layuiNext = '@T("下一页")';
        var layuiNotAllowClose = '@T("前页面不允许关闭")';
        var layuiOpenAtMost = '@T("最多打开")';
        var layuiTabs = '@T("个标签页")';
    </script>
    <script>
        var logoutUrl = '@Url.Action("Index", "Logout", new { ReturnState = 1 })';
        var refreshTokenUrl = '@Url.Action("RefreshToken", "Site", new { Area = "" })';
        var loginUrl = '@Url.Action("Index", "Login")';
    </script>
    @Html.DGScripts(ResourceLocation.Head)
    @Html.DGCanonicalUrls()
    @Html.DGInlineCss(ResourceLocation.Head)
    @Html.DGInlineScripts(ResourceLocation.Head)
    <!--Powered by DengHao - https://www.deng-hao.com-->
</head>
<body class="pear-container">
    @RenderBody()

    <!-- 依 赖 脚 本 -->
    <script src="~/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/libs/layui/layui.js" asp-append-version="true"></script>
    <script src="~/libs/pear/pear.js" asp-append-version="true"></script>
    <script src="~/lib/microsoft/signalr/dist/browser/signalr.js" asp-append-version="true"></script>
    <script src="~/lib/msgpack5/dist/msgpack5.js" asp-append-version="true"></script>
    <script src="~/lib/microsoft/signalr-protocol-msgpack/dist/browser/signalr-protocol-msgpack.js" asp-append-version="true"></script>

    @Html.DGCssFiles(ResourceLocation.Footer)
    @Html.DGScripts(ResourceLocation.Footer)
    @Html.DGInlineScripts(ResourceLocation.Footer)
</body>
</html>