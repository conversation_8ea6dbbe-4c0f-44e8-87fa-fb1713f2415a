﻿@{
    Html.AppendTitleParts(T("设备续期年限").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    .layui-layer-title {
        background-color: #fff;
    }

    .dtree-nav-item {
        padding-left: 0;
    }

    .layui-table-tool {
        z-index: 1;
    }
</style>

<form class="layui-form dg-form">
@*     <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
        <div class="layui-inline select">
            <label class="layui-form-label" style="padding-top: 10px;">@T("全部项目：")</label>
            <div class="layui-input-inline ">
                <input type="hidden" value="@ViewBag.PId" id="PId" name="PId" />
                <div id="demo1" style=" width: 100%;"></div>
            </div>
        </div>
    </div> *@
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
        <script type="text/html" id="tool">
            <a class="pear-btn pear-btn-xs" style="border:1px solid #36b368;background-color:#36b368;color:white;" lay-event="edit"> @T("编辑")</a>
            <a class="pear-btn pear-btn-danger pear-btn-xs" lay-event="del"> @T("删除")</a>
        </script>
    </div>
</div>
<script asp-location="Footer">
    $("body").on("click", function (event) {
        $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
        $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon', 'dtree'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;
        var dtree = layui.dtree;
        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetList")'
            , page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 80
            , cols: [[
                { field: 'Year', title: '@T("年限")', minWidth: 180 }
                , { field: 'Amount', title: '@T("价格")', minWidth: 180 }
                , { field: 'Quantity', title: '@T("数量")', minWidth: 120 }
                , { field: 'Discount', title: '@T("折扣")', minWidth: 180 }
                , { field: 'CreateTime', title: '@T("创建时间")', minWidth: 160 }
                , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: 160 }
            ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-100'
            , id: 'tables'
        });

        window.active = {
            reload: function () {
                table.reload('tables',
                    {
                        where: {
                            key: $("#key").val(),
                            PId: $("#PId").val(),
                        },
                        page: {
                            curr: 1
                        }
                    });
            },
            reload_Init: () => {
                table.reload('tables', {
                    where: {}
                })
            }
        }

        $("#key").on("input", function (e) {
            active.reload();
        });

        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            // console.log(obj);
            if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("Delete")', { Id: data.Id }, function (data) {
                        if (data.success) {
                            abp.notify.success(data.msg);
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            } else if (obj.event === 'edit') {
                window.edit(data);
            }
        });

        //监听开关事件
        form.on('switch(statusedit)', function (data) {
            //开关是否开启，true或者false
            var checked = data.elem.checked;
            //获取所属属性值
            var id = data.elem.attributes['data-id'].nodeValue;
            $.post('@Url.Action("ModifyState")', { id: id, status: checked }, function (res) {
                if (res.success) {
                    abp.notify.success(res.msg);
                }
                else {
                    abp.notify.error(res.msg);
                }
            });
        });
        window.add = function () {
            top.layui.dg.popupRight({
                id: 'Add'
                , title: ' @T("新增")'
                , closeBtn: 1
                , area: ['580px']
                , success: function () {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Add")" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });

        }

        window.edit = function (data) {
            top.layui.dg.popupRight({
                id: 'Edit'
                , title: ' @T("编辑")'
                , closeBtn: 1
                , area: ['580px']
                , success: function () {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Edit")' + abp.utils.formatString("?Id={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }

        window.warning = function (msg) {
            os.warning(msg);
        }

        window.layerClose = function () {

        }
    });
</script>

<script type="text/html" id="tool">
    @if (this.Has((PermissionFlags)4))
    {
                <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="edit"> @T("管理")</a>
    }
</script>

<script type="text/html" id="online">
    {{# if(d.Online) { }}
      <i class="layui-icon layui-icon-ok" style="color: #16b777"></i>
    {{# } else { }}
      <i class="layui-icon layui-icon-close" style="color: #ff5722"></i>
    {{# } }}
</script>

<script type="text/html" id="user-toolbar">
    <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon  layui-icon-add-1"></i>
        @T("新增")
    </button>
</script>