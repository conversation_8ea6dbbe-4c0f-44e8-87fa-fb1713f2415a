﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>续期支付表</summary>
public partial interface IRenewalPay
{
    #region 属性
    /// <summary>编号</summary>
    Int64 Id { get; set; }

    /// <summary>支付订单号</summary>
    String? TradeNo { get; set; }

    /// <summary>设备DeviceName</summary>
    String Devices { get; set; }

    /// <summary>续期年限</summary>
    Int32 RenewalYear { get; set; }

    /// <summary>金额</summary>
    Decimal Amount { get; set; }

    /// <summary>状态(0待支付 1支付成功 2支付失败 3已取消)</summary>
    Int32 Status { get; set; }

    /// <summary>支付方式(1支付宝 2微信)</summary>
    Int32 PayWay { get; set; }

    /// <summary>支付时间</summary>
    DateTime PayTime { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
