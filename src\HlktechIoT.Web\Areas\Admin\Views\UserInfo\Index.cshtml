﻿@model IUser
@{
    Html.AppendTitleParts(T("基本资料设置").Text);
    var RoleName = ViewBag.RoleName as String;
}
<div class="layui-card">
    <div class="layui-card-header">@T("设置我的资料")</div>
    <div class="layui-card-body" pad15>
        <form class="layui-form" lay-filter="">
            <div class="layui-form-item">
                <label class="layui-form-label">@T("用户ID")</label>
                <div class="layui-input-inline">
                    @Html.EditorFor(item => item.ID, new { htmlAttributes = new { @class = "layui-input", @readonly = true } })
                </div>
                <div class="layui-form-mid layui-word-aux">@T("用户ID")</div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("我的角色")</label>
                <div class="layui-input-inline">
                    @Html.EditorFor(item => RoleName, new { htmlAttributes = new { @class = "layui-input", @readonly = true } })
                </div>
                <div class="layui-form-mid layui-word-aux">@T("当前角色不可更改为其它角色")</div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("用户名")</label>
                <div class="layui-input-inline">
                    @Html.EditorFor(item => item.Name, new { htmlAttributes = new { @class = "layui-input", @readonly = true } })
                </div>
                <div class="layui-form-mid layui-word-aux">@T("不可修改。一般用于后台登入名")</div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("姓名")</label>
                <div class="layui-input-inline">
                    @*@Html.EditorFor(item => item.DisplayName, new { htmlAttributes = new Dictionary<String, Object> { { "class", "layui-input" }, { "autocomplete", "off" }, { "placeholder", "T(\"请输入昵称\")" }, { "lay-verify", "nickname" } } })*@
                    <input autocomplete="off" class="layui-input text-box single-line" id="DisplayName" lay-verify="nickname" name="DisplayName" placeholder="@T("请输入姓名")" type="text" value="@ManageProvider.User.DisplayName">

                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("手机号码")</label>
                <div class="layui-input-inline">
                    <input autocomplete="off" class="layui-input text-box single-line" id="Mobile" lay-verify="myphone" lay-verType="tips" name="Mobile" placeholder="@T("请输入手机号码")" type="text" value="@ManageProvider.User.Mobile">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("邮箱")</label>
                <div class="layui-input-inline">
                    <input autocomplete="off" class="layui-input text-box single-line" id="Mail" lay-verify="myemail" lay-verType="tips" name="Mail" placeholder="@T("请输入邮箱")" type="text" value="@ManageProvider.User.Mail">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("性别")</label>
                <div class="layui-input-block">
                    <input type="radio" name="sex" value="0" title="@T("未知")" @(Model!.Sex == SexKinds.未知 ? "checked" : "")>
                    <input type="radio" name="sex" value="1" title="@T("男")" @(Model.Sex == SexKinds.男 ? "checked" : "")>
                    <input type="radio" name="sex" value="2" title="@T("女")" @(Model.Sex == SexKinds.女 ? "checked" : "")>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="pear-btn pear-btn-primary" lay-submit lay-filter="updateinfo">@T("提交")</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'form'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;

        //自定义验证
        form.verify({
            nickname: function (value, item) { //value：表单的值、item：表单的DOM对象
                if (!new RegExp("^[a-zA-Z0-9_\u4e00-\u9fa5\\s·]+$").test(value)) {
                    //return '';
                    return "@T("用户名不能有特殊字符")";
                }
                if (/(^\_)|(\__)|(\_+$)/.test(value)) {
                    //return '用户名首尾不能出现下划线\'_\'';
                    return "@T("用户名首尾不能出现下划线\'_\'")";
                }
                if (/^\d+\d+\d$/.test(value)) {
                    return "@T("用户名不能全为数字")";

                }
            },
            myemail: function (value, item) { //value：表单的值、item：表单的DOM对象
                if (value != "") {
                    if (!/^[a-z0-9._%-]+@@([a-z0-9-]+\.)+[a-z]{2,4}$|^1[3|4|5|7|8]\d{9}$/.test(value)) {
                        return '@T("邮箱格式错误")';
                    }
                }
            },
            myphone: function (value, item) {
                if (value != "") {
                    if (!/^1[3|4|5|6|7|8|9]\d{9}$/.test(value)) {
                        return '@T("手机号格式错误")';
                    }
                }
            }
        });

        form.on('submit(updateinfo)', function (data) {
            var waitIndex = parent.layer.load(2);
            abp.ajax({
                url: "@Url.Action("UpdateInfo")",
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });
    });
</script>