﻿@{
    Html.AppendTitleParts(T("新增产品").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        padding-top: 30px;
        padding-left: 30px;
    }

    .layui-form-item.btn {
        padding-top: 10px;
        padding-left: 115px;
    }

    .label-width span {
        color: #f00;
    }

    .layui-form-label {
        width: 112px;
    }

    .upload {
        cursor: pointer;
        max-width: 120px;
        max-height: 90px;
    }
    .select{
        max-width: 420px !important;
        border: 1px solid #e6e6e6;
    }
        .uploadImage{
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
    #uploadDemoView{
        position: relative;
    }
    .close{
        content: "×";
        position: absolute;
        top: 10px;
        right: 0;
        width: 20px;
        height: 20px;
        font-size: 20px;
        line-height: 20px;
        color: #009688;
        background-color: rgba(0, 0, 0, 0.2);
        text-align: center;
        border-radius: 2px;
        cursor: pointer;
        z-index: 1000000;
        transition: all 1s;
    }
    .close:hover{
        color: red;
        transform: scale(1.2);
    }
</style>
<div class="containers">
    <form class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("名称")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="Name" placeholder="@T("请输入产品名称")" autocomplete="off" class="layui-input" lay-filter="Versions" value="">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("节点类型")</label>
                <div class="layui-input-inline" style="width: 300px;">
                <select name="Kind">
                    <option value="">@T("请选择")</option>
                    <option value="1">@T("直连设备")</option>
                    <option value="2">@T("网关设备")</option>
                    <option value="3">@T("子设备")</option>
                </select>
                </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("数据格式")</label>
                <div class="layui-input-inline" style="width: 300px;">
                <select name="DataFormat">
                    <option value="">@T("请选择")</option>
                    <option value="Json">Json</option>
                </select>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("连网方式")</label>
                <div class="layui-input-inline" style="width: 300px;">
                <select name="NetType">
                    <option value="">@T("请选择")</option>
                    <option value="MQTT">MQTT</option>
                    <option value="WiFi">WiFi</option>
                    <option value="蜂窝Cellular（2G/3G/4G/5G）">@T("蜂窝Cellular（2G/3G/4G/5G）")</option>
                    <option value="以太网">@T("以太网")</option>
                    <option value="LoRaWAN">LoRaWAN</option>
                    <option value="其它">@T("其它")</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("绑定类型")</label>
                <div class="layui-input-inline" style="width: 300px;">
                <select name="BindType">
                    <option value="">@T("请选择")</option>
                    <option value="0">@T("独占式")</option>
                    <option value="1">@T("分享式")</option>
                    <option value="2">@T("抢占式")</option>
                </select>
                </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("项目")</label>
            <div class="layui-input-inline" style="width: 300px;">
                <div id="demo1" style=" width: 100%;"></div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("产品型号")</label>
            <div class="layui-input-inline" style="width: 300px;">
                <div id="demo2" style=" width: 100%;"></div>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("是否启用时间限制")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="checkbox" lay-filter="switch" name="EnableLimitTime" lay-skin="switch" lay-text="@T("是")|@T("否")">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">
                @T("图标")
            </label>
            <div class="layui-input-inline">
                <div class="layui-upload-drag" id="upload">
                    <i class="layui-icon"></i>
                    <p>@T("点击上传，或将文件拖拽到此处")</p>
                    <div class="layui-hide" id="uploadDemoView">
                        <hr>
                        <label id="excel" class="layui-form-label-left">
                            <img class="uploadImage" src="" id="uploadImage" alt="">
                            <div class="close" id="close">×</div>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form-item btn">
            <input hidden name="FileUrl" id="FileUrl" />
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" style="margin-left: 12px;" value="@T("保存")" />
        </div>

    </form>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;

        var upload = layui.upload;

        $("#close").click(function () {
            //阻止打开文件选择器
            layui.$('#uploadDemoView').addClass('layui-hide');
            $("#FileUrl").val(" ");
            return false;
        });

        form.on("switch(switch)", function (data) {
            let checked = data.elem.checked;
            $("input[name='EnableLimitTime']").val(checked?'on':'off');
        });

        //拖拽阈值表上传
        upload.render({
            elem: '#upload'
            , url: '@Url.Action("UploadFiles")' //改成您自己的上传接口
            , done: function (res) {
                if (!res.success) { //失败打印
                    layui.common.warning(res.msg);
                    return;
                }
                os.success('@T("上传成功")');

                layui.$('#uploadDemoView').removeClass('layui-hide');
                $("#FileUrl").val(res.data.FileUrl);
                $("#uploadImage").attr("src", '/'+ res.data.FileUrl);
            }
            , accept: 'images' //允许上传的文件类型
            , exts: 'jpg|png|gif|bmp|jpeg|svg' //只允许上传jpg|png|gif|bmp|jpeg|svg 文件,以|分隔多个文件后缀
        });

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

         var demo1 = xmSelect.render({
            el: '#demo1',
            radio: true,
            name: 'ProjectId',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            clickClose: true,
            pageRemote: true,  // 分页
            remoteMethod: function (val, cb, show, pageIndex) {  // 远程方法
                var obj = [];

                // 接口数据
                $.post('@Url.Action("SearchProject")', { keyword: val, page: pageIndex }, function (res) {
                    // console.log('搜索后的数据',res);
                    if (res.success) {
                        if (res.data != null) {
                            cb(res.data, res.extdata);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });

            },
            on: function (data) {  // 监听选择
                if (data.arr.length > 0) {
                    var a = "";
                    for (var i = 0; i < data.arr.length; i++) {
                        if (i == 0) {
                            
                            a = data.arr[i].value;
                        }
                        else {
                            a += "," + data.arr[i].value;
                        }
                    }
                }
            }
        });

        var demo1 = xmSelect.render({
            el: '#demo2',
            radio: true,
            name: 'ProductModuleId',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            clickClose: true,
            pageRemote: true,  // 分页
            remoteMethod: function (val, cb, show, pageIndex) {  // 远程方法
                var obj = [];

                // 接口数据
                $.post('@Url.Action("SearchProductModule")', { keyword: val, page: pageIndex }, function (res) {
                    if (res.success) {
                        if (res.data != null) {
                            cb(res.data, res.extdata);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });

            },
            on: function (data) {  // 监听选择
                if (data.arr.length > 0) {
                    var a = "";
                    for (var i = 0; i < data.arr.length; i++) {
                        if (i == 0) {
                            a = data.arr[i].value;
                        }
                        else {
                            a += "," + data.arr[i].value;
                        }
                    }
                }
            }
        });


        form.on('submit(Submit)', function (data) {
            if (data.field.Name.length == 0) {
                abp.notify.warn("@T("名称不能为空")");
                return;
            }

            if (data.field.Kind.length == 0) {
                abp.notify.warn("@T("请选择节点类型")");
                return;
            }

            if (data.field.DataFormat.length == 0) {
                abp.notify.warn("@T("请选择数据格式")");
                return;
            }

            if (data.field.NetType.length == 0) {
                abp.notify.warn("@T("请选择连网方式")");
                return;
            }

            if (data.field.ProjectId.length == 0) {
                abp.notify.warn("@T("请选择项目")");
                return;
            }

            if (data.field.ProductModuleId.length == 0) {
                abp.notify.warn("@T("请选择产品型号")");
                return;
            }

            var waitIndex = parent.layer.load(2);

            abp.ajax({
                url: "@Url.Action("Add")",
                contentType : "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                dg.reload('tables');
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

    });
</Script>