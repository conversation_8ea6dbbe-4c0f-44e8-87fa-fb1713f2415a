﻿using DG.Web.Framework;

using DH.Core.Infrastructure;
using DH.Entity;

using HlktechIoT.Data;
using HlktechIoT.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Caching;
using NewLife.Data;
using NewLife.Log;
using NewLife.Serialization;

using Pek;
using Pek.Helpers;
using Pek.Ids;
using Pek.Models;

using System.ComponentModel;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Device.Controllers;

/// <summary>项目管理</summary>
[DisplayName("项目管理")]
[Description("用户项目管理")]
[DeviceArea]
[DHMenu(100, ParentMenuName = "DeviceManager", CurrentMenuUrl = "~/{area}/Projects", CurrentMenuName = "Projects", LastUpdate = "20240124")]
public class ProjectsController : BaseAdminControllerX {

    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 100;

    /// <summary>
    /// 项目管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("项目管理")]
    [HttpGet]
    public IActionResult Index()
    {
        //using var span = DefaultTracer.Instance?.NewSpan(nameof(Index), "项目管理测试");

        //span?.AppendTag($"{MenuOrder}");

        return View();
    }

    /// <summary>
    /// 根据条件搜索
    /// </summary>
    /// <param name="key">项目名称/项目ID等</param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("项目查询")]
    public IActionResult GetList(String key, Int32 page = 1, Int32 limit = 10)
    {
        if (!key.IsNullOrWhiteSpace()) key = key.Trim();
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = Project._.Id,
            Desc = true,
        };

        var UId = 0;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            UId = ManageProvider.User?.ID ?? -1;
        }

        var list = Project.Search(UId, key, pages);
        var data = list.Select(e =>
        {
            var count = ProjectShare.UserCountByProjectId(e.Id);
            if (count == 0)
            {
                var modelProjectShare = new ProjectShare
                {
                    ProjectId = e.Id,
                    ProjectKey = e.ProjectKey,
                    RType = 0, // 项目授权
                    RScope = ",0,",
                    UserName = UserE.FindByID(e.CreateUserID)?.Name,
                };
                modelProjectShare.Insert();
            }

            return new { e.Id, e.ProjectKey, e.Name, e.CreateUser, e.CreateTime, ProductCount = e.ProductList?.Count, AppCount = e.ProjectAppList?.Count, UserCount = count, e.AppKey, e.PushAppKey };
        });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 增加项目
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("增加项目")]
    public IActionResult Add()
    {
        return View();
    }

    /// <summary>
    /// 增加项目
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("增加项目")]
    [HttpPost]
    public IActionResult Add(string displayName, string remark)
    {
        var result = new DResult();

        if (displayName.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("项目名称不能为空");
            return Json(result);
        }

        var cacheProvider = EngineContext.Current.Resolve<ICacheProvider>();

        var model = new Project();
        model.Name = displayName;
        model.ProjectKey = model.CreateProjectKey(cacheProvider);
        model.Remark = remark;
        model.AppKey = IdHelper.GetNextId();
        model.AppSecret = Randoms.RandomString(32);
        model.PushEnabled = true;
        model.PushAppKey = IdHelper.GetNextId();
        model.PushAppSecret = Randoms.RandomString(32);
        model.MesLimitMin = 100;
        model.MesLimitMax = 200;
        model.Insert();

        result.success = true;
        result.msg = GetResource("成功新增项目");

        var modelProjectShare = new ProjectShare
        {
            ProjectId = model.Id,
            ProjectKey = model.ProjectKey,
            RType = 0, // 项目授权
            RScope = ",0,",
            UserName = ManageProvider.User?.Name
        };
        modelProjectShare.Insert();

        return Json(result);
    }

    /// <summary>
    /// 编辑项目
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("编辑项目")]
    public IActionResult Edit(Int32 Id)
    {
        var model = Project.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("项目不存在"));
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {

                return Content(GetResource("您没有权限操作"));
            }
        }

        return View(model);
    }

    /// <summary>
    /// 编辑项目
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("编辑项目")]
    [HttpPost]
    public IActionResult Edit(int Id, string Name, string remark)
    {
        var result = new DResult();

        if (Name.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("项目名称不能为空");
            return Json(result);
        }

        var model = Project.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("项目不存在");
            return Json(result);
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                result.msg = GetResource("您没有权限操作");
                return Json(result);
            }
        }

        model.Name = Name;
        model.Remark = remark;
        model.Update();

        result.success = true;
        result.msg = GetResource("成功编辑项目");

        return Json(result);
    }

    /// <summary>
    /// 删除项目
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除项目")]
    [HttpPost]
    public IActionResult Delete(Int32 Id)
    {
        var res = new DResult();

        var model = Project.FindById(Id);
        if (model == null)
        {
            res.msg = GetResource("项目不存在");
            return Json(res);
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                res.msg = GetResource("您没有权限操作");
                return Json(res);
            }
        }

        var list = Product.FindAllByProjectKey(model.ProjectKey);

        var dlist = Data.Device.FindCountByProductId(list.Select(e => e.Id));
        if (dlist > 0)
        {
            res.msg = GetResource("项目下存在子设备，不允许删除");
            return Json(res);
        }

        list.Delete();
        model.Delete();

        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>APP管理</summary>
    /// <returns></returns>
    [DisplayName("APP管理")]
    [EntityAuthorize((PermissionFlags)16)]
    public IActionResult App(Int32 Id)
    {
        // var model = ProjectApp.FindById(Id);

        var model = Project.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("项目不存在"));
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                return Content(GetResource("您没有权限操作"));
            }
        }

        return View(model);
    }

    /// <summary>APP列表</summary>
    /// <returns></returns>
    [DisplayName("APP管理")]
    [EntityAuthorize((PermissionFlags)16)]
    public IActionResult AppList(Int32 Id, Int32 page = 1, Int32 limit = 10)
    {
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = ProjectApp._.Id,
            Desc = true,
        };

        var UId = 0;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            UId = ManageProvider.User?.ID ?? -1;
        }

        var list = ProjectApp.Search(UId, Id, String.Empty, pages);

        var data = list.Select(x => new { x.Id, x.AndroidAppKey, x.IosAppKey, x.AndroidPackName, x.IosPackName, x.JPushKey, x.JPushSecret, x.URIAction, x.ApnsProduction, x.CreateTime, x.Name });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>搜索产品</summary>
    /// <returns></returns>
    [DisplayName("APP管理")]
    [EntityAuthorize((PermissionFlags)16)]
    public IActionResult SearchProduct(Int32 Id, String keyword, String PId, Int32 page)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = Product._.Id,
            Desc = true,
        };

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (modelRole.IsAdmin)
        {
            res.data = Product.Search(Id, 0, keyword, pages).Select(e =>
            {
                var selected = false;
                if (PId.SplitAsInt().Contains(e.Id))
                {
                    selected = true;
                }
                return new Xmselect<Int32>
                {
                    name = e.Name,
                    value = e.Id,
                    selected = selected
                };
            });
        }
        else
        {
            res.data = Product.Search(Id, ManageProvider.User?.ID ?? -1, keyword, pages).Select(e =>
            {
                var selected = false;
                if (PId.SplitAsInt().Contains(e.Id))
                {
                    selected = true;
                }

                return new Xmselect<Int32>
                {
                    name = e.Name,
                    value = e.Id,
                    selected = selected
                };
            });
        }
        res.extdata = pages.PageCount;
        res.success = true;
        return Json(res);
    }

    /// <summary>新增APP</summary>
    /// <returns></returns>
    [DisplayName("APP管理")]
    [EntityAuthorize((PermissionFlags)16)]
    public IActionResult AppAdd(Int32 Id)
    {
        var model = Project.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("项目不存在"));
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                return Content(GetResource("您没有权限操作"));
            }
        }

        return View(model);
    }

    /// <summary>新增APP</summary>
    /// <returns></returns>
    [DisplayName("APP管理")]
    [EntityAuthorize((PermissionFlags)16)]
    [HttpPost]
    public IActionResult AppAdd(Int32 Id, String Name, String PId, String AndroidPackName, String IosPackName, String JPushKey, String JPushSecret, String URIAction, String ApnsProduction)
    {
        var result = new DResult();

        if (Name.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("名称不能为空");
            return Json(result);
        }

        if (Id <= 0)
        {
            result.msg = GetResource("项目不存在");
            return Json(result);
        }

        if (PId.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("请选择产品");
            return Json(result);
        }

        var model = Project.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("项目不存在");
            return Json(result);
        }

        if (!AndroidPackName.IsNullOrWhiteSpace())
        {
            var modelProjectApp1 = ProjectApp.FindByProjectIdAndAndroidPackName(Id, AndroidPackName);
            if (modelProjectApp1 != null)
            {
                result.msg = GetResource("Android应用包名不能重复");
                return Json(result);
            }
        }
        if (!IosPackName.IsNullOrWhiteSpace())
        {
            var modelProjectApp1 = ProjectApp.FindByProjectIdAndIosPackName(Id, IosPackName);
            if (modelProjectApp1 != null)
            {
                result.msg = GetResource("iOS应用包名不能重复");
                return Json(result);
            }
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                result.msg = GetResource("您没有权限操作");
                return Json(result);
            }
        }

        var list = Product.FindAllByIds(PId.SplitAsInt(","));
        if (!list.Any())
        {
            result.msg = GetResource("产品不存在");
            return Json(result);
        }

        var cacheProvider = EngineContext.Current.Resolve<ICacheProvider>();

        var modelProjectApp = new ProjectApp();
        modelProjectApp.ProjectId = Id;
        modelProjectApp.Name = Name;
        modelProjectApp.ProjectKey = model.ProjectKey;
        modelProjectApp.PId = $",{PId},";
        modelProjectApp.ProductKey = $",{list.Select(e => e.Code).Join()},";
        modelProjectApp.AndroidAppKey = modelProjectApp.CreateProjectKey(cacheProvider);
        modelProjectApp.AndroidAppSecret = Randoms.RandomString(32);
        modelProjectApp.IosAppKey = modelProjectApp.CreateProjectKey(cacheProvider);
        modelProjectApp.IosAppSecret = Randoms.RandomString(32);
        modelProjectApp.AndroidPackName = AndroidPackName;
        modelProjectApp.IosPackName = IosPackName;
        modelProjectApp.JPushKey = JPushKey;
        modelProjectApp.JPushSecret = JPushSecret;
        modelProjectApp.URIAction = URIAction;
        modelProjectApp.ApnsProduction = ApnsProduction.SafeString() == "on";
        modelProjectApp.Insert();

        result.success = true;
        result.msg = GetResource("APP新增成功");

        return Json(result);
    }

    /// <summary>编辑APP</summary>
    /// <returns></returns>
    [DisplayName("APP管理")]
    [EntityAuthorize((PermissionFlags)16)]
    public IActionResult AppEdit(Int32 Id)
    {
        var model = ProjectApp.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("APP不存在"));
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                return Content(GetResource("您没有权限操作"));
            }
        }

        return View(model);
    }

    /// <summary>编辑APP</summary>
    /// <returns></returns>
    [DisplayName("APP管理")]
    [EntityAuthorize((PermissionFlags)16)]
    [HttpPost]
    public IActionResult AppEdit(Int32 Id, String Name, String PId, String AndroidPackName, String IosPackName, String JPushKey, String JPushSecret, String URIAction, String ApnsProduction)
    {
        var result = new DResult();

        if (Name.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("名称不能为空");
            return Json(result);
        }

        if (Id <= 0)
        {
            result.msg = GetResource("APP不存在");
            return Json(result);
        }

        if (PId.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("请选择产品");
            return Json(result);
        }

        var model = ProjectApp.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("APP不存在");
            return Json(result);
        }

        if (!AndroidPackName.IsNullOrWhiteSpace())
        {
            var modelProjectApp = ProjectApp.FindByProjectIdAndAndroidPackName(model.ProjectId, AndroidPackName);
            if (modelProjectApp != null && modelProjectApp.Id != Id)
            {
                result.msg = GetResource("Android应用包名不能重复");
                return Json(result);
            }
        }
        if (!IosPackName.IsNullOrWhiteSpace())
        {
            var modelProjectApp = ProjectApp.FindByProjectIdAndIosPackName(model.ProjectId, IosPackName);
            if (modelProjectApp != null && modelProjectApp.Id != Id)
            {
                result.msg = GetResource("iOS应用包名不能重复");
                return Json(result);
            }
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                result.msg = GetResource("您没有权限操作");
                return Json(result);
            }
        }

        var list = Product.FindAllByIds(PId.SplitAsInt(","));
        if (!list.Any())
        {
            result.msg = GetResource("产品不存在");
            return Json(result);
        }

        var cacheProvider = EngineContext.Current.Resolve<ICacheProvider>();

        model.Name = Name;
        model.PId = $",{PId},";
        model.ProductKey = $",{list.Select(e => e.Code).Join()},";
        model.AndroidPackName = AndroidPackName;
        model.IosPackName = IosPackName;
        model.JPushKey = JPushKey;
        model.JPushSecret = JPushSecret;
        model.URIAction = URIAction;
        model.ApnsProduction = ApnsProduction.SafeString() == "on";
        model.Update();

        result.success = true;
        result.msg = GetResource("APP编辑成功");

        return Json(result);
    }

    /// <summary>删除APP</summary>
    /// <returns></returns>
    [DisplayName("APP管理")]
    [EntityAuthorize((PermissionFlags)16)]
    [HttpPost]
    public IActionResult AppDelete(Int32 Id)
    {
        var res = new DResult();

        var model = ProjectApp.FindById(Id);
        if (model == null)
        {
            res.msg = GetResource("APP不存在");
            return Json(res);
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                res.msg = GetResource("您没有权限操作");
                return Json(res);
            }
        }

        model.Delete();

        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>项目用户</summary>
    /// <returns></returns>
    [DisplayName("用户")]
    [EntityAuthorize((PermissionFlags)32)]
    public IActionResult Users(Int32 Id)
    {
        // var model = ProjectApp.FindById(Id);

        var model = Project.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("项目不存在"));
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                return Content(GetResource("您没有权限操作"));
            }
        }

        return View(model);
    }

    /// <summary>
    /// 根据条件搜索项目用户
    /// </summary>
    /// <param name="Id">项目Id</param>
    /// <param name="key">用户名称、用户昵称、手机号、邮箱等</param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)32)]
    [DisplayName("用户")]
    public IActionResult GetUserList(Int32 Id, String key, Int32 page = 1, Int32 limit = 10)
    {
        var res = new DResult();

        var model = Project.FindById(Id);
        if (model == null)
        {
            res.msg = GetResource("项目不存在");
            return Json(res);
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                res.msg = GetResource("您没有权限操作");
                return Json(res);
            }
        }

        if (!key.IsNullOrWhiteSpace()) key = key.Trim();
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = Project._.Id,
            Desc = true,
        };

        var list = AppUser.Searchs(Id, key, pages);

        var data = list.Select(x => new { x.Id, x.Name, x.DisplayName, x.Enable, x.ProjectName, x.Mail, x.Mobile, x.RegisterTime, x.Online });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>修改App会员状态</summary>
    /// <returns></returns>
    [DisplayName("用户")]
    [HttpPost]
    [EntityAuthorize((PermissionFlags)32)]
    public IActionResult ModifyAUserState(Int32 Id, Boolean Status)
    {
        var result = new DResult();

        var model = AppUser.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("状态调整出错");
            return Json(result);
        }

        model.Enable = Status;
        model.Update();

        result.success = true;
        result.msg = GetResource("状态调整成功");

        return Json(result);
    }

    /// <summary>用户绑定设备</summary>
    /// <returns></returns>
    [DisplayName("用户")]
    [EntityAuthorize((PermissionFlags)32)]
    public IActionResult BindDevice(Int32 Id)
    {
        var model = AppUser.FindById(Id);
        if (model == null) return Content(GetResource("用户不存在"));

        return View(model);
    }

    /// <summary>
    /// 根据条件搜索
    /// </summary>
    /// <param name="Id">用户编号</param>
    /// <param name="Key">DeviceName</param>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)32)]
    [DisplayName("用户")]
    public IActionResult GetDeviceList(String Key, Int32 Id)
    {
        var model = AppUser.FindById(Id);
        if (model == null)
        {
            return Json(new { code = 0, msg = "暂无用户", count = 0, data = new { } });
        }

        var list = model.ProductKeys?.Split(',');
        var i = 0;
        var data = model.Devices?.Split(',').Select(e =>
        {
            if (i >= list?.Length) return null;

            if (!Key.IsNullOrWhiteSpace() && !e.Contains(Key, StringComparison.OrdinalIgnoreCase)) return null;

            i++;

            var ProductKey = list?[i - 1];

            var modelProduct = Product.FindByCode(ProductKey);
            if (modelProduct == null)
            {
                return null;
            }

            var modelDeviceAssociatedUsers = DeviceAssociatedUsers.FindByDeviceName(e);
            if (modelDeviceAssociatedUsers == null)
            {
                return null;
            }

            var owned = 1;
            if (modelProduct.BindType == 2)  // 抢占式
            {
                if (modelDeviceAssociatedUsers.IdentityId != model.IdentityId)
                {
                    return null;
                }
                else
                {
                    owned = 1;
                }
            }
            else if (modelProduct.BindType == 0)  // 独占式
            {
                if (modelDeviceAssociatedUsers.IdentityId != model.IdentityId)
                {
                    return null;
                }
                else
                {
                    owned = 1;
                }
            }
            else if (modelProduct.BindType == 1)  // 分享式
            {
                if (modelDeviceAssociatedUsers.IdentityId != model.IdentityId)
                {
                    if (modelDeviceAssociatedUsers.SubUsers.Contains(model.IdentityId))
                    {
                        owned = 0;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    owned = 1;
                }
            }

            //if (modelProduct.DBName != 0)
            //{
            //    Data.Device.Meta.TableName = $"Device_{modelProduct.DBName}"; //获取表名字
            //}
            //if (modelProduct.ConnName != 0)
            //{
            //    Data.Device.Meta.ConnName = $"Device_{modelProduct.ConnName}"; //获取连接名字
            //}

            try
            {
                var modelDevice = Data.Device.FindByCode(e);
                if (modelDevice == null)
                {
                    return null;
                }

                return new
                {
                    modelDevice.Id,
                    modelDevice.Name,
                    modelDevice.Online,
                    modelDevice.Code,
                    modelDevice.ProductName,
                    modelDevice.Enable,
                    modelDevice.Version,
                    modelDevice.Module,
                    modelDevice.PostPeriod,
                    modelDevice.LastLogin,
                    Owned = owned,
                };
            }
            catch (Exception ex)
            {
                XTrace.WriteException(ex);
            }
            //finally
            //{
            //    Data.Device.Meta.TableName = null;
            //    Data.Device.Meta.ConnName = null;
            //}
            return null;
        }).Where(e => e != null).ToList();

        return Json(new { code = 0, msg = "success", count = data?.Count(), data });
    }

    /// <summary>项目用户</summary>
    /// <returns></returns>
    [DisplayName("成员")]
    [EntityAuthorize((PermissionFlags)64)]
    public IActionResult UserCount(Int32 Id)
    {
        // var model = ProjectApp.FindById(Id);

        var model = Project.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("项目不存在"));
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                return Content(GetResource("您没有权限操作"));
            }
        }

        return View(model);
    }

    /// <summary>
    /// 根据条件搜索项目用户
    /// </summary>
    /// <param name="Id">项目Id</param>
    /// <param name="key">用户名称、用户昵称、手机号、邮箱等</param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)64)]
    [DisplayName("项目成员")]
    public IActionResult GetProjectShareList(Int32 Id, String key, Int32 page = 1, Int32 limit = 10)
    {
        var res = new DResult();

        var model = Project.FindById(Id);
        if (model == null)
        {
            res.msg = GetResource("项目不存在");
            return Json(res);
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                res.msg = GetResource("您没有权限操作");
                return Json(res);
            }
        }

        if (!key.IsNullOrWhiteSpace()) key = key.Trim();
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = Project._.Id,
            Desc = true,
        };

        var list = ProjectShareEX.SearchById(Id, key, pages);

        var data = list.Select(x =>
        {
            var RType = string.Empty;
            switch (x.RType)
            {
                case 0:
                    RType = GetResource("项目授权");
                    break;

                case 1:
                    RType = GetResource("指定产品");
                    break;
            }

            return new { x.Id, x.UserName, x.ProjectId, x.ProjectKey, x.ProjectName, RType, x.RScope, x.UName, x.CreateTime };
        });

        //var data = list.Select(x => new { x.Id, x.UserName, x.ProjectId, x.ProjectKey, x.ProjectName, x.RType, x.RScope, x.UName,x.CreateTime });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>增加项目成员</summary>
    /// <returns></returns>
    [DisplayName("成员")]
    [EntityAuthorize((PermissionFlags)64)]
    public IActionResult AddUser(Int32 Id)
    {
        var model = Project.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("项目不存在"));
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                return Content(GetResource("您没有权限操作"));
            }
        }

        return View(model);
    }

    /// <summary>新增项目成员</summary>
    /// <returns></returns>
    [DisplayName("成员")]
    [EntityAuthorize((PermissionFlags)64)]
    [HttpPost]
    public IActionResult AddUser(Int32 Id, String UserName, String UName, int RType, String RScope, String PId)
    {
        var result = new DResult();
        if (UserName.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("名称不能为空");
            return Json(result);
        }

        if (Id <= 0)
        {
            result.msg = GetResource("项目不存在");
            return Json(result);
        }

        if (RType == 1)
        {
            if (PId.IsNullOrWhiteSpace())
            {
                result.msg = GetResource("请选择产品");
                return Json(result);
            }
        }

        var model = Project.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("项目不存在");
            return Json(result);
        }
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                result.msg = GetResource("您没有权限操作");
                return Json(result);
            }
        }
        var uname = UserE.FindByUserName(0, UserName);
        if (uname)
        {
            result.msg = GetResource("未找到该用户");
            return Json(result);
        }
        var pname = ProjectShareEX.FindByNameAndId(UserName, Id);
        if (!pname)
        {
            result.msg = GetResource("请勿添加重复成员");
            return Json(result);
        }

        var list = Product.FindAllByIds(PId.SplitAsInt(","));

        var modelProjectShare = new ProjectShare
        {
            ProjectId = Id,
            ProjectKey = model.ProjectKey,
            ProductIds = $",{PId},",
            ProductKeys = $",{list.Select(e => e.ProjectKey).Join()},",
            UserName = UserName,
            UName = UName,
            RType = (short)RType,
            RScope = $",{RScope},"
        };
        modelProjectShare.Insert();

        result.success = true;
        result.msg = GetResource("成员新增成功");

        return Json(result);
    }

    /// <summary>编辑项目成员</summary>
    /// <returns></returns>
    [DisplayName("成员")]
    [EntityAuthorize((PermissionFlags)64)]
    public IActionResult EditUser(Int32 Id)
    {
        var model = ProjectShare.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("成员不存在"));
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                return Content(GetResource("您没有权限操作"));
            }
        }

        var res = new DResult();
        var modelProduct = Product.FindAllByIds(model.ProductIds.SplitAsInt(","));
        res.data = modelProduct.Select(e =>
        {
            return new Xmselect<Int32>
            {
                name = e.Name,
                value = e.Id,
            };
        });
        ViewBag.ProductList = res.data.ToJson();

        return View(model);
    }

    /// <summary>编辑项目成员</summary>
    /// <returns></returns>
    [DisplayName("成员")]
    [EntityAuthorize((PermissionFlags)64)]
    [HttpPost]
    public IActionResult EditUser(Int32 Id, String UName, int RType, String RScope, String PId, int ProjectId)
    {
        var result = new DResult();
        if (Id <= 0)
        {
            result.msg = GetResource("成员不存在");
            return Json(result);
        }

        if (RType == 1)
        {
            if (PId.IsNullOrWhiteSpace())
            {
                result.msg = GetResource("请选择产品");
                return Json(result);
            }
        }

        var model = Project.FindById(ProjectId);
        if (model == null)
        {
            result.msg = GetResource("项目不存在");
            return Json(result);
        }
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                result.msg = GetResource("您没有权限操作");
                return Json(result);
            }
        }

        var list = Product.FindAllByIds(PId.SplitAsInt(","));

        var modelProjectShare = ProjectShare.FindById(Id);
        modelProjectShare.ProductIds = $",{PId},";
        modelProjectShare.ProductKeys = $",{list.Select(e => e.ProjectKey).Join()},";
        modelProjectShare.UName = UName;
        modelProjectShare.RType = (short)RType;
        modelProjectShare.RScope = $",{RScope},";
        modelProjectShare.Update();

        result.success = true;
        result.msg = GetResource("成员编辑成功");

        return Json(result);
    }

    /// <summary>删除成员</summary>
    /// <returns></returns>
    [DisplayName("成员")]
    [EntityAuthorize((PermissionFlags)64)]
    [HttpPost]
    public IActionResult UserDelete(Int32 Id)
    {
        var res = new DResult();

        var model = ProjectShare.FindById(Id);
        if (model == null)
        {
            res.msg = GetResource("成员不存在");
            return Json(res);
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                res.msg = GetResource("您没有权限操作");
                return Json(res);
            }
        }

        model.Delete();

        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

}