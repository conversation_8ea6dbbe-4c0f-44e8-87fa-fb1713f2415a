﻿using DG.Web.Framework;
using HlktechIoT.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife.Data;
using Pek;
using Pek.Models;
using System.ComponentModel;
using XCode.Membership;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Device.Controllers
{
    [DisplayName("设备续期订单")]
    [Description("设备续期订单")]
    [DeviceArea]
    [DHMenu(8, ParentMenuName = "DeviceManager", CurrentMenuUrl = "~/{area}/RenewalOrder", CurrentMenuName = "RenewalOrder", LastUpdate = "20250418")]
    public class RenewalOrderController : BaseAdminControllerX
    {
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("列表")]
        public IActionResult Index()
        {
            return View();
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("列表")]
        public IActionResult GetList(Int32 page, Int32 limit,String key,Int32 status = -1,Int32 way = -1)
        {
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = RenewalPay._.CreateTime,
                Desc = true,
            };

            var data = RenewalPay.Search(key, status, way,pages).Select(e => new
            {
                Id = e.Id.SafeString(),
                e.Devices,
                e.Amount,
                e.RenewalYear,
                e.Status,
                e.PayWay,
                e.PayTime,
                e.CreateTime,
            });

            return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
        }
    }
}
