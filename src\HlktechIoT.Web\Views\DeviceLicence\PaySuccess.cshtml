﻿@{
    Layout = null;
}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>支付成功</title>
    <script src="~/js/jquery.min.js"></script>
    <style>
        body, html {
        margin: 0;
        padding: 0;
        overflow-x: hidden;
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        }

        * {
        box-sizing: border-box;
        }
        .pay-success {
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
            background: #f3f2f2;
            max-width: 100%; 
        }

        .success-header {
        text-align: center;
        padding: 30px 0;
        background-color: #357fff;
        color: white;
        height: 40vh;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        background: linear-gradient(0deg, #357BFF, #37B5FF);
        }
        .back-icon {
        position: absolute;
        top: 15px;
        left: 15px;
        width: 24px;
        height: 24px;
        cursor: pointer;
        }
        .success-icon {
        width: 48px;
        height: 48px;
        margin-bottom: 15px;
        color: white;
        }

        .success-amount {
        font-size: 28px;
        font-weight: bold;
        }

        .success-text {
        font-size: 14px;
        }

        .info-container {
        background-color: white;
        border-radius: 10px;
        margin: -50px 15px 0;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        position: relative;
        z-index: 10;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 6px 0;
            font-size: 14px;
        }

        .info-label {
            color: #2B2B2B;
        }

        .info-value {
            color: #2B2B2B;
            font-weight: 500;
        }

        .equipment {
            color: #357bff;
        }
        
        .button-group {
        display: flex;
        justify-content: space-between;
        margin: 30px 15px;
        }

        .btn {
        flex: 1;
        height: 40px;
        border-radius: 20px;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        margin: 0 10px;
        }

        .btn-primary {
        background: linear-gradient(0deg, #357BFF, #37B5FF);
        color: white;
        border: none;
        }

        .btn-default {
        color: #357BFF;
        background: #f3f2f2;
        border: 1px solid #357cff;
        }
    </style>
</head>
<body>
    <div class="pay-success">
        <div class="success-header">
            <a class="back-link">
                <img src="~/images/renewPay/back.png" alt="" class="back-icon" />
            </a>
            <img src="~/images/renewPay/zhifuchenggong.png" alt="" class="success-icon" />
            <div class="success-amount"><span style="font-size: 14px;">¥</span> @Model.Amount</div>
            <div class="success-text">@T("实际支付金额以订单为准")</div>
        </div>

        <div class="info-container">
            <div class="info-item">
                <div class="info-label">@T("订单编号")</div>
                <div class="info-value equipment">@Model.Id</div>
            </div>

            <div class="info-item">
                <div class="info-label">@T("支付方式")</div>
                <div class="info-value">@{
                        if(Model.PayWay == 1)
                        {
                            @T("支付宝")
                        }
                        else
                        {
                            @T("微信")
                        }    
                    }</div>
            </div>
            <div class="info-item">
                <div class="info-label">@T("支付时间")</div>
                <div class="info-value">@Model.PayTime</div>
            </div>
            <div class="info-item">
                <div class="info-label">@T("设备号")</div>
                <div class="info-value">@{
                        var device = Model.Devices.Split(",");
                        foreach (var item in device)
                        {
                            <div class="info-value">@item</div>
                        }    
                    }</div>
            </div>
        </div>
        
        <div class="button-group">
            <a href="@Url.Action("PayRecord")" class="btn btn-primary">@T("查看订单")</a>
            <a href="" class="btn btn-default back-link">@T("返回")</a>
        </div>
    </div>
</body>
<script>
    const searchQuery = JSON.parse(localStorage.getItem('paymentData')).searchQuery;
    if (searchQuery) {
        const baseUrl = '@Url.Action("Index")';
        console.log('window.location.origin + baseUrl + searchQuery => ', window.location.origin + baseUrl + searchQuery)
        if (baseUrl.startsWith('http')) {
            $('.back-link').attr('href', baseUrl + searchQuery);
        } else {
            $('.back-link').attr('href', window.location.origin + baseUrl + searchQuery);
        }
    } else {
        $('.back-link').click(function () {
            window.history.back();
        });
    }
    
    // 监听所有链接点击事件
    document.addEventListener('click', function(e) {
        // 检查点击的是否是链接元素
        if (e.target.tagName === 'A' || e.target.closest('a')) {
            // 是链接点击，将要离开页面，删除数据
            localStorage.removeItem('paymentData');
        }
    });
</script>
</html>