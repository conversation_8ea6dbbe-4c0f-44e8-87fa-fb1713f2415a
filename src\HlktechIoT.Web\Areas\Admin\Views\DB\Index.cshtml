﻿@{
    Html.AppendTitleParts(T("数据库管理").Text);
}

<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    /* 操作列自适应样式 */
    .layui-table-fixed-r .layui-table-body {
        overflow-x: hidden !important;
    }

    .operation-column {
        white-space: nowrap;
        padding: 0 5px; /* 添加左右边距 */
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center; /* 垂直居中 */
        flex-wrap: nowrap;
        height: 100%; /* 确保容器占满单元格高度 */
        width: 100%; /* 确保容器占满单元格宽度 */
        box-sizing: border-box; /* 确保内边距不增加总宽度 */
    }

        .operation-column .pear-btn {
            margin-right: 6px; /* 保持原有按钮间距 */
            padding: 0 8px; /* 保持按钮内边距 */
            height: 30px; /* 保持按钮高度 */
            line-height: 30px; /* 行高与高度一致 */
            min-width: 44px; /* 按钮最小宽度 */
            text-align: center;
            flex-grow: 1; /* 按钮自动扩展占用空间 */
            @* flex-basis: 0; /* 所有按钮基础宽度相同 */ *@
        }

            .operation-column .pear-btn:last-child {
                margin-right: 0;
            }

    /* 确保表格单元格内容居中 */
    .layui-table-cell {
        text-align: center;
        padding: 0 2px; /* 保持单元格内边距 */
    }

    /* 固定列样式优化 */
    .layui-table-fixed {
        height: auto !important;
    }

    /* 确保表格右侧固定列正确显示 */
    .layui-table-fixed-r {
        right: 0 !important;
    }

        /* 优化表格右侧固定列的单元格 */
        .layui-table-fixed-r .layui-table-cell {
            padding: 0;
        }
</style>

<div class="layui-card">
    <div class="layui-card-header">@T("数据库管理")</div>
    <div class="layui-card-body">
        <div class="table-body">
            <table class="layui-hide" id="tablist" lay-filter="tool"></table>
        </div>
    </div>
</div>

<script asp-location="Footer">
    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;

        
                 // 按钮配置集中定义
        var operationButtons = [
            @if (this.Has((PermissionFlags)1))
            {
                @:{ text: '@T("备份")', event: 'Backup', class: 'pear-btn-primary' },
                @:{ text: '@T("备份并压缩")', event: 'info', class: 'pear-btn-primary' },
                @:{ text: '@T("下载")', event: 'Download', class: 'pear-btn-primary' },
            }
            @if (this.Has((PermissionFlags)64))
            {
                @:{ text: '@T("管理")', event: 'Manage', class: 'pear-btn-primary' },
            }
        ];

        // 将按钮配置保存到全局变量，供模板使用
        window.operationButtons = operationButtons;

        // 动态计算操作列宽度的函数
        function calculateOperationColumnWidth() {
            try {
                var buttonMargin = 6; // 按钮间距
                var extraPadding = 24; // 额外边距
                var baseButtonPadding = 20; // 按钮内边距

                if (operationButtons.length === 0) {
                    return 100;
                }

                var totalWidth = 0;

                operationButtons.forEach(function(button) {
                    // 调整文字宽度估算：中文字符约16px，英文字符约9px
                    var text = button.text;
                    var textWidth = 0;
                    for (var i = 0; i < text.length; i++) {
                        var char = text.charAt(i);
                        if (/[\u4e00-\u9fa5]/.test(char)) {
                            textWidth += 16; // 中文字符
                        } else {
                            textWidth += 9;  // 英文字符
                        }
                    }

                    var buttonWidth = textWidth + baseButtonPadding;
                    totalWidth += buttonWidth;
                });

                // 加上按钮间距和额外边距
                totalWidth += (operationButtons.length - 1) * buttonMargin + extraPadding;

                // 设置最小宽度
                if (totalWidth < 120) {
                    totalWidth = 120;
                }

                console.log('计算的操作列宽度:', totalWidth, '按钮数量:', operationButtons.length, '按钮配置:', operationButtons);
                return totalWidth;
            } catch (error) {
                console.error('计算操作列宽度时出错:', error);
                return 200;
            }
        }

        // 应用操作列宽度的函数
        function applyOperationColumnWidth() {
            try {
                var calculatedWidth = calculateOperationColumnWidth();

                var fixedRightHeader = $('.layui-table-fixed-r .layui-table-header table thead tr th:last-child');
                var fixedRightBody = $('.layui-table-fixed-r .layui-table-body table tbody tr td:last-child');

                if (fixedRightHeader.length > 0) {
                    fixedRightHeader.css('width', calculatedWidth + 'px');
                    fixedRightHeader.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                if (fixedRightBody.length > 0) {
                    fixedRightBody.css('width', calculatedWidth + 'px');
                    fixedRightBody.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                var mainTableHeader = $('#tablist').next('.layui-table-view').find('.layui-table-header table thead tr th:last-child');
                var mainTableBody = $('#tablist').next('.layui-table-view').find('.layui-table-body table tbody tr td:last-child');

                if (mainTableHeader.length > 0) {
                    mainTableHeader.css('width', calculatedWidth + 'px');
                    mainTableHeader.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                if (mainTableBody.length > 0) {
                    mainTableBody.css('width', calculatedWidth + 'px');
                    mainTableBody.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                console.log('已应用操作列宽度:', calculatedWidth);
            } catch (error) {
                console.error('应用操作列宽度时出错:', error);
            }
        }

        // 监听窗口大小变化
        $(window).on('resize', function() {
            try {
                setTimeout(function() {
                    applyOperationColumnWidth();
                    console.log('窗口大小变化，已重新调整操作列宽度');
                }, 100);
            } catch (error) {
                console.error('窗口resize时出错:', error);
            }
        });

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetPage")'
            //, page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 80
            , cols: [[
                { field: 'Name', title: '@T("名称")' }
                , { field: 'Type', title: '@T("类型")', toolbar: '#Type' }
                , { field: 'ConnStr', title: '@T("链接字符串")', sort: true }
                , { field: 'Version', title: '@T("版本")', sort: true, width: 100 }
                , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', align: "center", width: calculateOperationColumnWidth() }
            ]]
            //, limit: 16
            , limits: [10, 16, 20, 30, 50, 100]
            , height: 'full-106'
            , id: 'tables'
            , done: function (res, curr, count) {
                try {
                    // 延迟执行，确保DOM已经渲染完成
                    setTimeout(function() {
                        applyOperationColumnWidth();
                    }, 200);
                } catch (error) {
                    console.error('表格done回调中出错:', error);
                }
            }
        });

        var active = {
            reload: function () {
                table.reload('tables');
            }
        }

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === "Backup") {
                $.post("@Url.Action("Backup")", { dbName: data.Name }, (res) => {
                    if (res.success) {
                        os.success('@T("备份成功")');
                    } else {
                        os.warning(data.msg);
                    }
                })
            } else if (obj.event === "info") {
                $.post("@Url.Action("BackupAndCompress")", { dbName: data.Name }, (res) => {
                    if (res.success) {
                        os.success('@T("备份并压缩成功")');
                    } else {
                        os.warning(data.msg);
                    }
                })
            } else if (obj.event === "Download") {
                var href = '@Url.Action("Download")?dbName=' + data.Name;
                $(this).attr("href", href);
            } else if (obj.event === "Edit") {
                layuiIndex = os.OpenNoTop('@T("编辑数据库链接")', "@Url.Action("EditSetting")?Name=" + data.Name, '420px', '350px', function () {
                    if ($("#state").val() == 1) abp.notify.success("@T("编辑成功")");
                    active.reload();
                });
            } else if (obj.event === "Manage") {
                layuiIndex = os.Open('@T("管理")', "@Url.Action("Manage")?Name=" + data.Name, '100%', '100%');
            }
        })

        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'refresh') {
                active.reload();
            }
        });

    });
</script>

<script type="text/html" id="tool">
    <div class="operation-column">
    {{#  layui.each(window.operationButtons, function(index, button){ }}
        <a class="pear-btn {{button.class}} pear-btn-xs" lay-event="{{button.event}}">{{button.text}}</a>
    {{#  }); }}
    </div>
</script>

<script type="text/html" id="Type">
    {{# if(d.Type == "6") { }}
    <span class="layui-badge layui-bg-green">@T("SQLite")</span>
    {{# } else if(d.Type == "4") { }}
    <span class="layui-badge layui-bg-cyan"> @T("MySql")</span>
    {{# } else if(d.Type == "8") { }}
    <span class="layui-badge layui-bg-blue"> @T("PostgreSQL")</span>
    {{# } }}
</script>