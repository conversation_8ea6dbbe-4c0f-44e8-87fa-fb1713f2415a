<!DOCTYPE html>
      <html>
      <head>
      <title>IOTData</title>
      <meta charset="utf-8" />
      <style>@charset "UTF-8";
html,
body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote {
  margin: 0;
  padding: 0;
  font-weight: normal;
  -webkit-font-smoothing: antialiased;
}

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 6px;
}

/* 外层轨道 */
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset006pxrgba(255, 0, 0, 0.3);
  background: rgba(0, 0, 0, 0.1);
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
}

::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(0, 0, 0, 0.2);
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimSun, sans-serif;
  font-size: 13px;
  line-height: 25px;
  color: #393838;
  position: relative;
}

table {
  margin: 10px 0 15px 0;
  border-collapse: collapse;
}

td,
th {
  border: 1px solid #ddd;
  padding: 3px 10px;
}

th {
  padding: 5px 10px;
}

a, a:link, a:visited {
  color: #34495e;
  text-decoration: none;
}

a:hover, a:focus {
  color: #59d69d;
  text-decoration: none;
}

a img {
  border: none;
}

p {
  padding-left: 10px;
  margin-bottom: 9px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #404040;
  line-height: 36px;
}

h1 {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 16px;
  font-size: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ddd;
  line-height: 50px;
}

h2 {
  font-size: 28px;
  padding-top: 10px;
  padding-bottom: 10px;
}

h3 {
  clear: both;
  font-weight: 400;
  margin-top: 20px;
  margin-bottom: 20px;
  border-left: 3px solid #59d69d;
  padding-left: 8px;
  font-size: 18px;
}

h4 {
  font-size: 16px;
}

h5 {
  font-size: 14px;
}

h6 {
  font-size: 13px;
}

hr {
  margin: 0 0 19px;
  border: 0;
  border-bottom: 1px solid #ccc;
}

blockquote {
  padding: 13px 13px 21px 15px;
  margin-bottom: 18px;
  font-family: georgia, serif;
  font-style: italic;
}

blockquote:before {
  font-size: 40px;
  margin-left: -10px;
  font-family: georgia, serif;
  color: #eee;
}

blockquote p {
  font-size: 14px;
  font-weight: 300;
  line-height: 18px;
  margin-bottom: 0;
  font-style: italic;
}

code,
pre {
  font-family: Monaco, Andale Mono, Courier New, monospace;
}

code {
  background-color: #fee9cc;
  color: rgba(0, 0, 0, 0.75);
  padding: 1px 3px;
  font-size: 12px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}

pre {
  display: block;
  padding: 14px;
  margin: 0 0 18px;
  line-height: 16px;
  font-size: 11px;
  border: 1px solid #d9d9d9;
  white-space: pre-wrap;
  word-wrap: break-word;
  background: #f6f6f6;
}

pre code {
  background-color: #f6f6f6;
  color: #737373;
  font-size: 11px;
  padding: 0;
}

sup {
  font-size: 0.83em;
  vertical-align: super;
  line-height: 0;
}

* {
  -webkit-print-color-adjust: exact;
}

@media print {
  body,
  code,
  pre code,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: black;
  }

  table,
  pre {
    page-break-inside: avoid;
  }
}
html,
body {
  height: 100%;
}

.table-of-contents {
  position: fixed;
  top: 61px;
  left: 0;
  bottom: 0;
  overflow-x: hidden;
  overflow-y: auto;
  width: 260px;
}

.table-of-contents > ul > li > a {
  font-size: 20px;
  margin-bottom: 16px;
  margin-top: 16px;
}

.table-of-contents ul {
  overflow: auto;
  margin: 0px;
  height: 100%;
  padding: 0px 0px;
  box-sizing: border-box;
  list-style-type: none;
}

.table-of-contents ul li {
  padding-left: 20px;
}

.table-of-contents a {
  padding: 2px 0px;
  display: block;
  text-decoration: none;
}

.content-right {
  max-width: 700px;
  margin-left: 290px;
  padding-left: 70px;
  flex-grow: 1;
}
.content-right h2:target {
  padding-top: 80px;
}

body > p {
  margin-left: 30px;
}

body > table {
  margin-left: 30px;
}

body > pre {
  margin-left: 30px;
}

.curProject {
  position: fixed;
  top: 20px;
  font-size: 25px;
  color: black;
  margin-left: -240px;
  width: 240px;
  padding: 5px;
  line-height: 25px;
  box-sizing: border-box;
}

.g-doc {
  margin-top: 56px;
  padding-top: 24px;
  display: flex;
}

.curproject-name {
  font-size: 42px;
}

.m-header {
  background: #32363a;
  height: 56px;
  line-height: 56px;
  padding-left: 60px;
  display: flex;
  align-items: center;
  position: fixed;
  z-index: 9;
  top: 0;
  left: 0;
  right: 0;
}
.m-header .title {
  font-size: 22px;
  color: #fff;
  font-weight: normal;
  -webkit-font-smoothing: antialiased;
  margin: 0;
  margin-left: 16px;
  padding: 0;
  line-height: 56px;
  border: none;
}
.m-header .nav {
  color: #fff;
  font-size: 16px;
  position: absolute;
  right: 32px;
  top: 0;
}
.m-header .nav a {
  color: #fff;
  margin-left: 16px;
  padding: 8px;
  transition: color .2s;
}
.m-header .nav a:hover {
  color: #59d69d;
}

.m-footer {
  border-top: 1px solid #ddd;
  padding-top: 16px;
  padding-bottom: 16px;
}

/*# sourceMappingURL=defaultTheme.css.map */
</style>
      </head>
      <body>
        <div class="m-header">
          <a href="#" style="display: inherit;"><svg class="svg" width="32px" height="32px" viewBox="0 0 64 64" version="1.1"><title>Icon</title><desc>Created with Sketch.</desc><defs><linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1"><stop stop-color="#FFFFFF" offset="0%"></stop><stop stop-color="#F2F2F2" offset="100%"></stop></linearGradient><circle id="path-2" cx="31.9988602" cy="31.9988602" r="2.92886048"></circle><filter x="-85.4%" y="-68.3%" width="270.7%" height="270.7%" filterUnits="objectBoundingBox" id="filter-3"><feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset><feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur><feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.159703351 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix></filter></defs><g id="首页" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="大屏幕"><g id="Icon"><circle id="Oval-1" fill="url(#linearGradient-1)" cx="32" cy="32" r="32"></circle><path d="M36.7078009,31.8054514 L36.7078009,51.7110548 C36.7078009,54.2844537 34.6258634,56.3695395 32.0579205,56.3695395 C29.4899777,56.3695395 27.4099998,54.0704461 27.4099998,51.7941246 L27.4099998,31.8061972 C27.4099998,29.528395 29.4909575,27.218453 32.0589004,27.230043 C34.6268432,27.241633 36.7078009,29.528395 36.7078009,31.8054514 Z" id="blue" fill="#2359F1" fill-rule="nonzero"></path><path d="M45.2586091,17.1026914 C45.2586091,17.1026914 45.5657231,34.0524383 45.2345291,37.01141 C44.9033351,39.9703817 43.1767091,41.6667796 40.6088126,41.6667796 C38.040916,41.6667796 35.9609757,39.3676862 35.9609757,37.0913646 L35.9609757,17.1034372 C35.9609757,14.825635 38.0418959,12.515693 40.6097924,12.527283 C43.177689,12.538873 45.2586091,14.825635 45.2586091,17.1026914 Z" id="green" fill="#57CF27" fill-rule="nonzero" transform="translate(40.674608, 27.097010) rotate(60.000000) translate(-40.674608, -27.097010) "></path><path d="M28.0410158,17.0465598 L28.0410158,36.9521632 C28.0410158,39.525562 25.9591158,41.6106479 23.3912193,41.6106479 C20.8233227,41.6106479 18.7433824,39.3115545 18.7433824,37.035233 L18.7433824,17.0473055 C18.7433824,14.7695034 20.8243026,12.4595614 23.3921991,12.4711513 C25.9600956,12.4827413 28.0410158,14.7695034 28.0410158,17.0465598 Z" id="red" fill="#FF561B" fill-rule="nonzero" transform="translate(23.392199, 27.040878) rotate(-60.000000) translate(-23.392199, -27.040878) "></path><g id="inner-round"><use fill="black" fill-opacity="1" filter="url(#filter-3)" xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#path-2"></use><use fill="#F7F7F7" fill-rule="evenodd" xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#path-2"></use></g></g></g></g></svg></a>
          <a href="#"><h1 class="title">YAPI 接口文档</h1></a>
          <div class="nav">
            <a href="https://hellosean1025.github.io/yapi/">YApi</a>
          </div>
        </div>
        <div class="g-doc">
          <div class="table-of-contents"><ul><li><a href="#api">Api</a><ul><li><a href="#u670du52a1u5668u4fe1u606fuff0cu7528u6237u5065u5eb7u68c0u6d4b0a3ca20id3du670du52a1u5668u4fe1u606fuff0cu7528u6237u5065u5eb7u68c0u6d4b4433e203ca3e">服务器信息，用户健康检测
<a id=服务器信息，用户健康检测443> </a></a></li><li><a href="#u83b7u53d6u6240u6709u63a5u53e30a3ca20id3du83b7u53d6u6240u6709u63a5u53e34433e203ca3e">获取所有接口
<a id=获取所有接口443> </a></a></li></ul></li><li><a href="#app">App</a><ul><li><a href="#u83b7u53d6u8bbeu5907u5c5eu60270a3ca20id3du83b7u53d6u8bbeu5907u5c5eu60274463e203ca3e">获取设备属性
<a id=获取设备属性446> </a></a></li><li><a href="#u8bbeu7f6eu8bbeu5907u5c5eu60270a3ca20id3du8bbeu7f6eu8bbeu5907u5c5eu60274463e203ca3e">设置设备属性
<a id=设置设备属性446> </a></a></li><li><a href="#u8c03u7528u8bbeu5907u670du52a10a3ca20id3du8c03u7528u8bbeu5907u670du52a14463e203ca3e">调用设备服务
<a id=调用设备服务446> </a></a></li></ul></li><li><a href="#device">Device</a><ul><li><a href="#u4e0bu884cu901au77e50a3ca20id3du4e0bu884cu901au77e54493e203ca3e">下行通知
<a id=下行通知449> </a></a></li><li><a href="#u5347u7ea7u68c0u67e50a3ca20id3du5347u7ea7u68c0u67e54493e203ca3e">升级检查
<a id=升级检查449> </a></a></li><li><a href="#u83b7u53d6u5b50u8bbeu59070a3ca20id3du83b7u53d6u5b50u8bbeu59074493e203ca3e">获取子设备
<a id=获取子设备449> </a></a></li><li><a href="#u83b7u53d6u8bbeu5907u901au90530a3ca20id3du83b7u53d6u8bbeu5907u901au90534493e203ca3e">获取设备通道
<a id=获取设备通道449> </a></a></li><li><a href="#u8bbeu5907u5fc3u8df30a3ca20id3du8bbeu5907u5fc3u8df34493e203ca3e">设备心跳
<a id=设备心跳449> </a></a></li><li><a href="#u8bbeu5907u6ce8u95000a3ca20id3du8bbeu5907u6ce8u95004493e203ca3e">设备注销
<a id=设备注销449> </a></a></li><li><a href="#u8bbeu5907u767bu5f550a3ca20id3du8bbeu5907u767bu5f554493e203ca3e">设备登录
<a id=设备登录449> </a></a></li></ul></li><li><a href="#filestore">FileStore</a><ul><li><a href="#u8bbeu5907u6587u4ef6u4e0au4f200a3ca20id3du8bbeu5907u6587u4ef6u4e0au4f204523e203ca3e">设备文件上传
<a id=设备文件上传452> </a></a></li></ul></li><li><a href="#thing">Thing</a><ul><li><a href="#u4e0au62a5u8bbeu5907u5c5eu60270a3ca20id3du4e0au62a5u8bbeu5907u5c5eu60274553e203ca3e">上报设备属性
<a id=上报设备属性455> </a></a></li><li><a href="#u4e0au62a5u8bbeu5907u5f71u5b500a3ca20id3du4e0au62a5u8bbeu5907u5f71u5b504553e203ca3e">上报设备影子
<a id=上报设备影子455> </a></a></li><li><a href="#u83b7u53d6u8bbeu5907u5c5eu60270a3ca20id3du83b7u53d6u8bbeu5907u5c5eu60274553e203ca3e">获取设备属性
<a id=获取设备属性455> </a></a></li><li><a href="#u83b7u53d6u8bbeu5907u5f71u5b500a3ca20id3du83b7u53d6u8bbeu5907u5f71u5b504553e203ca3e">获取设备影子
<a id=获取设备影子455> </a></a></li><li><a href="#u83b7u53d6u8bbeu5907u6240u5c5eu4ea7u54c1u7684u7269u6a21u578b0a3ca20id3du83b7u53d6u8bbeu5907u6240u5c5eu4ea7u54c1u7684u7269u6a21u578b4553e203ca3e">获取设备所属产品的物模型
<a id=获取设备所属产品的物模型455> </a></a></li><li><a href="#u83b7u53d6u8bbeu5907u70b9u4f4du88680a3ca20id3du83b7u53d6u8bbeu5907u70b9u4f4du88684553e203ca3e">获取设备点位表
<a id=获取设备点位表455> </a></a></li><li><a href="#u8bbeu5907u4e8bu4ef6u4e0au62a50a3ca20id3du8bbeu5907u4e8bu4ef6u4e0au62a54553e203ca3e">设备事件上报
<a id=设备事件上报455> </a></a></li><li><a href="#u8bbeu5907u6570u636eu4e0au62a50a3ca20id3du8bbeu5907u6570u636eu4e0au62a54553e203ca3e">设备数据上报
<a id=设备数据上报455> </a></a></li><li><a href="#u8bbeu5907u7aefu54cdu5e94u670du52a1u8c03u75280a3ca20id3du8bbeu5907u7aefu54cdu5e94u670du52a1u8c03u75284553e203ca3e">设备端响应服务调用
<a id=设备端响应服务调用455> </a></a></li><li><a href="#u8bbeu5907u7aefu67e5u8be2u914du7f6eu4fe1u606f0a3ca20id3du8bbeu5907u7aefu67e5u8be2u914du7f6eu4fe1u606f4553e203ca3e">设备端查询配置信息
<a id=设备端查询配置信息455> </a></a></li><li><a href="#u8bbeu5907u7aefu6ce8u518cu670du52a10a3ca20id3du8bbeu5907u7aefu6ce8u518cu670du52a14553e203ca3e">设备端注册服务
<a id=设备端注册服务455> </a></a></li></ul></li></ul></div>
          <div id="right" class="content-right">
           <h1 class="curproject-name"> IOTData </h1> 
<h1 id="api">Api</h1>
<p></p>
<h2 id="u670du52a1u5668u4fe1u606fuff0cu7528u6237u5065u5eb7u68c0u6d4b0a3ca20id3du670du52a1u5668u4fe1u606fuff0cu7528u6237u5065u5eb7u68c0u6d4b4433e203ca3e">服务器信息，用户健康检测
<a id=服务器信息，用户健康检测443> </a></h2>
<p></p>
<h3 id="">基本信息</h3>
<p><strong>Path：</strong> /Api/Info</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-2">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>state</td>
<td>否</td>
<td></td>
<td>状态信息</td>
</tr>
</tbody>
</table>
<h3 id="-3">返回数据</h3>
<pre><code class="language-javascript">Success
</code></pre>
<h2 id="u83b7u53d6u6240u6709u63a5u53e30a3ca20id3du83b7u53d6u6240u6709u63a5u53e34433e203ca3e">获取所有接口
<a id=获取所有接口443> </a></h2>
<p></p>
<h3 id="-4">基本信息</h3>
<p><strong>Path：</strong> /Api</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-5">请求参数</h3>
<h3 id="-6">返回数据</h3>
<pre><code class="language-javascript">Success
</code></pre>
<h1 id="app">App</h1>
<p></p>
<h2 id="u83b7u53d6u8bbeu5907u5c5eu60270a3ca20id3du83b7u53d6u8bbeu5907u5c5eu60274463e203ca3e">获取设备属性
<a id=获取设备属性446> </a></h2>
<p></p>
<h3 id="-7">基本信息</h3>
<p><strong>Path：</strong> /App/GetProperty</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-8">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>deviceId</td>
<td>否</td>
<td></td>
<td>设备编号</td>
</tr>
<tr>
<td>deviceCode</td>
<td>否</td>
<td></td>
<td>设备编码</td>
</tr>
</tbody>
</table>
<h3 id="-9">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> </span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">名称</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> value</span></td><td key=1><span></span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数值</span></td><td key=5></td></tr>
               </tbody>
              </table>
<h2 id="u8bbeu7f6eu8bbeu5907u5c5eu60270a3ca20id3du8bbeu7f6eu8bbeu5907u5c5eu60274463e203ca3e">设置设备属性
<a id=设置设备属性446> </a></h2>
<p></p>
<h3 id="-10">基本信息</h3>
<p><strong>Path：</strong> /App/SetProperty</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong></p>
<h3 id="-11">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>deviceCode</td>
<td>否</td>
<td></td>
<td>设备编码</td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">名称</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> value</span></td><td key=1><span></span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数值</span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="-12">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> </span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr>
               </tbody>
              </table>
<h2 id="u8c03u7528u8bbeu5907u670du52a10a3ca20id3du8c03u7528u8bbeu5907u670du52a14463e203ca3e">调用设备服务
<a id=调用设备服务446> </a></h2>
<p></p>
<h3 id="-13">基本信息</h3>
<p><strong>Path：</strong> /App/InvokeService</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong></p>
<h3 id="-14">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> deviceId</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">设备编号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> deviceCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">设备编码</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> serviceName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">服务名</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> inputData</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">入参</span></td><td key=5></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> expire</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">过期时间。未指定时表示不限制</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-5><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> timeout</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">超时时间。如果指定，则等待服务调用返回，单位毫秒</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr>
               </tbody>
              </table>
<h3 id="-15">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> id</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">服务编号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int64</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> status</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">服务状态</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p><p key=3><span style="font-weight: '700'">枚举: </span><span>0,1,2,3,4</span></p></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">返回数据</span></td><td key=5></td></tr>
               </tbody>
              </table>
<h1 id="device">Device</h1>
<p></p>
<h2 id="u4e0bu884cu901au77e50a3ca20id3du4e0bu884cu901au77e54493e203ca3e">下行通知
<a id=下行通知449> </a></h2>
<p></p>
<h3 id="-16">基本信息</h3>
<p><strong>Path：</strong> /Device/Notify</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-17">请求参数</h3>
<h3 id="-18">返回数据</h3>
<pre><code class="language-javascript">Success
</code></pre>
<h2 id="u5347u7ea7u68c0u67e50a3ca20id3du5347u7ea7u68c0u67e54493e203ca3e">升级检查
<a id=升级检查449> </a></h2>
<p></p>
<h3 id="-19">基本信息</h3>
<p><strong>Path：</strong> /Device/Upgrade</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-20">请求参数</h3>
<h3 id="-21">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> version</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">版本号</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> source</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">更新源，Url地址</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> fileHash</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">文件哈希</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> executor</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">更新后要执行的命令</span></td><td key=5></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> force</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否强制更新，不需要用户同意</span></td><td key=5></td></tr><tr key=0-5><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> description</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">描述</span></td><td key=5></td></tr>
               </tbody>
              </table>
<h2 id="u83b7u53d6u5b50u8bbeu59070a3ca20id3du83b7u53d6u5b50u8bbeu59074493e203ca3e">获取子设备
<a id=获取子设备449> </a></h2>
<p></p>
<h3 id="-22">基本信息</h3>
<p><strong>Path：</strong> /Device/GetDevices</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-23">请求参数</h3>
<h3 id="-24">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> </span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> code</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">名称</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">名称</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> productCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">产品编码</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> updateTime</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">更新时间。用于判断数据变化</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr>
               </tbody>
              </table>
<h2 id="u83b7u53d6u8bbeu5907u901au90530a3ca20id3du83b7u53d6u8bbeu5907u901au90534493e203ca3e">获取设备通道
<a id=获取设备通道449> </a></h2>
<p></p>
<h3 id="-25">基本信息</h3>
<p><strong>Path：</strong> /Device/GetChannels</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-26">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>deviceCode</td>
<td>否</td>
<td></td>
<td>设备编码</td>
</tr>
</tbody>
</table>
<h3 id="-27">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> </span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> id</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">编号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">名称</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> protocol</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">协议</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> address</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">设备地址。串口号COM1，或/dev/ttyAMA2，或者ModbusTCP的设备地址，127.0.0.1:502</span></td><td key=5></td></tr><tr key=0-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> baudrate</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">波特率。默认9600</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> host</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">从站号。默认1</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pollingTime</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">采集间隔。默认1000ms</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> readFunctionCode</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">读功能码。Modbus读取功能码，ReadCoil_0x01/ReadHoldingRegister_0x03</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> writeFunctionCode</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">写功能码。Modbus写入功能码，WriteSingleCoil_0x05/WriteRegister_0x06</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> updateTime</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">更新时间。用于判断数据变化</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr>
               </tbody>
              </table>
<h2 id="u8bbeu5907u5fc3u8df30a3ca20id3du8bbeu5907u5fc3u8df34493e203ca3e">设备心跳
<a id=设备心跳449> </a></h2>
<p></p>
<h3 id="-28">基本信息</h3>
<p><strong>Path：</strong> /Device/Ping</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong></p>
<h3 id="-29">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> memory</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">内存大小</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int64</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> availableMemory</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">可用内存大小</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int64</span></p></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> totalSize</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">磁盘大小。应用所在盘</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int64</span></p></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> availableFreeSpace</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">磁盘可用空间。应用所在盘</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int64</span></p></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> cpuRate</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">CPU使用率</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>float</span></p></td></tr><tr key=0-5><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> temperature</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">温度</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>double</span></p></td></tr><tr key=0-6><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> battery</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">电量</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>double</span></p></td></tr><tr key=0-7><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> ip</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">本地IP</span></td><td key=5></td></tr><tr key=0-8><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> uptime</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开机时间，单位s</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-9><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> time</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">本地UTC时间。ms毫秒</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int64</span></p></td></tr><tr key=0-10><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> delay</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">延迟。ms毫秒</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr>
               </tbody>
              </table>
<h3 id="-30">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> time</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">本地时间。ms毫秒</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int64</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> serverTime</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">服务器时间</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int64</span></p></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> period</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">心跳周期。单位秒</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> token</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">令牌。现有令牌即将过期时，颁发新的令牌</span></td><td key=5></td></tr>
               </tbody>
              </table>
<h2 id="u8bbeu5907u6ce8u95000a3ca20id3du8bbeu5907u6ce8u95004493e203ca3e">设备注销
<a id=设备注销449> </a></h2>
<p></p>
<h3 id="-31">基本信息</h3>
<p><strong>Path：</strong> /Device/Logout</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-32">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>reason</td>
<td>否</td>
<td></td>
<td>注销原因</td>
</tr>
</tbody>
</table>
<h3 id="-33">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">节点编码</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> secret</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">节点密钥</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">名称</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> token</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">令牌</span></td><td key=5></td></tr>
               </tbody>
              </table>
<h2 id="u8bbeu5907u767bu5f550a3ca20id3du8bbeu5907u767bu5f554493e203ca3e">设备登录
<a id=设备登录449> </a></h2>
<p></p>
<h3 id="-34">基本信息</h3>
<p><strong>Path：</strong> /Device/Login</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong></p>
<h3 id="-35">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">设备编码</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> secret</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">设备密钥</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> productKey</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">产品证书</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> productSecret</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">产品密钥</span></td><td key=5></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">名称。可用于标识设备的名称</span></td><td key=5></td></tr><tr key=0-5><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> version</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">版本</span></td><td key=5></td></tr><tr key=0-6><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> ip</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">本地IP地址</span></td><td key=5></td></tr><tr key=0-7><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> uuid</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">唯一标识</span></td><td key=5></td></tr><tr key=0-8><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> time</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">本地UTC时间</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int64</span></p></td></tr>
               </tbody>
              </table>
<h3 id="-36">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">节点编码</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> secret</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">节点密钥</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">名称</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> token</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">令牌</span></td><td key=5></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> time</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">服务器时间</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int64</span></p></td></tr>
               </tbody>
              </table>
<h1 id="filestore">FileStore</h1>
<p></p>
<h2 id="u8bbeu5907u6587u4ef6u4e0au4f200a3ca20id3du8bbeu5907u6587u4ef6u4e0au4f204523e203ca3e">设备文件上传
<a id=设备文件上传452> </a></h2>
<p></p>
<h3 id="-37">基本信息</h3>
<p><strong>Path：</strong> /FileStore/HttpUploadFile</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-38">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>path</td>
<td>否</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> </span></td><td key=1><span></span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="-39">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> time</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">本地时间。ms毫秒</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int64</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> serverTime</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">服务器时间</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int64</span></p></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> loginInfo</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">节点登录信息</span></td><td key=5></td></tr><tr key=0-2-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> code</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">设备编码</span></td><td key=5></td></tr><tr key=0-2-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> secret</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">设备密钥</span></td><td key=5></td></tr><tr key=0-2-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> productKey</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">产品证书</span></td><td key=5></td></tr><tr key=0-2-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> productSecret</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">产品密钥</span></td><td key=5></td></tr><tr key=0-2-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">名称。可用于标识设备的名称</span></td><td key=5></td></tr><tr key=0-2-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> version</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">版本</span></td><td key=5></td></tr><tr key=0-2-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ip</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">本地IP地址</span></td><td key=5></td></tr><tr key=0-2-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> uuid</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">唯一标识</span></td><td key=5></td></tr><tr key=0-2-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> time</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">本地UTC时间</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int64</span></p></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> filestrame</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-3-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> canRead</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-3-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> canWrite</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-3-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> canSeek</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-3-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> canTimeout</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-3-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> length</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int64</span></p></td></tr><tr key=0-3-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> position</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int64</span></p></td></tr><tr key=0-3-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> readTimeout</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-3-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> writeTimeout</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr>
               </tbody>
              </table>
<h1 id="thing">Thing</h1>
<p></p>
<h2 id="u4e0au62a5u8bbeu5907u5c5eu60270a3ca20id3du4e0au62a5u8bbeu5907u5c5eu60274553e203ca3e">上报设备属性
<a id=上报设备属性455> </a></h2>
<p></p>
<h3 id="-40">基本信息</h3>
<p><strong>Path：</strong> /Thing/PostProperty</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong></p>
<h3 id="-41">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> deviceCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">设备编码</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> items</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">属性集合</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">名称</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> value</span></td><td key=1><span></span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数值</span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="-42">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> </span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr>
               </tbody>
              </table>
<h2 id="u4e0au62a5u8bbeu5907u5f71u5b500a3ca20id3du4e0au62a5u8bbeu5907u5f71u5b504553e203ca3e">上报设备影子
<a id=上报设备影子455> </a></h2>
<p></p>
<h3 id="-43">基本信息</h3>
<p><strong>Path：</strong> /Thing/PostShadow</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
设备影子是一个JSON文档，用于存储设备上报状态、应用程序期望状态信息。<br>
每个设备有且只有一个设备影子，设备可以通过MQTT获取和设置设备影子来同步状态，该同步可以是影子同步给设备，也可以是设备同步给影子。<br>
使用设备影子机制，设备状态变更，只需同步状态给设备影子一次，应用程序请求获取设备状态，不论应用程序请求数量，和设备是否联网在线，都可从设备影子中获取设备当前状态，实现应用程序与设备解耦。</p>
<h3 id="-44">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> deviceCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">设备编码</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> shadow</span></td><td key=1><span></span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">影子</span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="-45">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> </span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr>
               </tbody>
              </table>
<h2 id="u83b7u53d6u8bbeu5907u5c5eu60270a3ca20id3du83b7u53d6u8bbeu5907u5c5eu60274553e203ca3e">获取设备属性
<a id=获取设备属性455> </a></h2>
<p></p>
<h3 id="-46">基本信息</h3>
<p><strong>Path：</strong> /Thing/GetProperty</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-47">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>deviceCode</td>
<td>否</td>
<td></td>
<td>设备编码</td>
</tr>
</tbody>
</table>
<h3 id="-48">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> </span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">名称</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> value</span></td><td key=1><span></span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数值</span></td><td key=5></td></tr>
               </tbody>
              </table>
<h2 id="u83b7u53d6u8bbeu5907u5f71u5b500a3ca20id3du83b7u53d6u8bbeu5907u5f71u5b504553e203ca3e">获取设备影子
<a id=获取设备影子455> </a></h2>
<p></p>
<h3 id="-49">基本信息</h3>
<p><strong>Path：</strong> /Thing/GetShadow</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-50">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>deviceCode</td>
<td>否</td>
<td></td>
<td>设备编码</td>
</tr>
</tbody>
</table>
<h3 id="-51">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> </span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h2 id="u83b7u53d6u8bbeu5907u6240u5c5eu4ea7u54c1u7684u7269u6a21u578b0a3ca20id3du83b7u53d6u8bbeu5907u6240u5c5eu4ea7u54c1u7684u7269u6a21u578b4553e203ca3e">获取设备所属产品的物模型
<a id=获取设备所属产品的物模型455> </a></h2>
<p></p>
<h3 id="-52">基本信息</h3>
<p><strong>Path：</strong> /Thing/GetSpecification</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-53">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>deviceCode</td>
<td>否</td>
<td></td>
<td>设备编码</td>
</tr>
</tbody>
</table>
<h3 id="-54">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> schema</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">模式</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> profile</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">简介</span></td><td key=5></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> version</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">版本</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> productKey</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">产品编码</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> properties</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">属性</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-2-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> id</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">唯一标识</span></td><td key=5></td></tr><tr key=0-2-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">名称</span></td><td key=5></td></tr><tr key=0-2-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> required</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否必须</span></td><td key=5></td></tr><tr key=0-2-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> accessMode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">访问模式</span></td><td key=5></td></tr><tr key=0-2-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> dataType</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">类型规范</span></td><td key=5></td></tr><tr key=0-2-4-0><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> type</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">类型。float/text</span></td><td key=5></td></tr><tr key=0-2-4-1><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> specs</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数据规范</span></td><td key=5></td></tr><tr key=0-2-4-1-0><td key=0><span style="padding-left: 60px"><span style="color: #8c8a8a">├─</span> min</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">最小值</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>double</span></p></td></tr><tr key=0-2-4-1-1><td key=0><span style="padding-left: 60px"><span style="color: #8c8a8a">├─</span> max</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">最大值</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>double</span></p></td></tr><tr key=0-2-4-1-2><td key=0><span style="padding-left: 60px"><span style="color: #8c8a8a">├─</span> unit</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位</span></td><td key=5></td></tr><tr key=0-2-4-1-3><td key=0><span style="padding-left: 60px"><span style="color: #8c8a8a">├─</span> unitName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位名称</span></td><td key=5></td></tr><tr key=0-2-4-1-4><td key=0><span style="padding-left: 60px"><span style="color: #8c8a8a">├─</span> step</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">步进</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>double</span></p></td></tr><tr key=0-2-4-1-5><td key=0><span style="padding-left: 60px"><span style="color: #8c8a8a">├─</span> length</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">长度</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-2-4-1-6><td key=0><span style="padding-left: 60px"><span style="color: #8c8a8a">├─</span> bValue</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">布尔类型值</span></td><td key=5></td></tr><tr key=0-2-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> address</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">采集点位置信息</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> services</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">服务</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-3-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> id</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">唯一标识</span></td><td key=5></td></tr><tr key=0-3-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">名称</span></td><td key=5></td></tr><tr key=0-3-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> required</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否必须</span></td><td key=5></td></tr><tr key=0-3-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> type</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">调用类型。sync/async</span></td><td key=5></td></tr><tr key=0-3-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> description</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">描述</span></td><td key=5></td></tr><tr key=0-3-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> method</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">方法</span></td><td key=5></td></tr><tr key=0-3-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inputData</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">输入</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-3-6-0><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> id</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">唯一标识</span></td><td key=5></td></tr><tr key=0-3-6-1><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">名称</span></td><td key=5></td></tr><tr key=0-3-6-2><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> required</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否必须</span></td><td key=5></td></tr><tr key=0-3-6-3><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> accessMode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">访问模式</span></td><td key=5></td></tr><tr key=0-3-6-4><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> dataType</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">类型规范</span></td><td key=5></td></tr><tr key=0-3-6-4-0><td key=0><span style="padding-left: 60px"><span style="color: #8c8a8a">├─</span> type</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">类型。float/text</span></td><td key=5></td></tr><tr key=0-3-6-4-1><td key=0><span style="padding-left: 60px"><span style="color: #8c8a8a">├─</span> specs</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数据规范</span></td><td key=5></td></tr><tr key=0-3-6-4-1-0><td key=0><span style="padding-left: 80px"><span style="color: #8c8a8a">├─</span> min</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">最小值</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>double</span></p></td></tr><tr key=0-3-6-4-1-1><td key=0><span style="padding-left: 80px"><span style="color: #8c8a8a">├─</span> max</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">最大值</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>double</span></p></td></tr><tr key=0-3-6-4-1-2><td key=0><span style="padding-left: 80px"><span style="color: #8c8a8a">├─</span> unit</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位</span></td><td key=5></td></tr><tr key=0-3-6-4-1-3><td key=0><span style="padding-left: 80px"><span style="color: #8c8a8a">├─</span> unitName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位名称</span></td><td key=5></td></tr><tr key=0-3-6-4-1-4><td key=0><span style="padding-left: 80px"><span style="color: #8c8a8a">├─</span> step</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">步进</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>double</span></p></td></tr><tr key=0-3-6-4-1-5><td key=0><span style="padding-left: 80px"><span style="color: #8c8a8a">├─</span> length</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">长度</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-3-6-4-1-6><td key=0><span style="padding-left: 80px"><span style="color: #8c8a8a">├─</span> bValue</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">布尔类型值</span></td><td key=5></td></tr><tr key=0-3-6-5><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> address</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">采集点位置信息</span></td><td key=5></td></tr><tr key=0-3-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> outputData</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">输出</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-3-7-0><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> id</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">唯一标识</span></td><td key=5></td></tr><tr key=0-3-7-1><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">名称</span></td><td key=5></td></tr><tr key=0-3-7-2><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> required</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否必须</span></td><td key=5></td></tr><tr key=0-3-7-3><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> accessMode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">访问模式</span></td><td key=5></td></tr><tr key=0-3-7-4><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> dataType</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">类型规范</span></td><td key=5></td></tr><tr key=0-3-7-4-0><td key=0><span style="padding-left: 60px"><span style="color: #8c8a8a">├─</span> type</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">类型。float/text</span></td><td key=5></td></tr><tr key=0-3-7-4-1><td key=0><span style="padding-left: 60px"><span style="color: #8c8a8a">├─</span> specs</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数据规范</span></td><td key=5></td></tr><tr key=0-3-7-4-1-0><td key=0><span style="padding-left: 80px"><span style="color: #8c8a8a">├─</span> min</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">最小值</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>double</span></p></td></tr><tr key=0-3-7-4-1-1><td key=0><span style="padding-left: 80px"><span style="color: #8c8a8a">├─</span> max</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">最大值</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>double</span></p></td></tr><tr key=0-3-7-4-1-2><td key=0><span style="padding-left: 80px"><span style="color: #8c8a8a">├─</span> unit</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位</span></td><td key=5></td></tr><tr key=0-3-7-4-1-3><td key=0><span style="padding-left: 80px"><span style="color: #8c8a8a">├─</span> unitName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位名称</span></td><td key=5></td></tr><tr key=0-3-7-4-1-4><td key=0><span style="padding-left: 80px"><span style="color: #8c8a8a">├─</span> step</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">步进</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>double</span></p></td></tr><tr key=0-3-7-4-1-5><td key=0><span style="padding-left: 80px"><span style="color: #8c8a8a">├─</span> length</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">长度</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-3-7-4-1-6><td key=0><span style="padding-left: 80px"><span style="color: #8c8a8a">├─</span> bValue</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">布尔类型值</span></td><td key=5></td></tr><tr key=0-3-7-5><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> address</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">采集点位置信息</span></td><td key=5></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> events</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">事件</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-4-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> id</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">唯一标识</span></td><td key=5></td></tr><tr key=0-4-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">名称</span></td><td key=5></td></tr><tr key=0-4-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> required</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否必须</span></td><td key=5></td></tr><tr key=0-4-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> type</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">类型。info/warning/error</span></td><td key=5></td></tr><tr key=0-4-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> description</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">描述</span></td><td key=5></td></tr><tr key=0-4-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> method</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">方法</span></td><td key=5></td></tr><tr key=0-4-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> outputData</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">输出</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-4-6-0><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> id</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">唯一标识</span></td><td key=5></td></tr><tr key=0-4-6-1><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">名称</span></td><td key=5></td></tr><tr key=0-4-6-2><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> required</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否必须</span></td><td key=5></td></tr><tr key=0-4-6-3><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> accessMode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">访问模式</span></td><td key=5></td></tr><tr key=0-4-6-4><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> dataType</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">类型规范</span></td><td key=5></td></tr><tr key=0-4-6-4-0><td key=0><span style="padding-left: 60px"><span style="color: #8c8a8a">├─</span> type</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">类型。float/text</span></td><td key=5></td></tr><tr key=0-4-6-4-1><td key=0><span style="padding-left: 60px"><span style="color: #8c8a8a">├─</span> specs</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数据规范</span></td><td key=5></td></tr><tr key=0-4-6-4-1-0><td key=0><span style="padding-left: 80px"><span style="color: #8c8a8a">├─</span> min</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">最小值</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>double</span></p></td></tr><tr key=0-4-6-4-1-1><td key=0><span style="padding-left: 80px"><span style="color: #8c8a8a">├─</span> max</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">最大值</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>double</span></p></td></tr><tr key=0-4-6-4-1-2><td key=0><span style="padding-left: 80px"><span style="color: #8c8a8a">├─</span> unit</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位</span></td><td key=5></td></tr><tr key=0-4-6-4-1-3><td key=0><span style="padding-left: 80px"><span style="color: #8c8a8a">├─</span> unitName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位名称</span></td><td key=5></td></tr><tr key=0-4-6-4-1-4><td key=0><span style="padding-left: 80px"><span style="color: #8c8a8a">├─</span> step</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">步进</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>double</span></p></td></tr><tr key=0-4-6-4-1-5><td key=0><span style="padding-left: 80px"><span style="color: #8c8a8a">├─</span> length</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">长度</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-4-6-4-1-6><td key=0><span style="padding-left: 80px"><span style="color: #8c8a8a">├─</span> bValue</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">布尔类型值</span></td><td key=5></td></tr><tr key=0-4-6-5><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> address</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">采集点位置信息</span></td><td key=5></td></tr>
               </tbody>
              </table>
<h2 id="u83b7u53d6u8bbeu5907u70b9u4f4du88680a3ca20id3du83b7u53d6u8bbeu5907u70b9u4f4du88684553e203ca3e">获取设备点位表
<a id=获取设备点位表455> </a></h2>
<p></p>
<h3 id="-55">基本信息</h3>
<p><strong>Path：</strong> /Thing/GetPoints</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-56">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>deviceCode</td>
<td>否</td>
<td></td>
<td>设备编码</td>
</tr>
</tbody>
</table>
<h3 id="-57">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> </span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">名称</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> address</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">地址。常规地址 0x06，大地址 40000，位域地址 0x46000:05</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> type</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数据类型。来自物模型</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> length</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">大小。数据字节数，或字符串长度，Modbus寄存器一般占2个字节</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> readRule</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">读取规则。数据解析规则，表达式或脚本</span></td><td key=5></td></tr><tr key=0-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> writeRule</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">写入规则。数据反解析规则，表达式或脚本</span></td><td key=5></td></tr>
               </tbody>
              </table>
<h2 id="u8bbeu5907u4e8bu4ef6u4e0au62a50a3ca20id3du8bbeu5907u4e8bu4ef6u4e0au62a54553e203ca3e">设备事件上报
<a id=设备事件上报455> </a></h2>
<p></p>
<h3 id="-58">基本信息</h3>
<p><strong>Path：</strong> /Thing/PostEvent</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong></p>
<h3 id="-59">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> deviceCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">设备编码</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> items</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">事件集合</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> time</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">时间。Unix毫秒，UTC</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int64</span></p></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> type</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">事件类型。info/alert/error</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">名称。事件名称，例如LightOpen</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> remark</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">内容。事件详情</span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="-60">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> </span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr>
               </tbody>
              </table>
<h2 id="u8bbeu5907u6570u636eu4e0au62a50a3ca20id3du8bbeu5907u6570u636eu4e0au62a54553e203ca3e">设备数据上报
<a id=设备数据上报455> </a></h2>
<p></p>
<h3 id="-61">基本信息</h3>
<p><strong>Path：</strong> /Thing/PostData</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong></p>
<h3 id="-62">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> deviceCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">设备编码</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> items</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数据集合</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> time</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">时间。数据采集时间，UTC毫秒</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int64</span></p></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> topic</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">主题</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> data</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数据</span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="-63">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> </span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr>
               </tbody>
              </table>
<h2 id="u8bbeu5907u7aefu54cdu5e94u670du52a1u8c03u75280a3ca20id3du8bbeu5907u7aefu54cdu5e94u670du52a1u8c03u75284553e203ca3e">设备端响应服务调用
<a id=设备端响应服务调用455> </a></h2>
<p></p>
<h3 id="-64">基本信息</h3>
<p><strong>Path：</strong> /Thing/ServiceReply</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong></p>
<h3 id="-65">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> id</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">服务编号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int64</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> status</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">服务状态</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p><p key=3><span style="font-weight: '700'">枚举: </span><span>0,1,2,3,4</span></p></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">返回数据</span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="-66">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> </span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr>
               </tbody>
              </table>
<h2 id="u8bbeu5907u7aefu67e5u8be2u914du7f6eu4fe1u606f0a3ca20id3du8bbeu5907u7aefu67e5u8be2u914du7f6eu4fe1u606f4553e203ca3e">设备端查询配置信息
<a id=设备端查询配置信息455> </a></h2>
<p></p>
<h3 id="-67">基本信息</h3>
<p><strong>Path：</strong> /Thing/GetConfig</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-68">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>deviceCode</td>
<td>否</td>
<td></td>
<td>设备编码</td>
</tr>
</tbody>
</table>
<h3 id="-69">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody">
               </tbody>
              </table>
<h2 id="u8bbeu5907u7aefu6ce8u518cu670du52a10a3ca20id3du8bbeu5907u7aefu6ce8u518cu670du52a14553e203ca3e">设备端注册服务
<a id=设备端注册服务455> </a></h2>
<p></p>
<h3 id="-70">基本信息</h3>
<p><strong>Path：</strong> /Thing/RegisterService</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong></p>
<h3 id="-71">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> deviceCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">设备编码</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">服务名</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> displayName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">显示名</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> description</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">描述</span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="-72">返回数据</h3>
<pre><code class="language-javascript">Success
</code></pre>

            <footer class="m-footer">
              <p>Build by <a href="https://ymfe.org/">YMFE</a>.</p>
            </footer>
          </div>
        </div>
      </body>
      </html>
      