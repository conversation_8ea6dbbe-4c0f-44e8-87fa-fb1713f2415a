﻿using DG.Web.Framework;

using DH;
using DH.Core.Infrastructure;
using DH.Entity;

using HlktechIoT.Data;
using HlktechIoT.Services;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Caching;
using NewLife.Data;
using NewLife.IoT.ThingModels;
using NewLife.Log;
using NewLife.Serialization;

using Pek;
using Pek.Configs;
using Pek.Helpers;
using Pek.Models;
using Pek.Timing;

using System.ComponentModel;
using System.Dynamic;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Device.Controllers;

/// <summary>设备管理</summary>
[DisplayName("设备管理")]
[Description("用户项目之产品下的设备管理")]
[DeviceArea]
[DHMenu(80, ParentMenuName = "DeviceManager", ParentMenuDisplayName = "设备管理", ParentMenuUrl = "", ParentMenuOrder = 90, ParentIcon = "layui-icon-util", CurrentMenuUrl = "~/{area}/Devices", CurrentMenuName = "Devices", LastUpdate = "20240124")]
public class DevicesController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 80;

    private readonly ITracer _tracer;

    /// <summary>
    /// 缓存
    /// </summary>
    private readonly ICacheProvider _cache;

    public DevicesController(ITracer tracer, ICacheProvider cache)
    {
        _tracer = tracer;
        _cache = cache;
    }

    /// <summary>
    /// 设备管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("设备管理")]
    [HttpGet]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// 根据条件搜索
    /// </summary>
    /// <param name="key">产品名称/产品密钥等</param>
    /// <param name="PId">产品编号</param>
    /// <param name="Status">状态。-1为全部，0为在线，1为不在线，2为未激活</param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("设备查询")]
    public IActionResult GetList(String key, String PId, Int32 Status = -1, Int32 page = 1, Int32 limit = 10)
    {
        if (!key.IsNullOrWhiteSpace()) key = key.Trim();
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = Data.Device._.Id,
            Desc = true
        };

        IList<Data.Device> list;

        var UId = 0;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin && (modelRole.Roles.IsNullOrWhiteSpace() || !modelRole.Roles.Contains(",004,")))
        {
            UId = ManageProvider.User?.ID ?? -1;
        }

        list = Data.Device.Search(PId, key, UId, Status, pages);

        var data = list.Select(x => new { x.Id, x.NickName, ProductId = x.Product?.Code, x.Online, x.Code, x.ProductName, x.Enable, x.Version, x.Module, x.PostPeriod, x.LastLogin, RegisterTime = x.RegisterTime <= DateTime.MinValue ? false : true, ProductModule = x.Product?.ProductModule?.Code,x.ExpiredTime });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 搜索产品
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [HttpPost]
    [DisplayName("搜索产品")]
    public IActionResult SearchProduct(String keyword, Int32 page)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = Product._.Id,
            Desc = true
        };

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin && (modelRole.Roles.IsNullOrWhiteSpace() || !modelRole.Roles.Contains(",004,")))
        {
            res.data = Product.Search(keyword, ManageProvider.User?.ID ?? -1, pages).Select(e =>
            {
                var selected = false;

                return new Xmselect<Int32>
                {
                    name = e.Name + $"({e.Code})",
                    value = e.Id,
                    selected = selected
                };
            });
        }
        else
        {
            res.data = Product.Search(keyword, 0, pages).Select(e =>
            {
                var selected = false;

                return new Xmselect<Int32>
                {
                    name = e.Name + $"({e.Code})",
                    value = e.Id,
                    selected = selected
                };
            });
        }

        res.extdata = pages.PageCount;
        res.success = true;
        return Json(res);
    }

    /// <summary>
    /// 搜索固件
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize]
    [HttpPost]
    [DisplayName("搜索固件")]
    public IActionResult SearchFirmware(String keyword, String Module, Int32 page,String ProductKey)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = FirmwareInfo._.Id,
            Desc = true,
        };

        //var model = ProductModule.FindByCode(Module);

        res.data = FirmwareInfo.FindAll(FirmwareInfo._.ProductKey == ProductKey, pages).Select(e =>
        {
            var selected = false;

            return new Xmselect<Int32>
            {
                name = e.SVersions + $"({e.Product?.Name}:{(e.FType == 0 ? GetResource("正式") : GetResource("测试"))})",
                value = e.Id,
                selected = selected
            };
        });

        res.extdata = pages.PageCount;
        res.success = true;
        return Json(res);
    }

    /// <summary>固件升级</summary>
    /// <returns></returns>
    [DisplayName("固件升级")]
    [EntityAuthorize((PermissionFlags)16)]
    public IActionResult Upgrade(String Code)
    {
        //var model = Data.Device.FindById(Id);
        var model = Data.Device.FindByCode(Code);

        if (model == null)
        {
            return Content(GetResource("设备不存在"));
        }

        return View(model);
    }

    /// <summary>固件升级</summary>
    /// <returns></returns>
    [DisplayName("固件升级")]
    [EntityAuthorize((PermissionFlags)16)]
    [HttpPost]
    public async Task<IActionResult> Upgrade(String Code, Int32 FId, String IsForce)
    {
        var result = new DResult();

        //var model = Data.Device.FindById(Id);
        var model = Data.Device.FindByCode(Code);

        if (model == null)
        {
            return Json(new { success = false, msg = GetResource("设备不存在") });
        }

        //XTrace.WriteLine($"设备升级：{model.ToJson()}");
        //var model1 = Data.Device.FindByCode(model.Code);
        //XTrace.WriteLine($"设备升级111：{model1?.ToJson()}");

        //var model2 = Data.Device.Find(Data.Device._.Code == model.Code);
        //XTrace.WriteLine($"设备升级222：{model2?.ToJson()}");
        //var model3 = Data.Device.Find(Data.Device._.Id == model.Id);
        //XTrace.WriteLine($"设备升级333：{model3?.ToJson()}");

        if (!model.Online)
        {
            return Json(new { success = false, msg = GetResource("设备不在线") });
        }

        if (model.UpStatus == 1)
        {
            var _appService = EngineContext.Current.Resolve<AppService>();
            var rs = await _appService.InvokeService(model.Id, "CheckOTA", String.Empty).ConfigureAwait(false);
            if (rs == null || rs.Status < ServiceStatus.已完成)
            {
                if (IsForce != "on") // 是否强制升级，强制升级不受设备升级状态限制
                {
                    return Json(new { success = false, msg = GetResource("设备升级中") });
                }
            }
        }

        var modelFirmwareInfo = FirmwareInfo.FindById(FId);
        if (modelFirmwareInfo == null)
        {
            return Json(new { success = false, msg = GetResource("固件不存在") });
        }

        var modelUpgradeLogs = new UpgradeLogs();
        modelUpgradeLogs.FirmwareInfoId = FId;
        modelUpgradeLogs.FirmwareInfoOriginFileName = modelUpgradeLogs.FirmwareInfo?.OriginFileName ?? String.Empty;
        modelUpgradeLogs.IotId = model.Code;
        modelUpgradeLogs.Status = 0;
        modelUpgradeLogs.LastSVersion = model.Version;
        modelUpgradeLogs.SVersions = modelFirmwareInfo.SVersions;
        modelUpgradeLogs.Insert();

        var _queue = EngineContext.Current.Resolve<QueueService>();  // 队列往设备发升级指令

        _queue.Publish(model.Code!, new ServiceModel
        {
            Type = "Upgrade",
            Name = $"{modelFirmwareInfo.SVersions} Upgrade",
            //InputData = $"MsgId={modelUpgradeLogs.Id};Version={modelFirmwareInfo.SVersions};Source={DHSetting.Current.CurDomainUrl}/{modelFirmwareInfo.FileUrl};FileHash={modelFirmwareInfo.FileHash};FileSize={modelFirmwareInfo.FileSize};Executor={modelFirmwareInfo.Executor};Force={modelFirmwareInfo.Force};Description={modelFirmwareInfo.Remark}",
            InputData = $"MsgId={modelUpgradeLogs.Id};FId={modelFirmwareInfo.Id};Version={modelFirmwareInfo.SVersions};Source={DHSetting.Current.CurDomainUrl + "/api/device/v1.01/devicecommon/otaupgrade"};FileHash={modelFirmwareInfo.FileHash};FileSize={(modelFirmwareInfo.DataSize == 0 ? modelFirmwareInfo.FileSize : modelFirmwareInfo.DataSize)};Executor={modelFirmwareInfo.Executor};Force={modelFirmwareInfo.Force};Description={modelFirmwareInfo.Remark}",
            DeviceCode = model.Code,
        });

        model.UpStatus = 1;
        model.Update();

        result.success = true;
        result.msg = GetResource("固件升级指令下发成功");
        return Json(result);
    }

    /// <summary>修改状态</summary>
    /// <returns></returns>
    [DisplayName("修改状态")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult ModifyState(Int32 Id, Boolean Status)
    {
        var result = new DResult();

        var model = Data.Device.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("状态调整出错");
            return Json(result);
        }

        model.Enable = Status;
        model.Update();

        var _queue = EngineContext.Current.Resolve<QueueService>();  // 队列往设备发升级指令

        if (!model.Enable)
        {
            _queue.Publish(new ServiceModel
            {
                Type = "OnLine",
                InputData = $"Status=8;Time={UnixTime.ToTimestamp()};ProductKey={model.Product?.Code};DeviceCode={model.Code}",
            });
        }
        else
        {
            _queue.Publish(new ServiceModel
            {
                Type = "OnLine",
                InputData = $"Status=1;Time={UnixTime.ToTimestamp()};ProductKey={model.Product?.Code};DeviceCode={model.Code}",
            });
        }

        result.success = true;
        result.msg = GetResource("状态调整成功");

        return Json(result);
    }

    /// <summary>设备绑定用户</summary>
    /// <returns></returns>
    [DisplayName("设备绑定用户")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult BindUser(Int32 Id)
    {
        dynamic viewModel = new ExpandoObject();

        var model = Data.Device.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("设备不存在"));
        }

        viewModel.Device = model;

        return View(viewModel);
    }

    /// <summary>
    /// 根据条件搜索
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("APP用户查询")]
    public IActionResult GetUserList(Int32 Id,String key,Int32 page = 1,Int32 limit = 10)
    {
        var model = Data.Device.FindById(Id);
        if (model == null)
        {
            return Json(new { code = 1, msg = "fail", count = 0 });
        }

        var list = new List<String>();

        var modelDeviceAssociatedUsers = DeviceAssociatedUsers.FindByDeviceName(model.Code);
        if (modelDeviceAssociatedUsers != null)
        {
            list.Add(modelDeviceAssociatedUsers.IdentityId);
            list.AddRange(modelDeviceAssociatedUsers.SubUsers?.Split(',').ToList() ?? []);  // 子用户
        }

        var listUser = AppUser.FindAllByIdentityIds(list).Select(e =>
        {
            var role = String.Empty;
            if (e.IdentityId == list.FirstOrDefault())
            {
                role = GetResource("拥有者");
            }
            else
            {
                role = GetResource("分享者");
            }

            return new
            {
                e.Id,
                e.Name,
                e.DisplayName,
                e.ProjectName,
                e.Enable,
                e.Online,
                e.Mail,
                e.Mobile,
                e.RegisterTime,
                e.IdentityId,
                Role = role,
            };
        });

        if (key.IsNotNullAndWhiteSpace())
        {
            listUser = listUser.Where(e => e.IdentityId.Contains(key)).ToList();
        }

        return Json(new { code = 0, msg = "success", count = listUser.Count(), data = listUser.Skip((page - 1) * limit).Take(limit) });
    }

    /// <summary>查看设备</summary>
    /// <returns></returns>
    [DisplayName("查看设备")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult Details(String Code)
    {
        dynamic viewModel = new ExpandoObject();

        //var model = Data.Device.FindById(Id);
        var model = Data.Device.FindByCode(Code);
        if (model == null)
        {
            return Content(GetResource("设备不存在"));
        }

        viewModel.Device = model;

        viewModel.ProductName = model.Product?.Name;  // 产品名称
        viewModel.DeviceName = model.Code;  // 设备DeviceName
        viewModel.DeviceSecret = model.Secret;  // 设备密钥DeviceSecret
        viewModel.ProductKey = model.Product?.Code;   // 产品ProductKey
        viewModel.ProductSecret = model.Product?.Secret;  // 产品密钥ProductSecret
        viewModel.ProjectKey = model.Product?.ProjectKey; // 项目ProjectKey

        var dateTime = DateTime.Now.Date;
        var data = MessageCount.FindByIotIdAndMTime(model.Code, dateTime);
        viewModel.Counts = 0;
        viewModel.AllCount = 0;
        if (data != null)
        {
            viewModel.Counts = data.Count;
            viewModel.AllCount = data.AllCount;
        }
        var map = Product.FindById(model.ProductId);
        viewModel.MesLimitMin = 0;
        viewModel.MesLimitMax = 0;
        if (map?.ProjectId > 0)
        {
            var project = Project.FindById(map.ProjectId);
            viewModel.MesLimitMin = project?.MesLimitMin;
            viewModel.MesLimitMax = project?.MesLimitMax;
        }
        IList<DeviceProperty> lists = DeviceProperty.FindAllByDeviceId(model.Id);
        viewModel.PropertyList = lists;
        return View(viewModel);
    }

    /// <summary>设备重置</summary>
    /// <returns></returns>
    [DisplayName("设备重置")]
    [EntityAuthorize((PermissionFlags)32)]
    [HttpPost]
    public IActionResult Reset(Int32 Id)
    {
        var result = new DResult();

        var model = Data.Device.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("设备不存在");
            return Json(result);
        }

        if (!model.Online)
        {
            result.msg = GetResource("设备不在线");
            return Json(result);
        }

        var _queue = EngineContext.Current.Resolve<QueueService>();  // 往队列发重置命令
        _queue.Publish(model.Code, new ServiceModel
        {
            Type = "Reset",
            Name = $"{model.Code} Reset",
            InputData = $"DeviceReset=1;ConfigClean=1",
            DeviceCode = model.Code,
        });

        result.success = true;
        result.msg = GetResource("重置指令已下发");

        return Json(result);
    }

    /// <summary>设备重启</summary>
    /// <returns></returns>
    [DisplayName("设备重启")]
    [EntityAuthorize((PermissionFlags)64)]
    [HttpPost]
    public IActionResult Reboot(Int32 Id)
    {
        var result = new DResult();

        var model = Data.Device.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("设备不存在");
            return Json(result);
        }

        if (!model.Online)
        {
            result.msg = GetResource("设备不在线");
            return Json(result);
        }

        var _queue = EngineContext.Current.Resolve<QueueService>();  // 往队列发重置命令
        _queue.Publish(model.Code, new ServiceModel
        {
            Type = "Reboot",
            Name = $"{model.Code} Reboot",
            InputData = $"DeviceReset=1;ConfigClean=0",
            DeviceCode = model.Code,
        });

        result.success = true;
        result.msg = GetResource("重启指令已下发");

        return Json(result);
    }

    /// <summary>调试设备</summary>
    /// <returns></returns>
    [DisplayName("调试设备")]
    [EntityAuthorize((PermissionFlags)128)]
    public IActionResult Debugging(Int32 Id)
    {
        var model = Data.Device.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("设备不存在"));
        }

        return View(model);
    }

    /// <summary>设备属性</summary>
    /// <param name="Id">设备Id</param>
    /// <param name="key">搜索关键词</param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [DisplayName("调试设备")]
    [EntityAuthorize((PermissionFlags)128)]
    public IActionResult ProductFunctionList(Int32 Id, String key, Int32 page = 1, Int32 limit = 10)
    {
        var result = new DResult();

        var model = Data.Device.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("设备不存在");
            return Json(result);
        }

        var UId = 0;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin && (modelRole.Roles.IsNullOrWhiteSpace() || !modelRole.Roles.Contains(",004,")))
        {
            UId = ManageProvider.User?.ID ?? -1;

            if (UId <= 0)
            {
                result.msg = GetResource("您没有操作权限");
                return Json(result);
            }

            var listU = Product.FindAllByCreateUserId(UId);
            if (!listU.Any(e => e.Id == model.ProductId))
            {
                result.msg = GetResource("您没有操作权限");
                return Json(result);
            }
        }

        if (!key.IsNullOrWhiteSpace()) key = key.Trim();
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = DeviceProperty._.Id,
            Desc = true
        };

        IList<DeviceProperty> list = DeviceProperty.Search(Id, key, pages);

        var data = list.Select(x => new { x.Id, x.DeviceName, x.Name, x.NickName, DataType = x.Type, x.Value, x.Unit, x.UnitName, x.Length, x.Address, x.Rule, x.Readonly, x.LastPost, x.FunctionName, x.DefaultValue });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>历史日志</summary>
    /// <param name="Id">设备Id</param>
    /// <param name="key">搜索关键词</param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [DisplayName("调试设备")]
    [EntityAuthorize((PermissionFlags)128)]
    public IActionResult HistoricalLogs(Int32 Id, String key, Int32 page = 1, Int32 limit = 10)
    {
        var result = new DResult();

        var model = Data.Device.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("设备不存在");
            return Json(result);
        }

        var UId = 0;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin && (modelRole.Roles.IsNullOrWhiteSpace() || !modelRole.Roles.Contains(",004,")))
        {
            UId = ManageProvider.User?.ID ?? -1;

            if (UId <= 0)
            {
                result.msg = GetResource("您没有操作权限");
                return Json(result);
            }

            var listU = Product.FindAllByCreateUserId(UId);
            if (!listU.Any(e => e.Id == model.ProductId))
            {
                result.msg = GetResource("您没有操作权限");
                return Json(result);
            }
        }

        if (!key.IsNullOrWhiteSpace()) key = key.Trim();
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = DeviceHistory._.Id,
            Desc = true
        };

        IList<DeviceHistory> list = DeviceHistory.Search(Id, key, pages);

        var data = list.Select(x => new { x.Id, x.Name, x.AreaName, x.Action, x.Success, x.Remark, x.Version, x.TraceId, x.Creator, x.CreateTime, x.CreateIP });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>设备属性设置</summary>
    /// <returns></returns>
    [DisplayName("调试设备")]
    [EntityAuthorize((PermissionFlags)128)]
    public IActionResult PropertiesSetting(Int32 Id)
    {
        var model = DeviceProperty.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("设备属性不存在"));
        }

        return View(model);
    }

    /// <summary>设备属性设置</summary>
    /// <returns></returns>
    [DisplayName("调试设备")]
    [EntityAuthorize((PermissionFlags)128)]
    [HttpPost]
    public async Task<IActionResult> PropertiesSetting(Int32 Id, String Value)
    {
        var result = new DResult();

        if (Id <= 0)
        {
            result.msg = GetResource("参数错误");
            return Json(result);
        }

        if (Value.IsNullOrEmpty())
        {
            result.msg = GetResource("值不能为空");
            return Json(result);
        }

        var msg = "";
        var entity = DeviceProperty.FindById(Id);
        if (entity != null && entity.Enable && !entity.Readonly && !entity.Locked)
        {
            if (!entity.DefaultValue.IsNullOrWhiteSpace())
            {
                entity.Value = Value;
                entity.SaveAsync();

                result.msg = GetResource("有默认值的属性不下发");
                return Json(result);
            }

            var _appService = EngineContext.Current.Resolve<AppService>();

            var rs = await _appService.SetProperty(entity.Device.Code, entity.Name, Value, entity.Type).ConfigureAwait(false);

            if (rs != null && rs.Status >= ServiceStatus.已完成)
            {
                msg = $"{rs.Status} {rs.Data}";
            }
            result.success = true;
            result.msg = GetResource("指令已下发" + msg);
            return Json(result);
        }
        else
        {
            result.msg = GetResource("下发失败");
            return Json(result);
        }
    }

    /// <summary>设备透传</summary>
    /// <returns></returns>
    [DisplayName("调试设备")]
    [EntityAuthorize((PermissionFlags)128)]
    public IActionResult Transmission(Int32 Id)
    {
        var model = DeviceProperty.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("属性不存在"));
        }

        return View(model);
    }

    /// <summary>设备透传</summary>
    /// <returns></returns>
    [DisplayName("调试设备")]
    [EntityAuthorize((PermissionFlags)128)]
    [HttpPost]
    public IActionResult Transmission(Int32 Id, String Value)
    {
        var result = new DResult();

        if (Id <= 0)
        {
            result.msg = GetResource("参数错误");
            return Json(result);
        }

        var entity = DeviceProperty.FindById(Id);

        if (Value.IsNullOrEmpty())
        {
            if (entity.Type == "bool")
            {
                Value = "False";
            }
            else
            {
                result.msg = GetResource("数据不能为空");
                return Json(result);
            }
        }
        else if (entity.Type == "bool")
        {
            Value = "True";
        }

        if (entity != null && entity.Enable && !entity.Readonly && !entity.Locked)
        {
            if (!entity.DefaultValue.IsNullOrWhiteSpace())
            {
                entity.Value = Value;
                entity.SaveAsync();
            }

            object content = entity.Type switch
            {
                "short" => Value.ToDGShort(),
                "int" => Value.ToDGInt(),
                "float" or "Single" => Value.ToDGFloat(),
                "bool" => Value.ToDGBool(),
                "byte" => Value.ToDGByte(),
                "long" or "UInt64" => Value.ToDGLong(),
                "double" or "Double" => Value.ToDGDouble(),
                "time" or "DateTime" => Value.ToDGDate(),
                _ => Value,
            };

            var _queue = EngineContext.Current.Resolve<QueueService>();  // 往队列发重置命令
            _queue.PublishTransmission(entity.Device.Code, new TransmissionModel
            {
                Id = Common.CommonFields.Snowflake.NewId(),
                Type = "Transmission",
                Name = $"{entity.Device.Code} Transmission",
                InputData = new TransmissionData { Name = entity.Name, Value = content, Address = entity.Address },
                DeviceCode = entity.Device.Code,
            });

            result.success = true;
            result.msg = GetResource("指令已下发");
            return Json(result);
        }
        else
        {
            result.msg = GetResource("下发失败");
            return Json(result);
        }
    }

    /// <summary>设备多属性透传</summary>
    /// <returns></returns>
    [DisplayName("调试设备")]
    [EntityAuthorize((PermissionFlags)128)]
    public IActionResult Transmissions(Int32 Id)
    {
        dynamic viewModel = new ExpandoObject();

        var model = Data.Device.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("调试设备不存在"));
        }

        viewModel.Device = model;

        IList<DeviceProperty> list = DeviceProperty.FindAllByDeviceId(Id);
        viewModel.DeviceProperty = list;

        return View(viewModel);
    }

    /// <summary>设备多属性透传</summary>
    /// <returns></returns>
    [DisplayName("调试设备")]
    [EntityAuthorize((PermissionFlags)128)]
    [HttpPost]
    public IActionResult Transmissions(Int32 Id, Boolean IsAll)
    {
        var result = new DResult();

        if (Id <= 0)
        {
            result.msg = GetResource("参数错误");
            return Json(result);
        }

        var model = Data.Device.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("调试设备不存在"));
        }

        var list = DeviceProperty.FindAllByDeviceId(Id);
        if (list == null || list.Count <= 0)
        {
            result.msg = GetResource("设备未包含有效属性");
            return Json(result);
        }

        var list1 = new List<TransmissionsData>();

        foreach (var item in list)
        {
            var IfCheckd = GetRequest($"[{item.Id}].IfCheckd").SafeString().Trim();  // 是否下发
            if (IfCheckd == "on")
            {
                var Value = GetRequest($"[{item.Id}].Value").SafeString().Trim();  // 下发值

                if (item.Type != "bool" && Value.IsNullOrWhiteSpace())
                {
                    result.msg = String.Format(GetResource($"{0}的值为空"), item.Name);
                    return Json(result);
                }

                object content = item.Type switch
                {
                    "short" => Value.ToDGShort(),
                    "int" => Value.ToDGInt(),
                    "float" or "Single" => Value.ToDGFloat(),
                    "bool" => Value.ToDGBool(),
                    "byte" => Value.ToDGByte(),
                    "long" or "UInt64" => Value.ToDGLong(),
                    "double" or "Double" => Value.ToDGDouble(),
                    "time" or "DateTime" => Value.ToDGDate(),
                    _ => Value,
                };

                list1.Add(new TransmissionsData
                {
                    Name = item.Name,
                    Value = content,
                    Address = item.Address
                });
            }
        }

        if (list1 != null && list1.Count > 0)
        {
            var _queue = EngineContext.Current.Resolve<QueueService>();  // 往队列发重置命令
            _queue.PublishTransmissions(model.Code, new TransmissionsModel
            {
                Id = Common.CommonFields.Snowflake.NewId(),
                Type = "Transmissions",
                Name = $"{model.Code} Transmissions",
                InputData = list1,
                DeviceCode = model.Code,
            });

            result.success = true;
            result.msg = GetResource("指令已下发");
            return Json(result);
        }
        else
        {
            result.msg = GetResource("下发失败");
            return Json(result);
        }
    }

    /// <summary>历史数据</summary>
    /// <returns></returns>
    [DisplayName("调试设备")]
    [EntityAuthorize((PermissionFlags)128)]
    public IActionResult GetHistoryDataList(Int32 deviceId, String names, DateTime start, DateTime end, String key, Int32 page = 1, Int32 limit = 10)
    {
        if (start <= DateTime.MinValue)
        {
            start = DateTime.Now.Date;
        }
        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now.Date;
        }

        if (!key.IsNullOrWhiteSpace()) key = key.Trim();
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = DeviceData._.Id,
            Desc = true
        };

        IList<DeviceData> list;

        var UId = 0;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin && (modelRole.Roles.IsNullOrWhiteSpace() || !modelRole.Roles.Contains(",004,")))
        {
            UId = ManageProvider.User?.ID ?? -1;

            var model = Data.Device.FindById(deviceId);
            if (model == null || model.Product?.CreateUserId != UId)
            {
                list = [];
            }
            else
            {
                list = DeviceData.Search(deviceId, names, start, end, key, pages);
            }
        }
        else
        {
            list = DeviceData.Search(deviceId, names, start, end, key, pages);
        }
        var data = list.Select(x => new { x.Id, x.Name, x.DeviceId, x.Kind, x.Value, Timestamp = UnixTime.ToDateTime(x.Timestamp, x.Timestamp.SafeString().Length == 13), x.CreateTime });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>运行状态</summary>
    /// <returns></returns>
    [DisplayName("运行状态")]
    [EntityAuthorize((PermissionFlags)256)]
    public IActionResult DeviceMap(Int32 deviceId)
    {
        var result = new DResult();

        var model = Data.Device.FindById(deviceId);
        if (model == null)
        {
            result.msg = GetResource("设备不存在");
            return Json(result);
        }

        var UId = 0;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin && (modelRole.Roles.IsNullOrWhiteSpace() || !modelRole.Roles.Contains(",004,")))
        {
            UId = ManageProvider.User?.ID ?? -1;

            if (UId <= 0)
            {
                result.msg = GetResource("您没有操作权限");
                return Json(result);
            }

            var listU = Product.FindAllByCreateUserId(UId);
            if (!listU.Any(e => e.Id == model.ProductId))
            {
                result.msg = GetResource("您没有操作权限");
                return Json(result);
            }
        }
        IList<DeviceProperty> list = DeviceProperty.FindAllByDeviceId(deviceId);
        var data = list.Select(x => new { x.Id, x.DeviceName, x.Name, x.NickName, DataType = x.Type, x.Value, x.Unit, x.UnitName, x.Length, x.Address, x.Rule, x.Readonly, x.LastPost, x.FunctionName, x.DefaultValue });
        return Json(new { code = 0, msg = "success", data });
    }

    /// <summary>
    /// 设备属性历史数据列表
    /// </summary>
    /// <returns></returns>
    [DisplayName("设备属性历史数据列表")]
    [EntityAuthorize((PermissionFlags)256)]
    public IActionResult DeviceLogList(Int32 Id)
    {
        var model = DeviceProperty.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("设备不存在"));
        }

        return View(model);
    }

    /// <summary>
    /// 解绑设备
    /// </summary>
    /// <returns></returns>
    [DisplayName("解绑设备")]
    [EntityAuthorize(PermissionFlags.Update)]
    public async Task<IActionResult> Untie(string IdentityId, string DeviceName)
    {
        var result = new DResult();

        var modelDeviceAssociatedUsers = DeviceAssociatedUsers.FindByDeviceName(DeviceName);
        if (modelDeviceAssociatedUsers == null)
        {
            result.errCode = 1007;
            result.msg = GetResource("设备未绑定用户");
            return Json(result);
        }

        var appUser = AppUser.FindByIdentityId(IdentityId);
        if (appUser == null)
        {
            result.errCode = 1012;
            result.msg = GetResource("用户不存在");
            return Json(result);
        }

        var modelProduct = Product.FindByCode(modelDeviceAssociatedUsers.ProductKey);
        if (modelProduct == null)
        {
            result.errCode = 1004;
            result.msg = GetResource("产品不存在");
            return Json(result);
        }

        if (modelProduct.BindType == 0 || modelProduct.BindType == 2)  // 抢占式/独占式
        {
            var modelDeviceBindLogs1 = new DeviceBindLogs
            {
                DeviceName = DeviceName,
                IdentityId = appUser.IdentityId,
                DType = 2,
                CreateUserID = ManageProvider.User?.ID ?? 0,
                CreateUser = ManageProvider.User?.DisplayName,
                Operating = $"抢占式/独占式后台{ManageProvider.User?.ID}解绑，待设备处理，{appUser.IdentityId}"
            };
            modelDeviceBindLogs1.SaveAsync();

            var modelDevice = Data.Device.FindByCode(DeviceName);
            if (modelDevice != null && !modelDevice.Online)  // 设备不在线时直接解绑
            {
                modelDeviceAssociatedUsers.Delete();

                var hashDevices = appUser.Devices?.Split(',').ToHashSet().ToList();
                hashDevices ??= [];
                var listProductKeys = appUser.ProductKeys?.Split(',').ToList();
                listProductKeys ??= [];
                var index = hashDevices.IndexOf(DeviceName);
                if (index != -1)
                {
                    hashDevices.RemoveAt(index);
                    listProductKeys.RemoveAt(index);

                    appUser.Devices = hashDevices.Join();
                    appUser.ProductKeys = listProductKeys.Join();
                    appUser.Update();
                }

                result.success = true;
                result.msg = GetResource("解绑成功");
                return Json(result);
            }

            // 解绑通知模块
            var _queue = EngineContext.Current.Resolve<QueueService>();  // 往队列发重置命令
            var logId = new Snowflake().NewId();
            _queue.Publish(DeviceName, new ServiceModel
            {
                Id = logId,
                Type = "Reset",
                Name = $"{DeviceName} Reset",
                InputData = $"DeviceReset=1;ConfigClean=1",
                DeviceCode = DeviceName,
            });

            var reply = new ServiceReplyModel();

            // 挂起等待。借助redis队列，等待响应
            var q = _queue.GetReplyQueue(logId);
            try
            {
                var mqMsg = await q.TakeOneAsync(3000 / 1000).ConfigureAwait(false);
                if (!mqMsg.IsNullOrEmpty())
                {
                    // 埋点
                    using var span = _tracer?.NewSpan($"redismq:ServiceLog", mqMsg);

                    // 解码
                    var dic = JsonParser.Decode(mqMsg);
                    span?.Detach(dic!);
                    reply = JsonHelper.Convert<ServiceReplyModel>(dic!);
                }
            }
            catch (TimeoutException ex)
            {
                XTrace.WriteException(ex);
            }

            if (reply != null)
            {
                if (reply.Id == 0)
                {
                    if (reply?.Status != ServiceStatus.已完成)
                    {
                        result.errCode = 1009;
                        result.msg = GetResource("处理失败");
                        return Json(result);
                    }
                }
                else if (reply.Id != logId)
                {
                    result.errCode = 1010;
                    result.msg = GetResource("处理失败");
                    return Json(result);
                }
                else if (reply.Id == logId)
                {
                    if (reply?.Status != ServiceStatus.已完成)
                    {
                        result.errCode = 1011;
                        result.msg = GetResource("处理失败");
                        return Json(result);
                    }
                }

                // 解绑
                modelDeviceAssociatedUsers = DeviceAssociatedUsers.FindByDeviceName(DeviceName);
                if (modelDeviceAssociatedUsers != null)
                {
                    var modelDeviceBindLogs = new DeviceBindLogs
                    {
                        DeviceName = DeviceName,
                        Operating = $"抢占式/独占式设备重置解绑后台处理：OnReset：{appUser.IdentityId}",
                        IdentityId = appUser.IdentityId,
                        DType = 2,
                        CreateUserID = ManageProvider.User?.ID ?? 0,
                        CreateUser = ManageProvider.User?.DisplayName,
                    };
                    modelDeviceBindLogs.SaveAsync();

                    var list = new List<String> { modelDeviceAssociatedUsers.IdentityId };

                    if (!modelDeviceAssociatedUsers.SubUsers.IsNullOrWhiteSpace())
                    {
                        var SubUsers = modelDeviceAssociatedUsers.SubUsers.Trim(',').Split(',');
                        SubUsers.ForEach(list.Add);
                    }

                    var searchList = AppUser.FindAllByIdentityId(list);  // 获取所有与设备有关的用户
                    foreach (var item in searchList)
                    {
                        var hashDevices = item.Devices?.Split(',').ToHashSet().ToList();
                        hashDevices ??= [];
                        var listProductKeys = item.ProductKeys?.Split(',').ToList();
                        listProductKeys ??= [];

                        var index = hashDevices.IndexOf(DeviceName);
                        if (index != -1)
                        {
                            hashDevices.RemoveAt(index);
                            listProductKeys.RemoveAt(index);

                            item.Devices = hashDevices.Join();
                            item.ProductKeys = listProductKeys.Join();
                            item.Update();
                        }
                    }

                    modelDeviceAssociatedUsers.Delete();
                }

                // 成功
                _cache.Cache.Set($"{RedisSetting.Current.CacheKeyPrefix}BindStatus:{appUser.IdentityId}", 2, 10);
            }
        }
        else if (modelProduct.BindType == 1)  // 分享式
        {
            if (appUser.IdentityId == modelDeviceAssociatedUsers.IdentityId)  // 主管理员
            {
                var modelDeviceBindLogs1 = new DeviceBindLogs
                {
                    DeviceName = DeviceName,
                    IdentityId = appUser.IdentityId,
                    DType = 2,
                    CreateUserID = ManageProvider.User?.ID ?? 0,
                    CreateUser = ManageProvider.User?.DisplayName,
                    Operating = $"分享式后台解绑，{appUser.IdentityId}"
                };
                modelDeviceBindLogs1.SaveAsync();

                var modelDevice = Data.Device.FindByCode(DeviceName);
                if (modelDevice != null && !modelDevice.Online)  // 设备不在线时直接解绑
                {
                    var list = modelDeviceAssociatedUsers.SubUsers?.Split(',').ToList();  // 子用户
                    list ??= [];

                    modelDeviceAssociatedUsers.Delete();

                    list.Add(appUser.IdentityId);
                    foreach (var item in list)
                    {
                        appUser = AppUser.FindByIdentityId(item);
                        if (appUser != null)
                        {
                            var hashDevices = appUser.Devices?.Split(',').ToHashSet().ToList();
                            hashDevices ??= [];
                            var listProductKeys = appUser.ProductKeys?.Split(',').ToList();
                            listProductKeys ??= [];
                            var index = hashDevices.IndexOf(DeviceName);
                            if (index != -1)
                            {
                                hashDevices.RemoveAt(index);
                                listProductKeys.RemoveAt(index);

                                appUser.Devices = hashDevices.Join();
                                appUser.ProductKeys = listProductKeys.Join();
                                appUser.Update();
                            }
                        }
                    }

                    result.success = true;
                    result.msg = GetResource("解绑成功");
                    return Json(result);
                }

                // 解绑通知模块
                var _queue = EngineContext.Current.Resolve<QueueService>();  // 往队列发重置命令
                var logId = new Snowflake().NewId();
                _queue.Publish(DeviceName, new ServiceModel
                {
                    Id = logId,
                    Type = "Reset",
                    Name = $"{DeviceName} Reset",
                    InputData = $"DeviceReset=1;ConfigClean=1",
                    DeviceCode = DeviceName,
                });

                var reply = new ServiceReplyModel();

                // 挂起等待。借助redis队列，等待响应
                var q = _queue.GetReplyQueue(logId);
                try
                {
                    var mqMsg = await q.TakeOneAsync(3000 / 1000).ConfigureAwait(false);
                    if (!mqMsg.IsNullOrEmpty())
                    {
                        // 埋点
                        using var span = _tracer?.NewSpan($"redismq:ServiceLog", mqMsg);

                        // 解码
                        var dic = JsonParser.Decode(mqMsg);
                        span?.Detach(dic!);
                        reply = JsonHelper.Convert<ServiceReplyModel>(dic!);
                    }
                }
                catch (TimeoutException ex)
                {
                    XTrace.WriteException(ex);
                }

                if (reply != null)
                {
                    if (reply.Id == 0)
                    {
                        if (reply?.Status != ServiceStatus.已完成)
                        {
                            result.errCode = 1009;
                            result.msg = GetResource("处理失败");
                            return Json(result);
                        }
                    }
                    else if (reply.Id != logId)
                    {
                        result.errCode = 1010;
                        result.msg = GetResource("处理失败");
                        return Json(result);
                    }
                    else if (reply.Id == logId)
                    {
                        if (reply?.Status != ServiceStatus.已完成)
                        {
                            result.errCode = 1011;
                            result.msg = GetResource("处理失败");
                            return Json(result);
                        }
                    }

                    // 解绑
                    modelDeviceAssociatedUsers = DeviceAssociatedUsers.FindByDeviceName(DeviceName);
                    if (modelDeviceAssociatedUsers != null)
                    {
                        var modelDeviceBindLogs = new DeviceBindLogs
                        {
                            DeviceName = DeviceName,
                            Operating = $"分享式设备重置解绑后台处理：OnReset：{appUser.IdentityId}",
                            IdentityId = appUser.IdentityId,
                            DType = 2,
                            CreateUserID = ManageProvider.User?.ID ?? 0,
                            CreateUser = ManageProvider.User?.DisplayName,
                        };
                        modelDeviceBindLogs.SaveAsync();

                        var list = new List<String> { modelDeviceAssociatedUsers.IdentityId };

                        if (!modelDeviceAssociatedUsers.SubUsers.IsNullOrWhiteSpace())
                        {
                            var SubUsers = modelDeviceAssociatedUsers.SubUsers.Trim(',').Split(',');
                            SubUsers.ForEach(list.Add);
                        }

                        var searchList = AppUser.FindAllByIdentityId(list);  // 获取所有与设备有关的用户
                        foreach (var item in searchList)
                        {
                            var hashDevices = item.Devices?.Split(',').ToHashSet().ToList();
                            hashDevices ??= [];
                            var listProductKeys = item.ProductKeys?.Split(',').ToList();
                            listProductKeys ??= [];

                            var index = hashDevices.IndexOf(DeviceName);
                            if (index != -1)
                            {
                                hashDevices.RemoveAt(index);
                                listProductKeys.RemoveAt(index);

                                item.Devices = hashDevices.Join();
                                item.ProductKeys = listProductKeys.Join();
                                item.Update();
                            }
                        }

                        modelDeviceAssociatedUsers.Delete();
                    }

                    // 成功
                    _cache.Cache.Set($"{RedisSetting.Current.CacheKeyPrefix}BindStatus:{appUser.IdentityId}", 2, 10);

                }
            }
            else
            {
                var listSubUsers = modelDeviceAssociatedUsers.SubUsers?.Split(',').ToList();
                listSubUsers ??= [];

                if (listSubUsers.Contains(appUser.IdentityId))
                {
                    var modelDeviceBindLogs1 = new DeviceBindLogs
                    {
                        DeviceName = DeviceName,
                        Operating = $"分享式后台解绑，{appUser.IdentityId}",
                        IdentityId = appUser.IdentityId,
                        DType = 2,
                        CreateUserID = ManageProvider.User?.ID ?? 0,
                        CreateUser = ManageProvider.User?.DisplayName,
                    };
                    modelDeviceBindLogs1.SaveAsync();

                    listSubUsers.Remove(appUser.IdentityId);
                    modelDeviceAssociatedUsers.SubUsers = listSubUsers.Join();
                    modelDeviceAssociatedUsers.Update();

                    var hashDevices = appUser.Devices?.Split(',').ToHashSet().ToList();
                    hashDevices ??= [];
                    var listProductKeys = appUser.ProductKeys?.Split(',').ToList();
                    listProductKeys ??= [];
                    var index = hashDevices.IndexOf(DeviceName);
                    if (index != -1)
                    {
                        hashDevices.RemoveAt(index);
                        listProductKeys.RemoveAt(index);

                        appUser.Devices = hashDevices.Join();
                        appUser.ProductKeys = listProductKeys.Join();
                        appUser.Update();
                    }
                }
            }
        }

        result.success = true;
        result.msg = GetResource("解绑成功");
        return Json(result);
    }

    /// <summary>
    /// 设备绑定日志
    /// </summary>
    /// <param name="deviceName"></param>
    /// <param name="start"></param>
    /// <param name="end"></param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [DisplayName("设备绑定日志")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult GetBindLogsList(String deviceName, DateTime start, DateTime end, Int32 page, Int32 limit)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "CreateTime",
            Desc = true
        };
        var list = DeviceBindLogs.Search(deviceName, start, end, "", pages);
        return Json(new { code = 0, msg = "", count = pages.TotalCount, data = list });
    }

    /// <summary>
    /// 续期
    /// </summary>
    /// <returns></returns>
    [DisplayName("续期")]
    [EntityAuthorize((PermissionFlags)512)]
    public IActionResult Renewal(Int32 Id)
    {
        var modal = Data.Device.FindById(Id);
        if(modal == null)
        {
            return Content(GetResource("设备不存在"));
        }
        if (modal.Product == null)
        {
            return Content(GetResource("产品不存在"));
        }
        if (!modal.Product.EnableLimitTime)
        {
            return Content(GetResource("续期未开放"));
        }
        return View(modal);
    }

    /// <summary>
    /// 续期
    /// </summary>
    /// <returns></returns>
    [DisplayName("续期")]
    [EntityAuthorize((PermissionFlags)512)]
    [HttpPost]
    public IActionResult Renewal(String DeviceName,Int32 Year,String Remark)
    {
        var res = new DResult();

        if(Year <= 0)
        {
            res.msg = GetResource("续期年份不能为空");
            return Json(res);
        }
        if (Remark.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("续期说明不能为空");
            return Json(res);
        }
        var device = Data.Device.FindByCode(DeviceName);
        if (device == null)
        {
            res.msg = GetResource("续期设备不存在");
            return Json(res);
        }

        DateTime dt = new();

        if(device.ExpiredTime == DateTime.MinValue)
        {
            dt = DateTime.Now.AddYears(Year);
        }
        else
        {
            dt = device.ExpiredTime.AddYears(Year);
        }
        
        DeviceRenewalLogs logs = new()
        {
            DeviceName = device.Code,
            LastExpiredTime = device.ExpiredTime,
            ExpiredTime = dt,
            DType = 1,
            Remark = Remark,
        };

        logs.Insert();

        device.ExpiredTime = dt;
        device.Update();

        res.success = true;
        return Json(res);
    }

    /// <summary>
    /// 续期日志列表
    /// </summary>
    /// <param name="DeviceName"></param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [DisplayName("续期")]
    [EntityAuthorize((PermissionFlags)512)]
    public IActionResult Renewallist(String DeviceName,Int32 page,Int32 limit)
    {
        var list = DeviceRenewalLogs.FindAllByDeviceName(DeviceName).OrderByDescending(e => e.Id).ToList();
        return Json(new { code = 0, msg = "", count = list.Count, data = list.Skip((page - 1) * limit).Take(limit) });
    }
}
