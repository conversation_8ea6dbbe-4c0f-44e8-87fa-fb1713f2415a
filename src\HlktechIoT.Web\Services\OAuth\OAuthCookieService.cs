using Microsoft.AspNetCore.DataProtection;

using NewLife;
using NewLife.Log;
using NewLife.Serialization;

namespace HlktechIoT.Services.OAuth;

/// <summary>
/// OAuth专用Cookie服务
/// 独立的Cookie认证系统，避免与现有NewLife框架用户表冲突
/// </summary>
public interface IOAuthCookieService
{
    /// <summary>
    /// 设置OAuth认证Cookie
    /// </summary>
    void SetOAuthUser(HttpContext context, OAuthUserInfo userInfo, int expireMinutes = 30);

    /// <summary>
    /// 获取OAuth认证用户信息
    /// </summary>
    OAuthUserInfo? GetOAuthUser(HttpContext context);

    /// <summary>
    /// 清除OAuth认证Cookie
    /// </summary>
    void ClearOAuthUser(HttpContext context);

    /// <summary>
    /// 验证OAuth用户是否已认证
    /// </summary>
    bool IsOAuthUserAuthenticated(HttpContext context);
}

/// <summary>
/// OAuth用户信息
/// </summary>
public class OAuthUserInfo
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Int32 UserId { get; set; } = 0;

    /// <summary>
    /// 用户名
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// 认证时间
    /// </summary>
    public DateTime AuthTime { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime ExpireTime { get; set; }
}

/// <summary>
/// OAuth Cookie服务实现
/// </summary>
public class OAuthCookieService : IOAuthCookieService
{
    private const string OAUTH_COOKIE_NAME = ".OAuth.Auth";
    
    private readonly IDataProtector _dataProtector;

    public OAuthCookieService(IDataProtectionProvider dataProtectionProvider)
    {
        _dataProtector = dataProtectionProvider.CreateProtector("OAuthCookieService");
    }

    /// <summary>
    /// 设置OAuth认证Cookie
    /// </summary>
    public void SetOAuthUser(HttpContext context, OAuthUserInfo userInfo, int expireMinutes = 30)
    {
        try
        {
            userInfo.AuthTime = DateTime.UtcNow;
            userInfo.ExpireTime = DateTime.UtcNow.AddMinutes(expireMinutes);

            var json = userInfo.ToJson();
            var protectedData = _dataProtector.Protect(json);

            var cookieOptions = new CookieOptions
            {
                HttpOnly = true,
                Secure = context.Request.IsHttps,
                SameSite = SameSiteMode.Lax,
                Expires = userInfo.ExpireTime,
                Path = "/",
                IsEssential = true
            };

            context.Response.Cookies.Append(OAUTH_COOKIE_NAME, protectedData, cookieOptions);

            XTrace.WriteLine($"OAuth用户 {userInfo.UserId} 认证Cookie已设置，过期时间: {userInfo.ExpireTime}");
        }
        catch (Exception ex)
        {
            XTrace.WriteLine("设置OAuth认证Cookie失败");
            XTrace.WriteException(ex);
            throw;
        }
    }

    /// <summary>
    /// 获取OAuth认证用户信息
    /// </summary>
    public OAuthUserInfo? GetOAuthUser(HttpContext context)
    {
        try
        {
            var cookieValue = context.Request.Cookies[OAUTH_COOKIE_NAME];
            if (cookieValue.IsNullOrWhiteSpace())
            {
                return null;
            }

            var json = _dataProtector.Unprotect(cookieValue);
            var userInfo = json.ToJsonEntity<OAuthUserInfo>();

            if (userInfo == null)
            {
                XTrace.Log.Warn("OAuth Cookie数据反序列化失败");
                return null;
            }

            // 检查是否过期
            if (userInfo.ExpireTime <= DateTime.UtcNow)
            {
                XTrace.WriteLine($"OAuth用户 {userInfo.UserId} 认证已过期");
                ClearOAuthUser(context);
                return null;
            }

            return userInfo;
        }
        catch (Exception ex)
        {
            XTrace.WriteLine("获取OAuth认证用户信息失败");
            XTrace.WriteException(ex);
            ClearOAuthUser(context);
            return null;
        }
    }

    /// <summary>
    /// 清除OAuth认证Cookie
    /// </summary>
    public void ClearOAuthUser(HttpContext context)
    {
        try
        {
            context.Response.Cookies.Delete(OAUTH_COOKIE_NAME, new CookieOptions
            {
                Path = "/",
                HttpOnly = true,
                Secure = context.Request.IsHttps,
                SameSite = SameSiteMode.Lax
            });

            XTrace.WriteLine("OAuth认证Cookie已清除");
        }
        catch (Exception ex)
        {
            XTrace.WriteLine("清除OAuth认证Cookie失败");
            XTrace.WriteException(ex);
        }
    }

    /// <summary>
    /// 验证OAuth用户是否已认证
    /// </summary>
    public Boolean IsOAuthUserAuthenticated(HttpContext context)
    {
        var userInfo = GetOAuthUser(context);
        return userInfo != null;
    }
}
