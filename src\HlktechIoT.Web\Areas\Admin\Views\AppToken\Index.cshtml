﻿@{
    Html.AppendTitleParts(T("应用令牌").Text);
}

<!-- 搜索表单 -->
<form class="layui-form dg-form">
    <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
        <div class="layui-inline">
            <label class="layui-form-label">@T("关键词")：</label>
            <div class="layui-input-inline">
                <input type="text" name="key" id="key" autocomplete="off" class="layui-input">
            </div>
        </div>
    </div>
</form>

<!-- 数据表格 -->
<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    layui.use(['table', 'abp', 'dg', 'form'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;

        table.render({
            elem: '#tablist',
            url: '@Url.Action("List")',
            page: true, //开启分页
            toolbar: '#user-toolbar',
            defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print'],
            loading: true,
            cols: [[
                { field: 'Id', title: '@T("编号")', width: 80 },
                { field: 'Name', title: '@T("名称")', width: 450 },
                { field: 'DisplayName', title: '@T("显示名")', width: 450 },
                { field: 'TokenExpire', title: '@T("有效期")', width: 100 },
                { field: 'Enable', title: '@T("启用")', width: 80 },
                { field: 'UpdateTime', title: '@T("更新时间")', width: 160 },
                { fixed: 'right', title: '@T("操作")', toolbar: '#tool', width: 210 }
            ]],
            height: 'full-100',
            limit: 13,
            limits: [10, 13, 20, 30, 50, 100],
            id: 'tables'
        });

        window.active = {
            reload: function () {
                table.reload('tables', {
                    where: {
                        key: $("#key").val()
                    },
                    page: { curr: 1 }
                });
            }
        };

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === "edit") {
                top.layui.dg.popupRight({
                    id: 'AppTokenDetail',
                    title: '@T("编辑应用令牌")',
                    closeBtn: 1,
                    area: ['750px'],
                    success: function () {
                        $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Edit")?id=' + data.Id + '" frameborder="0" class="layadmin-iframe"></iframe>');
                    }
                });
            } else if (obj.event === "del") {
                parent.layer.confirm('@T("确认删除吗")?', { icon: 3, btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("Delete")', { id: data.Id }, function (data) {
                        if (data.success) {
                            abp.notify.success('@T("删除成功")');
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            } else if (obj.event === "token") {
                top.layui.dg.popupRight({
                    id: 'AppTokenManage',
                    title: '@T("令牌管理")',
                    closeBtn: 1,
                    area: ['750px'],
                    success: function () {
                        $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("TokenManage")?id=' + data.Id + '" frameborder="0" class="layadmin-iframe"></iframe>');
                    }
                });
            }
        });

        $("#key").on("input", function () {
            active.reload();
        });

        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'add') {
                top.layui.dg.popupRight({
                    id: 'AppTokenDetail',
                    title: '@T("新增应用令牌")',
                    closeBtn: 1,
                    area: ['750px'],
                    success: function () {
                        $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Add")" frameborder="0" class="layadmin-iframe"></iframe>');
                    }
                });
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });
    });
</script>

<script type="text/html" id="tool">
    <a class="pear-btn pear-btn-warming pear-btn-xs" lay-event="token">@T("令牌管理")</a>
    <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="edit">@T("编辑")</a>
    <a class="pear-btn pear-btn-danger pear-btn-xs" lay-event="del">@T("删除")</a>
</script>

<script type="text/html" id="user-toolbar">
    <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        @T("新增")
    </button>
</script>
