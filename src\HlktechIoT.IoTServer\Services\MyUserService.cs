﻿using HlktechIoT.Core.Models;
using HlktechIoT.Data;

using NewLife;
using NewLife.Remoting;
using NewLife.Security;
using NewLife.Serialization;

using Pek;

namespace HlktechIoT.IoTServer.Services;

/// <summary>用户服务</summary>
public class MyUserService
{
    /// <summary>密码散列提供者。避免密码明文提交</summary>
    public IPasswordProvider PasswordProvider { get; set; } = new SaltPasswordProvider { Algorithm = "sha512", SaltTime = 0 };

    #region 登录

    /// <summary>
    /// 设备登录验证，内部支持动态注册
    /// </summary>
    /// <param name="inf">登录信息</param>
    /// <param name="source">登录来源</param>
    /// <param name="ip">远程IP</param>
    /// <param name="SType">0为默认密码登录，1为OpenId登录,2为阿里云密钥登录</param>
    /// <returns></returns>
    /// <exception cref="ApiException"></exception>
    public UserLoginResponse Login(UserLoginInfo inf, String source, String ip, Int32 SType)
    {
        var secret = inf.PassWord;

        var model = AppUser.FindByIdentityId(inf.IdentityId);
        if (model == null)
        {
            throw new ApiException(98, $"找不到用户，无法登录:{inf.ToJson()}");
        }
        else
        {
            if (!model.Enable) throw new ApiException(99, "禁止登录");

            if (model.Name != inf.Name) throw new ApiException(98, "登录失败");

            if (SType == 0)
            {
                if (!model.Password.IsNullOrWhiteSpace() && (secret.IsNullOrEmpty() || !PasswordProvider.Verify(secret, model.Password)))
                {
                    throw new ApiException(98, "登录失败");
                }
            }
            else
            {
                var pwd = $"{model.OpenId}".MD5();
                pwd = (pwd.Left(16) + model.OpenId + pwd.Right(16)).MD5();

                if (secret != pwd) throw new ApiException(98, "登录失败");
            }
        }

        model.LastActive = DateTime.Now;
        model.Online = true;
        model.Update();

        var rs = new UserLoginResponse
        {
            ClientId = inf.ClientId,
            IdentityId = model.IdentityId,
        };

        return rs;
    }

    #endregion
}
