﻿@using NewLife.Log
@using NewLife.Serialization
@model Role
@{
    var menus = Menu.Root.AllChilds;
    var role = Model as Role;

    var roleEx = RoleEx.FindById(role?.ID);

    var localizationSettings = LocalizationSettings.Current;
}
<style asp-location="true">
    html {
        background-color: #f2f2f2;
        background-color: transparent !important;
        color: #666;
    }

    body {
        height: 100%;
    }

    .pear-container {
        background-color: white;
    }

    .container .table > tbody > tr > td {
        padding: 5px 5px;
    }

    .containers {
        width: 100%;
        /* padding-top: 20px;*/
        height: 100%;
        position: relative
    }

    .bala {
        /*  float: right;
                                    padding-right: 66px;*/
        padding-bottom: 20px;
        padding-top: 10px;
        text-align: center;
    }

    .container.form-horizontal {
        width: 100%;
    }

    input[type=checkbox], input[type=radio] {
        vertical-align: sub !important;
    }

    .containers .form-horizontal .form-group {
        margin-right: 15px;
        margin-left: 15px;
    }

    input[type=checkbox], input[type=radio] {
        margin-left: 10px !important;
        height: 17px;
    }

    .form-group .tables {
        width: 80%
    }

    .input-group.col-sm-9.tables {
        padding-right: 20px;
    }

    .layui-tab.layui-tab-card {
        width: 85%;
        margin: 0 auto;
        margin-bottom: 10px
    }

    .layui-tab.layui-tab-brief {
        width: 85%;
        margin: 0 auto;
        margin-bottom: 10px
    }

    .layui-form-item {
        margin-top: 20px;
    }

    .layui-input-block {
        margin-left: 80px;
    }

    table#tables {
        margin: 0 auto;
        width: 703px;
        border-width: 1px;
        border-style: solid;
        border-radius: 2px;
        box-shadow: 0 2px 5px 0 rgba(0,0,0,.1);
        border-color: #e6e6e6;
    }

        table#tables td, th {
            border-width: 1px;
            border-style: solid;
            border-radius: 2px;
            border-color: #e6e6e6;
            line-height: 35px;
            padding-left: 10px;
        }

            table#tables td:nth-child(3), table#tables th:nth-child(3) {
                padding-right: 10px;
            }

    .layui-textarea {
        resize: none;
        /*width: 90%*/
    }

    .layui-tab.layui-tab-brief {
        margin: 0 auto;
        width: 703px;
    }

    .layui-form-label {
        text-align: left;
        padding: 0;
        padding-right: 20px;
        font-size: 13px;
        height: 36px;
        line-height: 36px;
    }

    .layui-input-block {
        margin-left: 0;
    }

    .layui-textarea {
        min-height: 80px;
    }

    .layui-tab-content {
        padding: 0;
    }

    .layui-input, .layui-textarea {
        border-radius: 5px;
        white-space: nowrap;
    }

    .layui-tab-brief > .layui-tab-title .layui-this {
        color: black;
    }

        .layui-tab-brief > .layui-tab-title .layui-this:after {
            border-bottom: 2px solid #1E88E5;
        }
</style>
<div class="containers">
    <form class="form-horizontal" action="javascript:void(0);">
        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
            <input name="Id" type="text" value="@Model!.ID" hidden>
            @if (localizationSettings.IsEnable)
            {
                <ul class="layui-tab-title">
                    <li class="layui-this">@T("标准"):</li>
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        <li>@item.DisplayName</li>
                    }
                </ul>
            }
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <div class="layui-form-item">
                        <label class="layui-form-label">@T("角色名"):</label>
                        <div class="layui-input-block">
                            <input type="text" name="Name" lay-verify="title" value="@Model.Name" autocomplete="off" placeholder="@T("请输入角色名")" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">@T("备注"):</label><br />
                        <div class="layui-input-block">
                            <textarea placeholder="@T("请输入内容")" class="layui-textarea" name="Remark">@Model.Remark</textarea>
                        </div>
                    </div>
                </div>

                @if (localizationSettings.IsEnable)
                {
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {

                        var modelRoleLan = DH.Entity.RoleLan.FindByRIdAndLId(Model.ID, item.Id, false);

                        <div class="layui-tab-item">
                            <div class="layui-form-item">
                                <label class="layui-form-label">@T("角色名"):</label>
                                <div class="layui-input-block">
                                    <input type="text" name="[@item.Id].Name" lay-verify="title" value="@modelRoleLan.Name" autocomplete="off" placeholder="@T("请输入角色名")" class="layui-input">
                                </div>
                            </div>

                            <div class="layui-form-item layui-form-text">
                                <label class="layui-form-label">@T("备注"):</label><br />
                                <div class="layui-input-block">
                                    <textarea placeholder="@T("请输入内容")" class="layui-textarea" name="[@item.Id].Remark">@modelRoleLan.Remark</textarea>
                                </div>
                            </div>
                        </div>
                    }
                }
            </div>
        </div>

        <div class="layui-tab layui-tab-brief layui-form">
            <div class="layui-tab-content">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width:93px">@T("是否系统角色")</label>
                        <div class="layui-input-inline">
                            <input type="checkbox" name="IsSystem" lay-skin="switch" lay-filter="IsSystem" @(Model.IsSystem ? "checked" : "") lay-text="@T("是")|@T("否")">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 100px;">@T("是否系统管理员")</label>
                        <div class="layui-input-inline">
                            <input type="checkbox" name="IsAdmin" lay-skin="switch" lay-filter="IsAdmin" @(roleEx?.IsAdmin == true ? "checked" : "") lay-text="@T("是")|@T("否")">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if (Model.ID > 1)
        {
            <div class="">
                <table id="tables">
                    <thead>
                        <tr>
                            <th style="border: 1px solid #ddd;">@T("名称")</th>
                            <th style="border: 1px solid #ddd;">@T("显示名")</th>
                            <th style="border: 1px solid #ddd;">@T("授权")</th>
                            <th style="border: 1px solid #ddd;">@T("操作权限")</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var entity in menus)
                        {
                            @if (entity.FullName?.Contains("NewLife.", StringComparison.OrdinalIgnoreCase) == false && entity.FullName?.Contains("IoTWeb.", StringComparison.OrdinalIgnoreCase) == false)
                            {
                                <tr>
                                    <td>@entity.TreeNodeName</td>
                                    <td>@T(entity.DisplayName)</td>
                                    <td>@Html.CheckBox("p" + entity.ID, role!.Has(entity.ID), new { @class = "authorize", @child = "auth_child" + entity.ID, @parentkey = "auth_child" + entity.ParentID, @childchange = 0 })</td>
                                    <td class="childpf">
                                        @foreach (var item in entity.Permissions.OrderBy(e => e.Key))
                                        {
                                            var id = "pf" + entity.ID + "_" + ((Int32)item.Key);
                                            @Html.CheckBox(id, role.Has(entity.ID, (PermissionFlags)item.Key), new { @parentkey = "auth_child" + entity.ID, @proparentkey = "pro_" + item.Key + "_" + entity.ParentID, @prokey = "pro_" + (UInt32)PermissionFlags.All + "_" + entity.ParentID, @dataid = entity.ID })
                                            @*@Html.Label(id, item.Value)*@
                                            <label for="@id">@T(item.Value)</label>
                                            <text>&nbsp;</text>
                                        }
                                    </td>
                                </tr>
                            }
                        }
                    </tbody>
                </table>
            </div>
        }
        <div class="bala">
            <div class="col-sm-offset-2 col-sm-10">
                <button type="button" lay-submit lay-filter="SubmitRole" id="SubmitRole" class="pear-btn pear-btn-primary pear-btn-normal">@T("保存")</button>
            </div>
        </div>
    </form>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'dg', 'form'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var dg = layui.dg;

        $('input.authorize').on('change', function () {
            var $this = $(this);

            if ($this.attr("childchange") == 0) {
                var status = $this.prop('checked');
                var childkey = $this.attr('child');
                // 规避change 需要在失去焦点时触发的问题，设置值完成后手工再次触发该事件
                $('input[parentkey="' + childkey + '"]').prop('checked', status).change();

                var parentkey = $this.attr('parentkey');

                var obj = $("input[child$='" + parentkey + "']");
                var obj1 = $("input[parentkey$='" + parentkey + "']");;
                var bol = false;
                for (i = 0; i < obj1.length; i++) {
                    if ($('#' + obj1[i].id).prop("checked")) {
                        bol = true;
                        continue;
                    }
                }
                if (!bol) {
                    $("#" + obj.attr("id")).prop("checked", "");
                }
            }
            else if ($this.attr("childchange") == 1) {
                var parentkey = $this.attr('parentkey');
                var obj = $("input[child$='" + parentkey + "']");
                var parentkey1 = $("#" + obj.attr("id")).attr('parentkey');
                var obj1 = $("input[child$='" + parentkey1 + "']");

                $("#" + obj.attr("id")).prop('checked', true);
                $("#" + obj1.attr("id")).prop('checked', true);

                $this.attr("childchange", 0);
            }
            else if ($this.attr("childchange") == 2) {
                var parentkey = $this.attr('parentkey');
                var obj = $("input[child$='" + parentkey + "']");

                var parentkey1 = $("#" + obj.attr("id")).attr('parentkey');
                var obj1 = $("input[child$='" + parentkey1 + "']");

                var obj1 = $("input[parentkey$='" + parentkey + "']");;
                var bol = false;
                for (i = 0; i < obj1.length; i++) {
                    if ($('#' + obj1[i].id).prop("checked")) {
                        bol = true;
                        continue;
                    }
                }
                if (!bol) {
                    $("#" + obj.attr("id")).attr("childchange", 3);
                    $("#" + obj.attr("id")).prop("checked", "").change();
                }

                $this.attr("childchange", 0);
            }
            else if ($this.attr("childchange") == 3) {
                var parentkey = $this.attr('parentkey');
                var obj = $("input[child$='" + parentkey + "']");

                $("#" + obj.attr("id")).prop('checked', "");

                $this.attr("childchange", 0);
            }
        });

        // 只读/查看
        $('input.pro_detail').on('change', function () {
            $this = $(this);
            var status = $this.prop('checked');
            var key = $this.attr('prochildkey');
            $('input[proparentkey=' + key + ']').prop('checked', status);
        });

        // 全部权限
        $('input.pro_all').on('change', function () {
            $this = $(this);
            var status = $this.prop('checked');
            var key = $this.attr('prochildkey');
            $('input[prokey=' + key + ']').prop('checked', status);
        });

        $('td.childpf input').on('change', function () {
            var dataid = $('#' + this.id).attr("dataid");

            if ($('#' + this.id).prop("checked")) {
                $("#p" + dataid).attr("childchange", 1);
                $("#p" + dataid).prop("checked", "checked").change();
            }
            else {
                var obj = $('#' + this.id).parent().find("input");
                var bol = false;
                for (i = 0; i < obj.length; i++) {
                    if ($('#' + obj[i].id).prop("checked")) {
                        bol = true;
                        continue;
                    }
                }

                if (!bol) {
                    $("#p" + dataid).attr("childchange", 2);
                    $("#p" + dataid).prop("checked", "").change();
                }
            }
        });

        // 监听指定名称（IsSystem）的开关
        form.on('switch(IsSystem)', function (data) {
            // data.elem 是得到具体事件的DOM元素，可以用它来获取或设置属性
            // data.value 是开关的值（true或false），不过通常需要用data.elem.checked来获取开关是否被选中
            console.log(data.elem.checked); // 打印当前开关是否被选中（true为选中，false为未选中）

            // 可以在这里根据开关状态执行更多操作
            if (!data.elem.checked) {
                $('input[name="IsAdmin"]').prop('checked', false);
            }
            // else {
            //     $('input[name="IsAdmin"]').prop('checked', true);
            // }

            form.render('checkbox');
        });

        // 监听指定名称（IsSystem）的开关
        form.on('switch(IsAdmin)', function (data) {
            // data.elem 是得到具体事件的DOM元素，可以用它来获取或设置属性
            // data.value 是开关的值（true或false），不过通常需要用data.elem.checked来获取开关是否被选中
            console.log(data.elem.checked); // 打印当前开关是否被选中（true为选中，false为未选中）

            // 可以在这里根据开关状态执行更多操作
            if (data.elem.checked) {
                $('input[name="IsSystem"]').prop('checked', true);
            }

            form.render('checkbox');
        });

        form.on('submit(SubmitRole)', function (data) {
            var name = $("[name='Name']").val();
            if (!name) {
                abp.notify.warn('@T("标准下的角色名称不能为空")');
                return;
            }

            var waitIndex = parent.layer.load(2);

            abp.ajax({
                url: "@Url.Action("Edit")",
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                data: $('form').serialize(),
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                dg.reload('tables');
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

        reload = function () {
            var topLayui = parent === self ? layui : top.layui;
            var bodyIframe = parent.$("#content").find(".layui-show iframe")[0].contentWindow;
            console.log(bodyIframe);
            //bodyIframe.active.reload();
            bodyIframe.layui.table.reload('tables');
        }
    });
</script>