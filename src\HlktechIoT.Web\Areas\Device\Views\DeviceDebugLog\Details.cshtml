﻿@using Pek.Timing;
@{
    Html.AppendTitleParts(T("设备故障日志详情").Text);

    var log = Model as DeviceDebugLog;
}

<style>
    .log-details {
        padding: 20px;
    }

        .log-details h2 {
            font-size: 20px;
            margin-bottom: 20px;
        }

        .log-details table {
            width: 100%;
            border-collapse: collapse;
        }

        .log-details th, .log-details td {
            padding: 8px;
            border: 1px solid #ddd;
        }

        .log-details th {
            width: 150px;
            text-align: left;
            background-color: #f9f9f9;
        }
</style>
<div class="log-details">
    <h2>@T("日志详情")</h2>
    <table>
        <tr>
            <th>@T("日志ID")</th>
            <td>@log.Id</td>
        </tr>
        <tr>
            <th>@T("产品Key")</th>
            <td>@log.ProductKey</td>
        </tr>
        <tr>
            <th>@T("设备唯一标识符")</th>
            <td>@log.DeviceCode</td>
        </tr>
        <tr>
            <th>@T("故障数据")</th>
            <td>@log.Content</td>
        </tr>
        <tr>
            <th>@T("故障类型")</th>
            <td>@log.DType</td>
        </tr>
        <tr>
            <th>@T("故障创建时间")</th>
            <td>
                @{
                    var faultTime = UnixTime.ToDateTime(log.FaultTime);
                    if (faultTime.Year >= 2001)
                    {
                        @faultTime.ToString("yyyy-MM-dd HH:mm:ss")
                    }
                }
            </td>
        </tr>
    </table>
</div>
