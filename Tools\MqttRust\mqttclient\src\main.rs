// use ntex::time::{sleep, Mill<PERSON>, Seconds};
// use ntex_mqtt::v5;

// #[derive(Debug)]
// struct Error;

// impl std::convert::TryFrom<Error> for v5::PublishAck {
//     type Error = Error;

//     fn try_from(err: Error) -> Result<Self, Self::Error> {
//         Err(err)
//     }
// }

// async fn publish(pkt: v5::Publish) -> Result<v5::PublishAck, Error> {
//     log::info!(
//         "incoming publish: {:?} -> {:?} payload {:?}",
//         pkt.id(),
//         pkt.topic(),
//         pkt.payload()
//     );
//     Ok(pkt.ack())
// }

// #[ntex::main]
// async fn main() -> std::io::Result<()> {
//     std::env::set_var("RUST_LOG", "client=trace,ntex=info,ntex_mqtt=trace");
//     env_logger::init();

//     // connect to server
//     let client = v5::client::MqttConnector::new("yun.hlktech.com:1883")
//         .client_id("adsfb35tasd|fdef3421ade33s3|B35|44334CC4EB14|NET WIFI")
//         .max_packet_size(30)
//         .keep_alive(Seconds::ONE)
//         .username(ntex::util::ByteString::from(
//             "fdef3421ade33s3|1701919397000|**************",
//         ))
//         .password(ntex::util::Bytes::from_static(
//             b"$md5$1702719285$7C92F8B89FEE26B2330B6529D1F6E568",
//         ))
//         .connect()
//         .await
//         .unwrap();

//     let sink = client.sink();

//     let router = client.resource("response", publish);
//     ntex::rt::spawn(router.start_default());

//     sink.subscribe(None)
//         .topic_filter(
//             "response".into(),
//             v5::codec::SubscriptionOptions {
//                 qos: v5::codec::QoS::AtLeastOnce,
//                 no_local: false,
//                 retain_as_published: false,
//                 retain_handling: v5::codec::RetainHandling::AtSubscribe,
//             },
//         )
//         .send()
//         .await
//         .unwrap();

//     sleep(Millis(10_000)).await;

//     log::info!("closing connection");
//     sink.close();
//     sleep(Millis(1_000)).await;

//     Ok(())
// }

use std::time::Duration;

use rumqttc::{Client, MqttOptions, QoS};

#[tokio::main]
async fn main() {
    use rumqttc::{AsyncClient, MqttOptions, QoS};
    use std::time::Duration;
    use tokio::{task, time};

    // let mut mqttoptions = MqttOptions::new(
    //     "test|abc|B35|D0B76BBD-8B08-C814-AD85-047C16C0BD9F|NET WIFI",
    //     "localhost",
    //     1883,
    // );
    let mut mqttoptions = MqttOptions::new(
        "adsfb35tasd|fdef3421ade33s3|B35|44334CC4EB14|NET WIFI",
        "yun.hlktech.com",
        1883,
    );
    // mqttoptions
    //     .set_keep_alive(Duration::from_secs(5))
    //     .set_credentials(
    //         "abc|1702723543734|**************",
    //         "$md5$1702723543$F5AEF201CEC49EB360BDFB5C09B775F5",
    //     );
    mqttoptions
        .set_keep_alive(Duration::from_secs(5))
        .set_credentials(
            "fdef3421ade33s3|1701919397000|**************",
            "$md5$1702719285$7C92F8B89FEE26B2330B6529D1F6E568",
        );

    let (mut client, mut eventloop) = AsyncClient::new(mqttoptions, 10);
    client
        .subscribe("hello/rumqtt", QoS::AtMostOnce)
        .await
        .unwrap();

    task::spawn(async move {
        for i in 0..=10000u32 {
            let data: Vec<u8> = (0..i).map(|_| i as u8).collect();
            client
                .publish("hello/rumqtt", QoS::AtLeastOnce, false, data)
                .await
                .unwrap();
            time::sleep(Duration::from_millis(100)).await;
        }
    });

    loop {
        let notification = eventloop.poll().await.unwrap();
        println!("Received = {:?}", notification);
    }
}
