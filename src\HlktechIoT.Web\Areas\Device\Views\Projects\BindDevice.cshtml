@model AppUser
@{
    Html.AppendTitleParts(T("用户设备绑定").Text);

}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    .layui-form-item {
        padding: 5px;
        display: block;
        width: 100% !important;
        height: 100% !important;
        font-size: 15px;
        /* border:2px solid ; */
    }
</style>

<form class="layui-form dg-form">
    <div class="layui-form-item" id="search" style="margin-bottom: 6px;">
        <div style="display:flex;width:100%;place-items:center;">
            <div>@T("搜索设备")：</div>
            <div class="layui-input-inline">
                <input type="text" style="width:300px;" name="key" id="key" placeholder="@T("用户名称、昵称等")" autocomplete="off" class="layui-input" lay-filter="key">
            </div>
        </div>
    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>


<script asp-location="Footer">
    $("body").on("click", function (event) {
        $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
        $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetDeviceList")'
            , page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 80
            , cols: [[
                { field: 'Code', title: '@T("DeviceName")', minWidth: 140 }
                , { field: 'Name', title: '@T("昵称")', minWidth: 130 }
                , {
                    title: '@T("在线")', width: 80, templet: (d) => {
                        if (d.Online) {
                            return '<span style="color:mediumseagreen !important;">在线<i class="layui-icon layui-icon-rss" style="margin-left:1px"></i></span>'
                        }
                        return '<span style="color:gray !important;">离线<i class="layui-icon layui-icon-close" style="color:red;margin-left:2px"></i></span>'
                    }
                }
                , { field: 'ProductName', title: '@T("产品")', width: 140 }
                , {
                    field: 'Owned', title: '@T("用户身份")', width: 85, templet: function (d) {
                        return d.Owned == 1 ? '@T("拥有者")' : '@T("分享者")';
                    }
                }
                , { field: 'Enable', title: '@T("启用")', templet: '#switchTpl', width: 100 }
                , { field: 'Version', title: '@T("版本")', width: 180 }
                , { field: 'PostPeriod', title: '@T("上报间隔")', width: 90, align: 'center' }
                , {
                    field: 'LastLogin', title: '@T("最后上线时间")', width: 160, templet: function (d) {
                        if (d.LastLogin && !/^0001/.test(d.LastLogin)) {
                            return d.LastLogin;
                        }
                        return "";
                    }
                }
                // , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', minWidth: 290 }
            ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-180'
            , id: 'tables'
            , where: {
                Id: '@Model.Id'
            }
        });

        window.active = {
            reload: function () {
                table.reload('tables', {
                    where: {
                        Id: '@Model.Id',
                        Key: $("#key").val()
                    },
                    page: {
                        curr: 1
                    }
                });
            }
        };

        $("#key").on("input", function (e) {
            active.reload()
        });


        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', function (index) {
                    $.post('@Url.Action("Delete")', { Id: data.Id, Code: data.Code }, function (data) {
                        if (data.success) {
                            abp.notify.success('@T("删除成功")');
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            } else if (obj.event === 'Userdevice') {
                window.Userdevice(data)
            }
        });

        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'select') {
                window.select();
            } else if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });

        window.warning = function (msg) {
            abp.notify.warn(msg);
        }

        window.saveCallback = function (data) {
            layer.close(data.index);
            abp.notify.success(data.msg);
            active.reload();
        }

    });
</script>
<script type="text/html" id="switchTpl">
    <input type="checkbox" name="status" disabled data-id="{{d.Id}}" lay-skin="switch" lay-text="ON|OFF" lay-filter="statusedit" {{ d.Enable == "1" ? 'checked' : ''}}>
</script>



@*<script type="text/html" id="tool">
    @if (this.Has((PermissionFlags)32))
    {
         <a class="pear-btn pear-btn-warming pear-btn-xs" style="width:80px;" lay-event="Userdevice"> @T("设备")</a>
    }

</script>*@