﻿@model Language
@{
    Html.AppendTitleParts(T("编辑语言").Text);
}
<style asp-location="true">
    html {
        background-color: #f2f2f2;
        background-color: transparent !important;
        color: #666;
    }

    body {
        height: 100%;
    }

    .pear-container {
        background-color: white;
    }

    .containers {
        width: 100%;
        padding-top: 20px;
        height: 100%
    }

    .container.form-horizontal {
        width: 100%;
        padding-left: -50px
    }

    .label-width {
    @if (language.UniqueSeoCode == "en")
    {
        <text> width: 100px;
        </text>
    }
    else
    {
        <text> width: 85px;
        </text>
    }
    }

    .text-width {
        width: 154px
    }

    .text-width2 {
        width: 240px
    }

    .text-width3 {
        width: 327px
    }

    .label-width span {
        color: red
    }

    .layui-textarea {
        width: 90%;
    }

    .layui-form-item {
        padding: 0px 10px;
    }

        .layui-form-item.btn {
            text-align: center
        }
</style>
<div class="containers">
    <form class="layui-form" action="">

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("标题")</label>
            <div class="layui-input-inline">
                <input type="text" name="Name" id="Name" autocomplete="off" class="layui-input text-width" value="@Model!.Name">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("显示名称")</label>
            <div class="layui-input-inline">
                <input type="text" name="DisplayName" id="DisplayName" autocomplete="off" class="layui-input text-width" value="@Model.DisplayName">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("英文名")</label>
            <div class="layui-input-inline">
                <input type="text" name="EnglishName" id="EnglishName" autocomplete="off" class="layui-input text-width2" value="@Model.EnglishName">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("语言缩写")</label>
            <div class="layui-input-inline">
                <input type="text" name="LanguageCulture" id="LanguageCulture" autocomplete="off" class="layui-input text-width" value="@Model.LanguageCulture">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("Url缩写")</label>
            <div class="layui-input-inline">
                <input type="text" name="UniqueSeoCode" id="UniqueSeoCode" autocomplete="off" class="layui-input text-width" value="@Model.UniqueSeoCode">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("旗帜")</label>
            <div class="layui-input-inline">
                <input type="text" name="Flag" id="Flag" autocomplete="off" class="layui-input text-width3" value="@Model.Flag">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("域名")</label>
            <div class="layui-input-inline">
                <input type="text" name="Domain" id="Domain" autocomplete="off" class="layui-input text-width3" value="@Model.Domain">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width">LCID</label>
            <div class="layui-input-inline">
                <input type="number" name="Lcid" id="Lcid" autocomplete="off" class="layui-input text-width" value="@Model.Lcid">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("状态")</label>
            <div class="layui-input-inline">
                <select name="Status" id="Status">
                    @if (Model.Status)
                    {
                        <option value="true" selected="">@T("有效")</option>
                        <option value="false">@T("无效")</option>
                    }
                    else
                    {
                        <option value="true">@T("有效")</option>
                        <option value="false" selected="">@T("无效")</option>
                    }

                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("是否默认")</label>
            <div class="layui-input-inline">
                <select name="IsDefault" id="IsDefault">
                    @if (Model.IsDefault == 1)
                    {
                        <option value="true" selected="">@T("是")</option>
                        <option value="false">@T("否")</option>
                    }
                    else
                    {
                        <option value="true">@T("是")</option>
                        <option value="false" selected="">@T("否")</option>
                    }
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("排序")</label>
            <div class="layui-input-inline">
                <input type="number" name="DisplayOrder" id="DisplayOrder" autocomplete="off" class="layui-input" value="@Model.DisplayOrder">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label label-width">@T("描述")</label>
            <div class="layui-input-block">
                <textarea placeholder="@T("请输入内容")" name="Remark" id="Remark" class="layui-textarea">@Model.Remark</textarea>
            </div>
        </div>
        <div class="layui-form-item btn">
            <input type="hidden" value="@Model.Id" name="Id" />
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" value="@T("保存")" />
        </div>
    </form>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'dg', 'form'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var dg = layui.dg;

        form.on('submit(Submit)', function (data) {
            if (data.field.Name.length == 0) {
                abp.notify.warn("@T("标题不为能空")");
                return;
            }
            if (data.field.DisplayName.length == 0) {
                abp.notify.warn("@T("显示名称不为能空")");
                return;
            }
            if (data.field.EnglishName.length == 0) {
                abp.notify.warn("@T("英文名称不为能空")");
                return;
            }
            if (data.field.LanguageCulture.length == 0) {
                abp.notify.warn("@T("语言缩写不为能空")");
                return;
            }
            if (data.field.UniqueSeoCode.length == 0) {
                abp.notify.warn("@T("Url缩写不为能空")");
                return;
            }

            var waitIndex = parent.layer.load(2);

            abp.ajax({
                url: "@Url.Action("Edit")",
                data: JSON.stringify(data.field),
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                dg.reload('tables');
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;

        });
    });
</script>