﻿@using NewLife.Reflection
@{
    Layout = null;
    ViewBag.Title = "程序集列表";
}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit|ie-stand">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width" />
    @*<link href="/Content/bootstrap.min.css" rel="stylesheet" />

    <script src="/Scripts/jquery1111.min.js"></script>
    <script src="/Scripts/bootstrap.min.js"></script>*@

    <style>
        .container .table > tbody > tr > td {
            padding: 5px 5px;
        }
      

        .container .table {
            margin: 0;
            background-color: #fff;
            color: #494e52;
            font-size: 13px;
        }

        .table-bordered {
            border: 1px solid #ddd;
        }

        .table > tbody > tr > td {
            border-top: 0;
            border-bottom: 1px solid #e7e7e7;
        }

        .container .table > thead > tr > th, .table > tbody > tr > td {
            vertical-align: middle;
        }

        .table-bordered > tbody > tr > td, .table-bordered > tbody > tr > th, .table-bordered > tfoot > tr > td, .table-bordered > tfoot > tr > th, .table-bordered > thead > tr > td, .table-bordered > thead > tr > th {
            border: 1px solid #ddd;
        }

        .container .table thead tr th {
            border: 0;
            height: 45px;
            font-weight: 900 !important;
        }

        .table thead th:first-child {
            border-top-left-radius: 5px;
        }

        table {
            border-spacing: 0;
            border-collapse: collapse;
        }

        .table#list, .table.category_table, .table#applyList, .table#HandSlideDatagrid, .table#typeDatagrid, .table#shopDatagrid, .table#topicGrid, .table#listAutoReplay, .table#productList {
            margin-top: 5px;
            margin-bottom: 60px;
        }

        .table {
            width: 100%;
            max-width: 100%;
            margin-bottom: 20px;
        }

            .table thead {
                background-color: #f9f9f9;
                border-bottom: 1px solid #e7e7e7;
                border-top-left-radius: 15px !important;
                border-top-right-radius: 15px !important;
                color: #494e52;
                font-size: 14px;
            }
    </style>
    <script>
        var pleaseSelect = '@T("请选择")';
        var layuiPrint = '@T("打印")';
        var layuiExport = '@T("导出")';
        var layuiFilterColumn = '@T("筛选列")';
        var layuiArticlePage = '@T("条/页")';
        var layuiTotal = '@T("共")';
        var layuiBtn = '@T("确定")';
        var layuiGoPage = '@T("到第")';
        var layuiPage = '@T("页")';
        var layuiNumber = '@T("个")';
        var layuiPrev = '@T("上一页")';
        var layuiNext = '@T("下一页")';
        var layuiNoData = '@T("无数据")';
        var layuiAsc = '@T("升序")';
        var layuiDesc = '@T("降序")';
        var layuiCloseCurrent = '@T("关 闭 当 前")';
        var layuiCloseOther = '@T("关 闭 其 他")';
        var layuiCloseAll = '@T("关 闭 全 部")';
        var layuiMenuStyle = '@T("菜单风格")';
        var layuiTopStyle = '@T("顶部风格")';
        var layuiThemeColor = '@T("主题配色")';
        var layuiMoreSettings = '@T("更多设置")';
        var layuiOpen = '@T("开")';
        var layuiClose = '@T("关")';
        var layuiMenu = '@T("菜单")';
        var layuiView = '@T("视图")';
        var layuiBanner = '@T("通栏")';
        var layuiThroughColor = '@T("通色")';
        var layuiFooter = '@T("页脚")';
        var layuiSelectAll = '@T("全选")';
        var layuiClear = '@T("清空")';
        var layuiReverseSelection = '@T("反选")';
        var layuiPeeling = '@T("换肤")';
        var layuiNoDataYet = '@T("暂无数据")';
        var layuiSearch = '@T("搜索")';
        var layuiPrevious = '@T("上一页")';
        var layuiNext = '@T("下一页")';
        var layuiNotAllowClose = '@T("前页面不允许关闭")';
        var layuiOpenAtMost = '@T("最多打开")';
        var layuiTabs = '@T("个标签页")';
    </script>
</head>
<body>
    <div class="container" style="width:100%;">
        <div>
            <table class="table table-bordered table-hover table-striped table-condensed" id="list">
                <thead>
                    <tr>
                        <th style="text-align:center;padding-left:0px;padding-right:0px;width:auto;border:1px solid #ddd;">@T("名称")</th>
                        <th style="text-align:center;padding-left:0px;padding-right:0px;width:auto;border:1px solid #ddd;">@T("标题")</th>
                        <th style="text-align:center;padding-left:0px;padding-right:0px;width:auto;border:1px solid #ddd;">@T("文件版本")</th>
                        <th style="text-align:center;padding-left:0px;padding-right:0px;width:auto;border:1px solid #ddd;">@T("内部版本")</th>
                        <th style="text-align:center;padding-left:0px;padding-right:0px;width:140px;border:1px solid #ddd;">@T("编译时间")</th>
                        <th style="text-align:center;padding-left:0px;padding-right:0px;width:auto;border:1px solid #ddd;">@T("描述")</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (AssemblyX item in ViewBag.MyAllAsms)
                    {
                        <tr himalldatagrid-row-index="0">
                            <td style="width:auto;"><div class="hiMallDatagrid-cell ">@item.Name</div></td>
                            <td style="width:auto;"><div class="hiMallDatagrid-cell ">@item.Title</div></td>
                            <td style="width:auto;"><div class="hiMallDatagrid-cell ">@item.FileVersion</div></td>
                            <td style="width:auto;"><div class="hiMallDatagrid-cell ">@item.Version</div></td>
                            <td style="width:auto;width:150px"><div class="hiMallDatagrid-cell ">@(item.Compile.Year <= 2000 ? "" : item.Compile.ToFullString())</div></td>
                            <td style="width:auto;"><div class="hiMallDatagrid-cell ">@item.Description</div></td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>