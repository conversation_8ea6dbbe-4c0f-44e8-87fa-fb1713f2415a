@model Project
@{
    Html.AppendTitleParts(T("成员").Text);

}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }
    .layui-form-item{
        margin-top:-25px !important;
        display:block;
        width:100% !important;
        height:100% !important;
        font-size:15px;
        /* border:2px solid ; */
    }
    /* 操作列自适应样式 */
    .layui-table-fixed-r .layui-table-body {
        overflow-x: hidden !important;
    }

    .operation-column {
        white-space: nowrap;
        padding: 0 5px; /* 添加左右边距 */
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center; /* 垂直居中 */
        flex-wrap: nowrap;
        height: 100%; /* 确保容器占满单元格高度 */
        width: 100%; /* 确保容器占满单元格宽度 */
        box-sizing: border-box; /* 确保内边距不增加总宽度 */
    }

        .operation-column .pear-btn {
            margin-right: 6px; /* 保持原有按钮间距 */
            padding: 0 8px; /* 保持按钮内边距 */
            height: 30px; /* 保持按钮高度 */
            line-height: 30px; /* 行高与高度一致 */
            min-width: 44px; /* 按钮最小宽度 */
            text-align: center;
            flex-grow: 1; /* 按钮自动扩展占用空间 */
            flex-basis: 0; /* 所有按钮基础宽度相同 */
        }

            .operation-column .pear-btn:last-child {
                margin-right: 0;
            }

    /* 确保表格单元格内容居中 */
    .layui-table-cell {
        text-align: center;
        padding: 0 2px; /* 保持单元格内边距 */
    }

    /* 固定列样式优化 */
    .layui-table-fixed {
        height: auto !important;
    }

    /* 确保表格右侧固定列正确显示 */
    .layui-table-fixed-r {
        right: 0 !important;
    }

        /* 优化表格右侧固定列的单元格 */
        .layui-table-fixed-r .layui-table-cell {
            padding: 0;
        }

</style>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    $("body").on("click", function (event) {
        $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
        $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;

                 // 按钮配置集中定义
        var operationButtons = [
            { text: '@T("编辑")', event: 'edit', class: 'pear-btn-primary' },
            { text: '@T("删除")', event: 'del', class: 'pear-btn-danger' },
        ];

        // 将按钮配置保存到全局变量，供模板使用
        window.operationButtons = operationButtons;

        // 动态计算操作列宽度的函数
        function calculateOperationColumnWidth() {
            try {
                var buttonMargin = 6; // 按钮间距
                var extraPadding = 24; // 额外边距
                var baseButtonPadding = 20; // 按钮内边距

                if (operationButtons.length === 0) {
                    return 100;
                }

                var totalWidth = 0;

                operationButtons.forEach(function(button) {
                    // 调整文字宽度估算：中文字符约16px，英文字符约9px
                    var text = button.text;
                    var textWidth = 0;
                    for (var i = 0; i < text.length; i++) {
                        var char = text.charAt(i);
                        if (/[\u4e00-\u9fa5]/.test(char)) {
                            textWidth += 16; // 中文字符
                        } else {
                            textWidth += 9;  // 英文字符
                        }
                    }

                    var buttonWidth = textWidth + baseButtonPadding;
                    totalWidth += buttonWidth;
                });

                // 加上按钮间距和额外边距
                totalWidth += (operationButtons.length - 1) * buttonMargin + extraPadding;

                // 设置最小宽度
                if (totalWidth < 120) {
                    totalWidth = 120;
                }

                console.log('计算的操作列宽度:', totalWidth, '按钮数量:', operationButtons.length, '按钮配置:', operationButtons);
                return totalWidth;
            } catch (error) {
                console.error('计算操作列宽度时出错:', error);
                return 200;
            }
        }

        // 应用操作列宽度的函数
        function applyOperationColumnWidth() {
            try {
                var calculatedWidth = calculateOperationColumnWidth();

                var fixedRightHeader = $('.layui-table-fixed-r .layui-table-header table thead tr th:last-child');
                var fixedRightBody = $('.layui-table-fixed-r .layui-table-body table tbody tr td:last-child');

                if (fixedRightHeader.length > 0) {
                    fixedRightHeader.css('width', calculatedWidth + 'px');
                    fixedRightHeader.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                if (fixedRightBody.length > 0) {
                    fixedRightBody.css('width', calculatedWidth + 'px');
                    fixedRightBody.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                var mainTableHeader = $('#tablist').next('.layui-table-view').find('.layui-table-header table thead tr th:last-child');
                var mainTableBody = $('#tablist').next('.layui-table-view').find('.layui-table-body table tbody tr td:last-child');

                if (mainTableHeader.length > 0) {
                    mainTableHeader.css('width', calculatedWidth + 'px');
                    mainTableHeader.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                if (mainTableBody.length > 0) {
                    mainTableBody.css('width', calculatedWidth + 'px');
                    mainTableBody.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                console.log('已应用操作列宽度:', calculatedWidth);
            } catch (error) {
                console.error('应用操作列宽度时出错:', error);
            }
        }

        // 监听窗口大小变化
        $(window).on('resize', function() {
            try {
                setTimeout(function() {
                    applyOperationColumnWidth();
                    console.log('窗口大小变化，已重新调整操作列宽度');
                }, 100);
            } catch (error) {
                console.error('窗口resize时出错:', error);
            }
        });

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetProjectShareList", new { Id = Model.Id })'
            , page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 60
            , cols: [[
                { field: 'Id', title: '@T("ID")',width:70 }
                , { field: 'UserName', title: '@T("账号")',minWidth:180 }
                , { field: 'UName', title: '@T("备注名")',minWidth:180 }
                , { field: 'ProjectName', title: '@T("项目")', width: 140 }
                , { field: 'RType', title: '@T("权限类型")', width: 140 }
                , { field: 'CreateTime', title: '@T("创建时间")', minWidth: 180 }
                , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: calculateOperationColumnWidth() }
            ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-100'
            , id: 'usercount'
            , done: function (res, curr, count) {
                try {
                    // 延迟执行，确保DOM已经渲染完成
                    setTimeout(function() {
                        applyOperationColumnWidth();
                    }, 200);
                } catch (error) {
                    console.error('表格done回调中出错:', error);
                }
            }
        });
        // bug: 监听输入框的input事件，当输入框失去焦点时，监听事件会失效。
        $("#key").on("input", function (e) {
            active.reload();
            // console.log('监听输入', $("#key").val());
        });

       window.active = {
            reload: function () {
                // 第一步：先保存上次输入的值
                let searchData = $("#key").val()
                table.reload('usercount', {
                    
                    where: {
                        Key: $("#key").val(),
                    },
                    page: {
                        curr: 1
                    }
                });
                // 第二步：重新渲染回input
                $("#key").val(searchData)
                $("#key").focus()// 第三步：重新渲获取焦点
                setTimeout(function () {
                    $("#key").on("input", function (e) {// 第四步：重新监听
                        active.reload();
                        // console.log('监听输入', $("#key").val());
                    });
                }, 100); // 延迟100毫秒再监听输入框的input事件
            }
        };


        //监听开关事件
        form.on('switch(statusedit)', function (data) {
            //开关是否开启，true或者false
            var checked = data.elem.checked;
            //获取所属属性值
            var id = data.elem.attributes['data-id'].nodeValue;
            $.post('@Url.Action("ModifyAUserState")', { id: id, status: checked }, function (res) {
                if (res.success) {
                    abp.notify.success(res.msg);
                }
                else {
                    abp.notify.error(res.msg);
                }
            });
        });

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', function (index) {
                    $.post('@Url.Action("UserDelete")', { Id: data.Id, Code: data.Code }, function (data) {
                        if (data.success) {
                            abp.notify.success('@T("删除成功")');
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            } else if (obj.event == 'edit') {
                window.edituser(data);
            }
        });

        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'select') {
                window.select();
            } else if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'refresh') {
                active.reload();
            } else if (obj.event === 'adduser') {
                window.adduser();
            }
        });

        window.adduser = function () {

            window.addPageIndex = top.layui.dg.popupRight({
                id: 'AddUser'
                , title: ' @T("新增项目成员")'
                , closeBtn: 1
                , area: ['580px']
                , success: function (obj, index) {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("AddUser")/' + @Model.Id + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });

            window.name = 'AddUser'


        }

        window.edituser = function (data) {

            window.editPageIndex = top.layui.dg.popupRight({
                id: 'EditUser'
                , title: ' @T("编辑项目成员")'
                , closeBtn: 1
                , area: ['580px']
                , success: function (obj, index) {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("EditUser")/' + data.Id + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });

            window.name = 'EditUser'


        }

        

        window.warning = function (msg) {
            abp.notify.warn(msg);
        }

        window.saveCallback = function (data) {
            layer.close(data.index);
            abp.notify.success(data.msg);
            active.reload();
        }

    });
</script>
<script type="text/html" id="switchTpl">
    <input type="checkbox" name="status" data-id="{{d.Id}}" lay-skin="switch" lay-text="ON|OFF" lay-filter="statusedit" {{ d.Enable == "1" ? 'checked' : ''}}>
</script>
<script type="text/html" id="user-toolbar">
    <form class="layui-form dg-form">
        <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
            <div style="display:flex;width:100%;">
                <div>@T("关键词")：</div>
                <div class="layui-input-inline">
                    <input type="text" style="width:300px;" name="key" id="key" placeholder="@T("用户名称、用户昵称、手机号、邮箱等")" autocomplete="off" class="layui-input" lay-filter="key">
                </div>
            </div>
        </div>
    </form>

     <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="adduser">
           <i class="layui-icon layui-icon-add-1"></i>
            @T("新增")
          </button>
</script>
<script type="text/html" id="tool">
    <div class="operation-column">
    {{#  layui.each(window.operationButtons, function(index, button){ }}
        <a class="pear-btn {{button.class}} pear-btn-xs" lay-event="{{button.event}}">{{button.text}}</a>
    {{#  }); }}
    </div>

</script>