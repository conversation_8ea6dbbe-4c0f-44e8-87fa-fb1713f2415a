﻿using DG.SafeOrbit.Extensions;
using DG.Web.Framework;

using HlktechIoT.Common;
using HlktechIoT.Data;
using HlktechIoT.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Caching;
using NewLife.Data;
using NewLife.Log;
using NewLife.Serialization;

using Pek;
using Pek.Compress;
using Pek.Configs;
using Pek.Cookies;
using Pek.Helpers;
using Pek.Models;
using Pek.Timing;

namespace HlktechIoT.Controllers;

/// <summary>
/// 设备授权
/// </summary>
public class DeviceLicenceController : ControllerBaseX {
    /// <summary>
    /// 缓存提供者
    /// </summary>
    private ICacheProvider _cacheProvider;

    /// <summary>
    /// Cookie操作类
    /// </summary>
    private ICookie _cookie;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="cacheProvider">缓存提供者</param>
    /// <param name="cookie">Cookie操作类</param>
    public DeviceLicenceController(ICacheProvider cacheProvider, ICookie cookie)
    {
        _cacheProvider = cacheProvider;
        _cookie = cookie;
    }

    /// <summary>
    /// 创建支付链接
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public IActionResult CreatePayUrl(String deviceNames, Int32 payWay, Int32 year,Int64 Id)
    {
        var res = new DResult();

        var Content = _cookie.GetValue<String>($"{RedisSetting.Current.CacheKeyPrefix}App");
        if (Content.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("请从APP打开");
            return Json(res);
        }
        var strArray = Content.Split('|');
        if (strArray.Length != 3)
        {
            res.msg = GetResource("参数错误");
            return Json(res);
        }
        var AppType = strArray[0].ToInt();
        var AccessId = strArray[1];
        var Info = strArray[2];

        ProjectApp? model;
        var appSecret = String.Empty;
        if (AppType == 1)
        {
            model = ProjectApp.FindByAndroidAppKey(AccessId);
            appSecret = model?.AndroidAppSecret;
        }
        else
        {
            model = ProjectApp.FindByIosAppKey(AccessId);
            appSecret = model?.IosAppSecret;
        }
        if (model == null)
        {
            res.msg = GetResource("参数错误");
            return Json(res);
        }

        var key = DHLZW.GetKeyFromText(appSecret!);

        Info = Base64Helper.Base64ToString(Info); // 解密信息
        var Infos = Info.SplitAsInt(",");
        Info = DHLZW.Decompress([.. Infos], key); // 解压缩信息

        var dic = Info.SplitAsDictionary("=", ",");
        if (!dic.TryGetValue("IdentityId", out var IdentityId))
        {
            res.msg = GetResource("参数无效");
            return Json(res);
        }

        if (!dic.TryGetValue("AppTime", out var AppTime))
        {
            res.msg = GetResource("参数无效");
            return Json(res);
        }

        var modelAppUser = AppUser.FindByIdentityId(IdentityId);
        if (modelAppUser == null)
        {
            res.msg = GetResource("非法操作");
            return Json(res);
        }
        if (modelAppUser.Devices.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("未找到关联设备");
            return Json(res);
        }
        if (deviceNames.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("设备号不能为空");
            return Json(res);
        }

        if (payWay <= 0)
        {
            res.msg = GetResource("支付方式不能为空");
            return Json(res);
        }

        if (year <= 0)
        {
            res.msg = GetResource("续期年限不能为空");
            return Json(res);
        }

        var modelRenewalYear = RenewalYear.FindByYear(year);
        if (modelRenewalYear == null)
        {
            res.msg = GetResource("续期年限不存在");
            return Json(res);
        }

        foreach (var item in deviceNames.Split(","))
        {
            if (!modelAppUser.Devices.Contains(item))
            {
                res.msg = GetResource($"未找到关联设备{item}");
                return Json(res);
            }
            var d = Device.FindByCode(item);
            if (d == null)
            {
                res.msg = GetResource($"设备{item}不存在");
                return Json(res);
            }
            var p = Product.FindById(d.ProductId);
            if (p == null || !p.EnableLimitTime)
            {
                res.msg = GetResource($"设备{item}未启用续期");
                return Json(res);
            }
        }

        decimal amount = 0;

        if (deviceNames.Split(",").Length >= modelRenewalYear.Quantity && modelRenewalYear.Discount > 0)
        {
            amount = modelRenewalYear.Discount;
        }
        else
        {
            amount = modelRenewalYear.Amount;
        }

        amount = amount * deviceNames.Split(",").Length;

        amount = Math.Round(amount, 2);

        bool insert = false;

        if(Id == 0)
        {
            insert = true;
            Id = Common.CommonFields.Snowflake.NewId();
        }

        XTrace.WriteLine($"创建支付链接生成的ID：{Id}");

        if (payWay == 1)
        {
            var response = PayHelper.AliTradeWapPay(Id.SafeString(), amount.ToString("N2"));

            XTrace.WriteLine($"创建支付宝支付链接：{response.ToJson()}");

            if (response.IsError)
            {
                res.msg = GetResource("支付失败");
                return Json(res);
            }

            res.data = response.Body;
        }
        if (payWay == 2)
        {
            var response = PayHelper.WxTradeWapPay(Id.SafeString(), (amount * 100).ToInt()).Result;

            XTrace.WriteLine($"创建微信支付链接：{response.ToJson()}");

            if (response.H5Url.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("支付失败");
                return Json(res);
            }

            res.data = response.H5Url + "&redirect_url="+ Settings.Current.NotifyUrl + "/DeviceLicence/PayCallBack?Id=" + Id;
        }

        if (insert)
        {
            RenewalPay renewalPay = new()
            {
                Id = Id,
                Devices = deviceNames,
                TradeNo = Guid.NewGuid().SafeString(),
                RenewalYear = year,
                Amount = amount,
                PayWay = payWay,
                CreateUserID = modelAppUser.Id,
                CreateUser = modelAppUser.Name,
            };

            renewalPay.Insert();
        }

        res.success = true;
        return Json(res);
    }

    /// <summary>
    /// 购买授权
    /// </summary>
    /// <param name="AccessId"></param>
    /// <param name="Info"></param>
    /// <param name="AppType">App类型 1为安卓，2为IOS</param>
    /// <returns></returns>
    public IActionResult Index(String AccessId, String Info, Int32 AppType)
    {
        XTrace.WriteLine($"拿到的数据：{AccessId}:{Info}");
        AccessId = AccessId.SafeString();
        Info = Info.SafeString();

        if (AccessId.IsNullOrWhiteSpace() || Info.IsNullOrWhiteSpace())
        {
            return Content("<div>参数错误</div>", "text/html");
        }

        ProjectApp? model;
        var appSecret = String.Empty;
        if (AppType == 1)
        {
            model = ProjectApp.FindByAndroidAppKey(AccessId);
            appSecret = model?.AndroidAppSecret;
        }
        else
        {
            model = ProjectApp.FindByIosAppKey(AccessId);
            appSecret = model?.IosAppSecret;
        }
        if (model == null)
        {
            return Content("<div>参数错误</div>", "text/html");
        }

        var key = DHLZW.GetKeyFromText(appSecret!);

        var Info1 = Base64Helper.Base64ToString(Info); // 解密信息
        var Infos = Info1.SplitAsInt(",");
        var Info2 = DHLZW.Decompress([.. Infos], key); // 解压缩信息

        var dic = Info2.SplitAsDictionary("=", ",");
        if (!dic.TryGetValue("IdentityId", out var IdentityId))
        {
            return Content("<div>参数无效</div>", "text/html");
        }

        if (!dic.TryGetValue("AppTime", out var AppTime))
        {
            return Content("<div>参数无效</div>", "text/html");
        }

        var modelAppUser = AppUser.FindByIdentityId(IdentityId);
        if (modelAppUser == null)
        {
            return Content("<div>非法操作</div>", "text/html");
        }

        if (!_cacheProvider.Cache.ContainsKey($"{RedisSetting.Current.CacheKeyPrefix}_{IdentityId}"))
        {
            XTrace.WriteLine($"获取到的时间：{AppTime}");
            if (UnixTime.ToDateTime(AppTime.ToLong()).AddHours(2) < DateTime.Now)
            {
                return Content("<div>请从APP打开</div>", "text/html");
            }

            _cacheProvider.Cache.Set($"{RedisSetting.Current.CacheKeyPrefix}_{IdentityId}", AppTime);
        }

        _cookie.SetValue($"{RedisSetting.Current.CacheKeyPrefix}App", $"{AppType}|{AccessId}|{Info}", 120);

        // 开始写业务逻辑





        return View();
    }

    public IActionResult PayRecord()
    {
        return View();
    }

    /// <summary>
    /// 查询支付订单
    /// </summary>
    /// <returns></returns>
    public IActionResult QueryPayOrder(Int64 Id)
    {
        var res = new DResult();

        var Content = _cookie.GetValue<String>($"{RedisSetting.Current.CacheKeyPrefix}App");
        if (Content.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("请从APP打开");
            return Json(res);
        }
        var strArray = Content.Split('|');
        if (strArray.Length != 3)
        {
            res.msg = GetResource("参数错误");
            return Json(res);
        }
        var AppType = strArray[0].ToInt();
        var AccessId = strArray[1];
        var Info = strArray[2];

        ProjectApp? model;
        var appSecret = String.Empty;
        if (AppType == 1)
        {
            model = ProjectApp.FindByAndroidAppKey(AccessId);
            appSecret = model?.AndroidAppSecret;
        }
        else
        {
            model = ProjectApp.FindByIosAppKey(AccessId);
            appSecret = model?.IosAppSecret;
        }
        if (model == null)
        {
            res.msg = GetResource("参数错误");
            return Json(res);
        }

        var key = DHLZW.GetKeyFromText(appSecret!);

        Info = Base64Helper.Base64ToString(Info); // 解密信息
        var Infos = Info.SplitAsInt(",");
        Info = DHLZW.Decompress([.. Infos], key); // 解压缩信息

        var dic = Info.SplitAsDictionary("=", ",");
        if (!dic.TryGetValue("IdentityId", out var IdentityId))
        {
            res.msg = GetResource("参数无效");
            return Json(res);
        }

        if (!dic.TryGetValue("AppTime", out var AppTime))
        {
            res.msg = GetResource("参数无效");
            return Json(res);
        }

        var modelAppUser = AppUser.FindByIdentityId(IdentityId);
        if (modelAppUser == null)
        {
            res.msg = GetResource("非法操作");
            return Json(res);
        }

        var modal = RenewalPay.FindById(Id);

        if (modal == null || modal.Status > 0)
        {
            res.msg = GetResource("支付不存在");
            return Json(res);
        }

        if (modal.PayWay == 1)
        {
            var response = PayHelper.AlipayTradeQuery(modal.Id.SafeString());
            if (response.TradeStatus == "TRADE_SUCCESS")
            {
                modal.Status = 1;
                modal.TradeNo = response.TradeNo;
                modal.PayTime = DateTime.Now;
                res.success = true;
            }
        }

        modal.Update();
        return Json(res);
    }

    /// <summary>
    /// 根据deviceName查询用户绑定的设备
    /// </summary>
    /// <param name="deviceName"></param>
    /// <returns></returns>
    public IActionResult QueryUserDevice(String deviceName)
    {
        var res = new DResult();

        var Content = _cookie.GetValue<String>($"{RedisSetting.Current.CacheKeyPrefix}App");
        if (Content.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("请从APP打开");
            return Json(res);
        }
        var strArray = Content.Split('|');
        if (strArray.Length != 3)
        {
            res.msg = GetResource("参数错误");
            return Json(res);
        }
        var AppType = strArray[0].ToInt();
        var AccessId = strArray[1];
        var Info = strArray[2];

        ProjectApp? model;
        var appSecret = String.Empty;
        if (AppType == 1)
        {
            model = ProjectApp.FindByAndroidAppKey(AccessId);
            appSecret = model?.AndroidAppSecret;
        }
        else
        {
            model = ProjectApp.FindByIosAppKey(AccessId);
            appSecret = model?.IosAppSecret;
        }
        if (model == null)
        {
            res.msg = GetResource("参数错误");
            return Json(res);
        }

        var key = DHLZW.GetKeyFromText(appSecret!);

        Info = Base64Helper.Base64ToString(Info); // 解密信息
        var Infos = Info.SplitAsInt(",");
        Info = DHLZW.Decompress([.. Infos], key); // 解压缩信息

        var dic = Info.SplitAsDictionary("=", ",");
        if (!dic.TryGetValue("IdentityId", out var IdentityId))
        {
            res.msg = GetResource("参数无效");
            return Json(res);
        }

        XTrace.WriteLine($"续期用户的IdentityId：{IdentityId}");

        if (!dic.TryGetValue("AppTime", out var AppTime))
        {
            res.msg = GetResource("参数无效");
            return Json(res);
        }

        var modelAppUser = AppUser.FindByIdentityId(IdentityId);
        if (modelAppUser == null)
        {
            res.msg = GetResource("非法操作");
            return Json(res);
        }

        XTrace.WriteLine($"续期用户绑定的设备：{modelAppUser.Devices}");

        var listProduct = Product.FindAll(Product._.EnableLimitTime == true).Select(e => e.Id);
        var listDeviceCode = Device.FindAll(Device._.ProductId.In(listProduct)).Select(e => e.Code).ToList();

        if (!modelAppUser.Devices.IsNullOrEmpty())
        {
            var devices = modelAppUser.Devices.Split(",").ToList();
            res.data = devices.Where(e=> listDeviceCode.Contains(e)).WhereIf(deviceName.IsNotNullOrWhiteSpace(),e => e.Contains(deviceName)).Select(e => new { name = e, value = e });
            res.success = true;
        }


        return Json(res);
    }

    /// <summary>
    /// 查询年限价格
    /// </summary>
    /// <returns></returns>
    public IActionResult QueryYearAmount()
    {
        var res = new DResult();

        var Content = _cookie.GetValue<String>($"{RedisSetting.Current.CacheKeyPrefix}App");
        if (Content.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("请从APP打开");
            return Json(res);
        }
        var strArray = Content.Split('|');
        if (strArray.Length != 3)
        {
            res.msg = GetResource("参数错误");
            return Json(res);
        }



        var AppType = strArray[0].ToInt();
        var AccessId = strArray[1];
        var Info = strArray[2];

        ProjectApp? model;
        var appSecret = String.Empty;
        if (AppType == 1)
        {
            model = ProjectApp.FindByAndroidAppKey(AccessId);
            appSecret = model?.AndroidAppSecret;
        }
        else
        {
            model = ProjectApp.FindByIosAppKey(AccessId);
            appSecret = model?.IosAppSecret;
        }
        if (model == null)
        {
            res.msg = GetResource("参数错误");
            return Json(res);
        }

        var key = DHLZW.GetKeyFromText(appSecret!);
        Info = Base64Helper.Base64ToString(Info); // 解密信息
        var Infos = Info.SplitAsInt(",");
        Info = DHLZW.Decompress([.. Infos], key); // 解压缩信息

        var dic = Info.SplitAsDictionary("=", ",");
        if (!dic.TryGetValue("IdentityId", out var IdentityId))
        {
            res.msg = GetResource("参数无效");
            return Json(res);
        }

        if (!dic.TryGetValue("AppTime", out var AppTime))
        {
            res.msg = GetResource("参数无效");
            return Json(res);
        }

        var modelAppUser = AppUser.FindByIdentityId(IdentityId);
        if (modelAppUser == null)
        {
            res.msg = GetResource("非法操作");
            return Json(res);
        }

        var list = RenewalYear.FindAll().Select(e => new { year = e.Year });
        res.data = list;
        return Json(res);
    }

    /// <summary>
    /// 查询续期支付记录列表
    /// </summary>
    /// <returns></returns>
    public IActionResult QueryRenewalPayList(Int32 page,Int32 limit)
    {
        var res = new DResult();

        var Content = _cookie.GetValue<String>($"{RedisSetting.Current.CacheKeyPrefix}App");
        if (Content.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("请从APP打开");
            return Json(res);
        }
        var strArray = Content.Split('|');
        if (strArray.Length != 3)
        {
            res.msg = GetResource("参数错误");
            return Json(res);
        }
        var AppType = strArray[0].ToInt();
        var AccessId = strArray[1];
        var Info = strArray[2];

        ProjectApp? model;
        var appSecret = String.Empty;
        if (AppType == 1)
        {
            model = ProjectApp.FindByAndroidAppKey(AccessId);
            appSecret = model?.AndroidAppSecret;
        }
        else
        {
            model = ProjectApp.FindByIosAppKey(AccessId);
            appSecret = model?.IosAppSecret;
        }
        if (model == null)
        {
            res.msg = GetResource("参数错误");
            return Json(res);
        }

        var key = DHLZW.GetKeyFromText(appSecret!);

        Info = Base64Helper.Base64ToString(Info); // 解密信息
        var Infos = Info.SplitAsInt(",");
        Info = DHLZW.Decompress([.. Infos], key); // 解压缩信息

        var dic = Info.SplitAsDictionary("=", ",");
        if (!dic.TryGetValue("IdentityId", out var IdentityId))
        {
            res.msg = GetResource("参数无效");
            return Json(res);
        }

        if (!dic.TryGetValue("AppTime", out var AppTime))
        {
            res.msg = GetResource("参数无效");
            return Json(res);
        }

        var modelAppUser = AppUser.FindByIdentityId(IdentityId);
        if (modelAppUser == null)
        {
            res.msg = GetResource("非法操作");
            return Json(res);
        }
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = RenewalPay._.CreateTime,
            Desc = true,
        };

        res.success = true;
        res.data = RenewalPay.FindAll(RenewalPay._.CreateUserID == modelAppUser.Id, pages).Select(e => new
        {
            Id = e.Id.SafeString(),
            e.CreateTime,
            e.PayWay,
            e.Amount,
            e.Devices,
            e.Status,
            e.RenewalYear,
        });
        return Json(res);
    }

    /// <summary>
    /// 支付回调页面
    /// </summary>
    /// <returns></returns>
    public IActionResult PayCallBack(Int64 Id,String out_trade_no)
    {
        if (out_trade_no.IsNotNullAndWhiteSpace())
        {
            Id = out_trade_no.ToLong();
        }

        var modal = RenewalPay.FindById(Id);

        if (modal?.PayWay == 1)
        {

            try
            {
                var response = PayHelper.AlipayTradeQuery(modal.Id.SafeString());

                XTrace.WriteLine($"查询{Id}支付结果：{response.ToJson()}");

                if (response.TradeStatus == "TRADE_SUCCESS")
                {
                    modal.Status = 1;
                    modal.TradeNo = response.TradeNo;
                    modal.PayTime = DateTime.Now;
                    modal.Update();
                    foreach (var item in modal.Devices.Split(","))
                    {
                        var d = Device.FindByCode(item);
                        if (d != null)
                        {
                            DeviceRenewalLogs modelDeviceRenewalLogs = new();
                            modelDeviceRenewalLogs.DeviceName = d.Code;
                            modelDeviceRenewalLogs.LastExpiredTime = d.ExpiredTime;
                            if (d.ExpiredTime == DateTime.MinValue)
                            {
                                d.ExpiredTime = DateTime.Now.AddYears(modal.RenewalYear);
                            }
                            else
                            {
                                d.ExpiredTime = d.ExpiredTime.AddYears(modal.RenewalYear);
                            }
                            modelDeviceRenewalLogs.ExpiredTime = d.ExpiredTime;
                            modelDeviceRenewalLogs.DType = 2;
                            modelDeviceRenewalLogs.Remark = response.TradeNo;
                            modelDeviceRenewalLogs.Insert();
                            d.Update();
                        }
                    }
                }
                else
                {
                    modal.Status = 2;
                    modal.Update();
                }
            }
            catch (Exception)
            {
                modal.Status = 2;
                modal.Update();
            }

        }
        if (modal?.PayWay == 2)
        {
            try
            {
                var result = PayHelper.WxGetPayTransaction(modal.Id.SafeString()).Result;
                XTrace.WriteLine($"查询微信支付订单{result.ToJson()}");
                if(result.TradeState == "SUCCESS")
                {
                    modal.Status = 1;
                    modal.TradeNo = result.TransactionId;
                    modal.PayTime = DateTime.Now;
                    modal.Update();
                    foreach (var item in modal.Devices.Split(","))
                    {
                        var d = Device.FindByCode(item);
                        if (d != null)
                        {
                            DeviceRenewalLogs modelDeviceRenewalLogs = new();
                            modelDeviceRenewalLogs.DeviceName = d.Code;
                            modelDeviceRenewalLogs.LastExpiredTime = d.ExpiredTime;
                            if (d.ExpiredTime == DateTime.MinValue)
                            {
                                d.ExpiredTime = DateTime.Now.AddYears(modal.RenewalYear);
                            }
                            else
                            {
                                d.ExpiredTime = d.ExpiredTime.AddYears(modal.RenewalYear);
                            }
                            modelDeviceRenewalLogs.ExpiredTime = d.ExpiredTime;
                            modelDeviceRenewalLogs.DType = 2;
                            modelDeviceRenewalLogs.Remark = result.TransactionId;
                            modelDeviceRenewalLogs.Insert();
                            d.Update();
                        }
                    }
                }
                else
                {
                    modal.Status = 2;
                    modal.Update();
                }
            }
            catch (Exception)
            {
                modal.Status = 2;
                modal.Update();
            }
        }

        
        return View(modal);
    }

    /// <summary>
    /// 支付成功页面
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    public IActionResult PaySuccess(Int64 Id)
    {
        var modal = RenewalPay.FindById(Id);
        return View(modal);
    }
    
    /// <summary>
    /// 取消支付
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [HttpPost]
    public IActionResult PayClose(Int64 Id)
    {
        var res = new DResult();

        var Content = _cookie.GetValue<String>($"{RedisSetting.Current.CacheKeyPrefix}App");
        if (Content.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("请从APP打开");
            return Json(res);
        }
        var strArray = Content.Split('|');
        if (strArray.Length != 3)
        {
            res.msg = GetResource("参数错误");
            return Json(res);
        }
        var AppType = strArray[0].ToInt();
        var AccessId = strArray[1];
        var Info = strArray[2];

        ProjectApp? model;
        var appSecret = String.Empty;
        if (AppType == 1)
        {
            model = ProjectApp.FindByAndroidAppKey(AccessId);
            appSecret = model?.AndroidAppSecret;
        }
        else
        {
            model = ProjectApp.FindByIosAppKey(AccessId);
            appSecret = model?.IosAppSecret;
        }
        if (model == null)
        {
            res.msg = GetResource("参数错误");
            return Json(res);
        }

        var key = DHLZW.GetKeyFromText(appSecret!);

        Info = Base64Helper.Base64ToString(Info); // 解密信息
        var Infos = Info.SplitAsInt(",");
        Info = DHLZW.Decompress([.. Infos], key); // 解压缩信息

        var dic = Info.SplitAsDictionary("=", ",");
        if (!dic.TryGetValue("IdentityId", out var IdentityId))
        {
            res.msg = GetResource("参数无效");
            return Json(res);
        }

        XTrace.WriteLine($"续期用户的IdentityId：{IdentityId}");

        if (!dic.TryGetValue("AppTime", out var AppTime))
        {
            res.msg = GetResource("参数无效");
            return Json(res);
        }

        var modelAppUser = AppUser.FindByIdentityId(IdentityId);
        if (modelAppUser == null)
        {
            res.msg = GetResource("非法操作");
            return Json(res);
        }

        XTrace.WriteLine($"续期用户绑定的设备：{modelAppUser.Devices}");

        var modelRenewalPay = RenewalPay.FindById(Id);
        if(modelRenewalPay == null)
        {
            res.msg = GetResource("支付记录不存在");
            return Json(res);
        }
        modelRenewalPay.Status = 3;
        modelRenewalPay.Update();
        res.success = true;
        res.msg = GetResource("取消成功");
        return Json(res);
    }
}
