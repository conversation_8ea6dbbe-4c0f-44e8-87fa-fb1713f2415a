﻿@{
    Html.AppendTitleParts(T("设备续期订单").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    .layui-layer-title {
        background-color: #fff;
    }

    .dtree-nav-item {
        padding-left: 0;
    }

    .layui-table-tool {
        z-index: 1;
    }
</style>

<form class="layui-form dg-form">
        <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width: auto">@T("设备")：</label>
            <div class="layui-input-inline">
                <input type="text" name="key" id="key" placeholder="@T("请输入设备编码")" autocomplete="off" class="layui-input" lay-filter="key">
            </div>
        </div>
        <div class="layui-inline select">
            <label class="layui-form-label" style="width: auto;margin-top:5px;">@T("支付状态")：</label>
            <div class="layui-input-inline select">
                <input type="hidden" id="status" name="status" />
                <div id="demo1" style=" width: 100%;"></div>
            </div>
        </div>
        <div class="layui-inline select">
            <label class="layui-form-label" style="width: auto;margin-top:5px;">@T("支付方式")：</label>
            <div class="layui-input-inline select">
                <input type="hidden" id="way" name="way" />
                <div id="demo2" style=" width: 100%;"></div>
            </div>
        </div>
    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
        <script type="text/html" id="tool">
@*             <a class="pear-btn pear-btn-xs" style="border:1px solid #36b368;background-color:#36b368;color:white;" lay-event="edit"> @T("编辑")</a>
            <a class="pear-btn pear-btn-danger pear-btn-xs" lay-event="del"> @T("删除")</a> *@
        </script>
    </div>
</div>
<script asp-location="Footer">
    $("body").on("click", function (event) {
        $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
        $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon', 'dtree'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;
        var dtree = layui.dtree;
        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetList")'
            , page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 80
            , cols: [[
                { field: 'Devices', title: '@T("设备")', width: 540 }
                , { field: 'RenewalYear', title: '@T("购买年限")', width: 120 }
                , { field: 'Amount', title: '@T("价格")', width: 120 }
                , { field: 'PayWay', title: '@T("支付方式")', width: 120,templet:function(d){
                    if(d.PayWay == 1){
                        return '@T("支付宝")'
                    }else{
                        return '@T("微信")'
                    }
                } }
                , { field: 'Status', title: '@T("状态")', minWidth: 120,templet:function(d){
                    if(d.Status == 0){
                        return '@T("待支付")'
                    }else if(d.Status == 1){
                        return '@T("支付成功")'
                    }else if(d.Status == 3){
                        return '@T("已取消")'
                    }else{
                        return '@T("支付失败")'
                    }
                } }
                , { field: 'CreateTime', title: '@T("创建时间")', minWidth: 160 }
                , { field: 'PayTime', title: '@T("支付时间")', minWidth: 160 }
                // , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: 160 }
            ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-100'
            , id: 'tables'
        });
                // 静态搜索
        xmSelect.render({
            el: '#demo2',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            clickClose: true,
            multiple:false, // 开启多选功能
            data:[{name:'@T("全部")',value:-1},{name:'@T("支付宝")',value:1},{name:'@T("微信")',value:2}],
            on: function (data) {  // 监听选择
                // console.log(data);
                // 手动处理单选
                if (data.arr.length>1) {
                    data.arr.splice(0,1)
                }
                if (data.arr.length > 0) {
                    var a = "";
                    for (var i = 0; i < data.arr.length; i++) {
                        if (i == 0) {
                            a = data.arr[i].value;
                        }
                        else {
                            a += "," + data.arr[i].value;
                        }
                    }
                    $("[name=way]").val(a);
                }
                else {
                    $("[name=way]").val("");
                }

                if (data.change.length > 0) {
                    active.reload();
                }
            }
        });
                xmSelect.render({
            el: '#demo1',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            clickClose: true,
            multiple:false, // 开启多选功能
            data:[{name:'@T("全部")',value:-1},{name:'@T("待支付")',value:0},{name:'@T("支付成功")',value:1},{name:'@T("支付失败")',value:2},{name:'@T("已取消")',value:3}],
            on: function (data) {  // 监听选择
                // console.log(data);
                // 手动处理单选
                if (data.arr.length>1) {
                    data.arr.splice(0,1)
                }
                if (data.arr.length > 0) {
                    var a = "";
                    for (var i = 0; i < data.arr.length; i++) {
                        if (i == 0) {
                            a = data.arr[i].value;
                        }
                        else {
                            a += "," + data.arr[i].value;
                        }
                    }
                    $("[name=status]").val(a);
                }
                else {
                    $("[name=status]").val("");
                }

                if (data.change.length > 0) {
                    active.reload();
                }
            }
        });
        window.active = {
            reload: function () {
                table.reload('tables',
                    {
                        where: {
                            key: $("#key").val(),
                            status: $("#status").val(),
                            way: $("#way").val(),
                        },
                        page: {
                            curr: 1
                        }
                    });
            },
            reload_Init: () => {
                table.reload('tables', {
                    where: {}
                })
            }
        }

        $("#key").on("input", function (e) {
            active.reload();
        });

        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            // console.log(obj);
            if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("Delete")', { Id: data.Id }, function (data) {
                        if (data.success) {
                            abp.notify.success(data.msg);
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            } else if (obj.event === 'edit') {
                window.edit(data);
            }
        });

        //监听开关事件
        form.on('switch(statusedit)', function (data) {
            //开关是否开启，true或者false
            var checked = data.elem.checked;
            //获取所属属性值
            var id = data.elem.attributes['data-id'].nodeValue;
            $.post('@Url.Action("ModifyState")', { id: id, status: checked }, function (res) {
                if (res.success) {
                    abp.notify.success(res.msg);
                }
                else {
                    abp.notify.error(res.msg);
                }
            });
        });
        window.add = function () {
            top.layui.dg.popupRight({
                id: 'Add'
                , title: ' @T("新增")'
                , closeBtn: 1
                , area: ['580px']
                , success: function () {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Add")" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });

        }

        window.edit = function (data) {
            top.layui.dg.popupRight({
                id: 'Edit'
                , title: ' @T("编辑")'
                , closeBtn: 1
                , area: ['580px']
                , success: function () {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Edit")' + abp.utils.formatString("?Id={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }

        window.warning = function (msg) {
            os.warning(msg);
        }

        window.layerClose = function () {

        }
    });
</script>

<script type="text/html" id="tool">
@*     @if (this.Has((PermissionFlags)4))
    {
                    <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="edit"> @T("管理")</a>
    } *@
</script>

<script type="text/html" id="online">
    {{# if(d.Online) { }}
      <i class="layui-icon layui-icon-ok" style="color: #16b777"></i>
    {{# } else { }}
      <i class="layui-icon layui-icon-close" style="color: #ff5722"></i>
    {{# } }}
</script>

<script type="text/html" id="user-toolbar">
@*     <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon  layui-icon-add-1"></i>
        @T("新增")
    </button> *@
</script>