﻿@model DeviceProperty
@{
    Html.AppendTitleParts(T("设备属性设置").Text);
}
<style>
    .layui-input-block {
        margin-right: 40px;
    }

    .layui-form-switch {
        margin-left: -10px !important;
        margin-top: 4px;
        width: 80px;
        height: 30px;
        margin-left: 10px;
        font-size: 18px;
    }

        .layui-form-switch > i {
            margin-top: 2px;
            width: 18px;
            height: 18px;
        }

        .layui-form-switch > div {
            width: 50px;
            height: 30px;
            text-align: left !important;
            text-indent: 7px;
            font-size: 16px;
            line-height: 30px;
        }
</style>
<form class="layui-form" lay-filter="organization-form" style="padding: 10px 0 0 0;">
    <div class="layui-form-item" style="height: 100px;">
        @if (Model.Type == "bool")  // 布尔型
        {

            <div style="display: flex;width: 100%;padding: 10px 0px;justify-content: center;">
                <div class="layui-form-label" style="width: fit-content;padding: 9px 10px 9px 0px;margin-right: 10px;">@Model.NickName</div>
                <div>
                    <input type="checkbox" name="checked" data-id="@Model.Id" id="switch" class="switch"
                    lay-skin="switch" lay-text="ON|OFF" lay-filter="statusedit" @(Model.Value.ToDGBool() ? Html.Raw("checked") : "")>
                </div>
            </div>

        }
        else
        {
            <div style="display: flex;width: 100%;padding: 10px 0px;justify-content: center;">
                <div class="layui-form-label" style="width: fit-content;margin-left: -10px;">@Model.NickName</div>
                <div>
                    <input type="text" name="Value" lay-verify="required" lay-verType="tips" placeholder="@T("请输入值")" autocomplete="off" class="layui-input" value="">
                </div>
            </div>
        }
    </div>

    <div class="layui-form-item layui-hide">
        <div class="layui-input-block">
            <button class="layui-btn layui-hide" lay-submit lay-filter="organization-submit" id="organization-submit">@T("提交")</button>
        </div>
    </div>
</form>
<script asp-location="Footer">
    layui.use(['abp', 'form'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        window.submitForm = function () {
            $("#organization-submit").click();
        }

        form.on('submit(organization-submit)', function (data) {
            var field = data.field.Value;
            var checked = data.field.checked
            let Value = field;
            if ("@Model.Type" == "bool") {
                checked == 'on' ? Value = true : Value = false
            }else{
                Value = field
            }
            var waitIndex = parent.layer.load(2);
            abp.ajax({
                url: "@Url.Action("PropertiesSetting")",
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                data: {
                    Id: '@Model.Id',
                    Value: Value
                },
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }

                data.index = index;

                parent.saveCallback(data);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

    });
</script>