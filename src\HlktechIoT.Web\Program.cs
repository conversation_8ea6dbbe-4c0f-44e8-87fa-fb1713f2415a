using DG.SafeOrbit.Library;
using DG.SafeOrbit.Memory;
using DG.Web.Framework;

using DH;
using DH.Entity;

using NewLife;
using NewLife.Agent;
using NewLife.Log;
using NewLife.Web;

using Pek.Configs;
using Pek.Helpers;

using Stardust;

namespace HlktechIoT;

/// <summary>
/// 应用程序
/// </summary>
public class Program {
    public static void Main(string[] args)
    {
        ApplicationHelper.SetEnvironment(args);

        if (DHSetting.Current.Debug)
        {
            XTrace.UseConsole(); // 日志输出到控制台，并拦截全局异常
        }

        XCode.Cache.CacheBase.Debug = false;  // 用于调试缓存问题

        SafeOrbitCore.Current.StartEarlyAsync();
        SafeOrbitCore.Current.Factory.SetProtectionMode(SafeContainerProtectionMode.FullProtection);  // 如果是云端系统，建议此处改为SafeContainerProtectionMode.NoProtection

        //if (Runtime.Windows)
        //{
        //    if (!DG.Setting.Current.IsRunService)
        //    {
        //        Run(args);
        //    }
        //    else
        //    {
        //        new DHRun(args).Main(args);
        //    }
        //}
        //else
        //{
        //    new DHRun(args).Main(args);
        //}

        Run(args);
    }

    public static IHostBuilder CreateHostBuilder(string[] args) =>
         CubeService.CreateHostBuilder<Startup>(args);

    public static void Run(string[] args)
    {
        var set = DG.Setting.Current;

        // 版本号管理
        DHSetting.Current.CurrentVersion = "0.2.0";
        //DHSetting.Current.UniversalCaptchaEndTime = DateTime.Now.AddDays(7);
        DHSetting.Current.TokenCookie = true;
        DHSetting.Current.Save();

        PekSysSetting.Current.ExcludeUrl = "SendMail/Send,/otaserver/,/Common/GetProfilePicture,/devicecommon/otaupgrade";
        PekSysSetting.Current.Save();

        RedisSetting.Current.IsUseRedisCache = true;
        RedisSetting.Current.Save();

        if (!set.HasInstalled)
        {
            set.HasInstalled = true;
            set.IsOnlyManager = true;
#if DEBUG
            set.Urls = "http://localhost:9142;https://localhost:9143;http://*:9142;https://*:9143"; // 如果有使用ListenAnyIP监听时建议此处设为空。
#else
            set.Urls = "http://*:9142;https://*:9143"; // 如果有使用ListenAnyIP监听时建议此处设为空。
#endif
            DHSetting.Current.CurDomainUrl = "http://localhost:9142";
            set.IsAlertOrCheckCode = 2;

            set.IsAllowSignalR = true;
            set.CORSUrl = "http://localhost:9142,https://localhost:9143";
            set.CorrelationClientName = "HlktechIoTWeb";

            set.CaptChaUrl = "/CaptCha/GetCheckCode";
            set.LoginUrl = "~/UserLogin";
            set.LogoutAll = false;

            set.AllowManageFindPassword = true;  // 允许后台找回密码

            set.Save();

            if (!DHSetting.Current.IsInstalled)
            {
                DHSetting.Current.SessionTimeout = 7200;
                DHSetting.Current.AdminArea = "Biz";
                DHSetting.Current.Save();

                RedisSetting.Current.RedisEnabled = true;
                RedisSetting.Current.CacheKeyPrefix = set.CorrelationClientName;
                RedisSetting.Current.RedisDatabaseId = 3;
                RedisSetting.Current.Save();

                var siteInfo = SiteInfo.FindDefault();
                if (siteInfo.Url.IsNullOrWhiteSpace())
                {
                    siteInfo.Url = $"{DHSetting.Current.CurDomainUrl}/";
                    siteInfo.Save();
                }
            }
            //else
            //{
            //    UtilSetting.Current.RedisConnectionString = DHUtilSetting.Current.RedisConnectionString;
            //    UtilSetting.Current.RedisPassWord = DHUtilSetting.Current.RedisPassWord;
            //    UtilSetting.Current.RedisEnabled = DHUtilSetting.Current.RedisEnabled;
            //    UtilSetting.Current.CacheKeyPrefix = set.CorrelationClientName;
            //    UtilSetting.Current.RedisDatabaseId = DHUtilSetting.Current.RedisDatabaseId;
            //    UtilSetting.Current.Save();
            //}

            ThreadPool.QueueUserWorkItem(s =>
            {
                var wc = new WebClientX()
                {
                    Log = XTrace.Log
                };

                var content = "Data".AsDirectory();

                wc.DownloadLinkAndExtract("http://x.chuangchu.net/", "3-Regions-20200622.zip", content.FullName, true);
                wc.DownloadLinkAndExtract("http://x.chuangchu.net/", "ip2region-20230908.zip", content.FullName, true);
                wc.DownloadLinkAndExtract("http://x.chuangchu.net/", "GeoLite2-ASN-20210320.zip", content.FullName, true);
                wc.DownloadLinkAndExtract("http://x.chuangchu.net/", "GeoLite2-City-20210320.zip", content.FullName, true);
            });
        }

        //RedisSetting.Current.RedisEnabled = DHUtilSetting.Current.RedisEnabled;
        //RedisSetting.Current.CacheKeyPrefix = DHUtilSetting.Current.CacheKeyPrefix;
        //RedisSetting.Current.Save();

#if !DEBUG
        if (StarSetting.Current.Server.IsNullOrWhiteSpace() || StarSetting.Current.AppKey.IsNullOrWhiteSpace())
        {
            //#if DEBUG
            //                StarSetting.Current.Server = "http://star.chuangchu.net:6600";
            //#else
            //            StarSetting.Current.Server = "http://***********:6600";
            //#endif

            StarSetting.Current.Server = "http://star.chuangchu.net:6600";
            StarSetting.Current.AppKey = set.CorrelationClientName;
            StarSetting.Current.Save();
        }
#endif

        CreateHostBuilder(args).Build().Run();
    }

}

/// <summary>服务类。名字可以自定义</summary>
class DHRun : ServiceBase {
    private String[] _args { get; set; }

    public DHRun(String[] args)
    {
        ServiceName = "HlktechIoTWeb";
        DisplayName = "海凌科IOT云系统Web服务端";
        Description = "深圳市海凌科电子有限公司Web服务端，用于IOT云系统";

        _args = args;
    }

    public override void StartWork(string reason)
    {
        Program.Run(_args);
        base.StartWork(reason);
    }

    /// <summary>停止服务</summary>
    /// <param name="reason"></param>
    public override void StopWork(string reason)
    {
        base.StopWork(reason);
    }
}