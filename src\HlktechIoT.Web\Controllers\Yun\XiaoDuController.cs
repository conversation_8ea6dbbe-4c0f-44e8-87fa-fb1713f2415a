﻿using DG.Web.Framework;

using DH.Core.Infrastructure;
using DH.Permissions.Identity.JwtBearer;

using HlktechIoT.Common;
using HlktechIoT.Data;
using HlktechIoT.Services.OAuth;
using HlktechIoT.Sign;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Caching;
using NewLife.Log;

using Pek;
using Pek.Helpers;
using Pek.Models;
using Pek.MVC;
using Pek.Security;
using Pek.Swagger;

using System.Security.Claims;

using XCode.Membership;

namespace HlktechIoT.Controllers.Yun;

/// <summary>
/// 小度云云对接接口
/// </summary>
[Produces("application/json")]
[CustomRoute(ApiVersions.V1)]
[Authorize("jwt")]
public class XiaoDuController : ApiControllerBaseX {
    private readonly IManageProvider _provider;
    private readonly IOAuthCookieService _oauthCookieService;
    private readonly Int32 ProjectAppId = 1;

    /// <summary>
    /// Jwt令牌构建器
    /// </summary>
    private IJsonWebTokenBuilder TokenBuilder { get; }

    /// <summary>
    /// Jwt令牌存储器
    /// </summary>
    private IJsonWebTokenStore TokenStore { get; }

    /// <summary>
    /// 令牌Payload存储器
    /// </summary>
    private ITokenPayloadStore PayloadStore { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="provider">管理提供者</param>
    /// <param name="oauthCookieService">OAuth Cookie服务</param>
    /// <param name="tokenBuilder">Jwt令牌构建器</param>
    /// <param name="tokenStore">Jwt令牌存储器</param>
    /// <param name="payloadStore">令牌Payload存储器</param>
    public XiaoDuController(IJsonWebTokenBuilder tokenBuilder, IJsonWebTokenStore tokenStore, ITokenPayloadStore payloadStore, IManageProvider provider, IOAuthCookieService oauthCookieService)
    {
        TokenBuilder = tokenBuilder;
        TokenStore = tokenStore;
        PayloadStore = payloadStore;
        _provider = provider;
        _oauthCookieService = oauthCookieService;
    }

    /// <summary>
    /// OAuth授权端点
    /// </summary>
    /// <param name="client_id">客户端ID</param>
    /// <param name="redirect_uri">回调地址</param>
    /// <param name="scope">权限范围</param>
    /// <param name="state">状态参数</param>
    /// <param name="response_type">响应类型，通常为code</param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpGet("Authorize")]
    public IActionResult Authorize(
        String client_id,
        String redirect_uri,
        String scope = "read",
        String state = "",
        String response_type = "code")
    {
        // 1. 验证OAuth参数
        if (client_id.IsNullOrWhiteSpace() || redirect_uri.IsNullOrWhiteSpace())
        {
            return BadRequest(new { error = "invalid_request", error_description = "missing required parameters" });
        }

        if (response_type != "code")
        {
            return BadRequest(new { error = "unsupported_response_type" });
        }

        // 2. 验证client_id和redirect_uri
        if (YunSettings.Current.BaiduClientId != client_id || YunSettings.Current.BaiduRedirectUri != redirect_uri.UrlDecode())
        {
            return BadRequest(new { error = "invalid_client", error_description = "client_id or redirect_uri is invalid" });
        }

        // 2.5. 验证scope参数
        if (!IsValidScopeForClient(client_id, scope))
        {
            return BadRequest(new { error = "invalid_scope", error_description = $"请求的权限范围 '{scope}' 无效或超出允许范围" });
        }

        // 3. 检查用户登录状态
        var user = _oauthCookieService.GetOAuthUser(HttpContext);
        if (user == null)
        {
            // 未登录，重定向到OAuth专用登录页
            var loginUrl = $"/oauth/login?client_id={client_id}&redirect_uri={Uri.EscapeDataString(redirect_uri)}&scope={scope}&state={state}&response_type={response_type}";

            // 添加防缓存头，确保每次都执行服务器端逻辑
            SetNoCacheHeaders();
            return Redirect(loginUrl);
        }

        // 4. 用户已登录，显示授权确认页
        var authorizeUrl = $"/oauth/consent?client_id={client_id}&redirect_uri={Uri.EscapeDataString(redirect_uri)}&scope={scope}&state={state}&response_type={response_type}";

        // 添加防缓存头，确保每次都执行服务器端逻辑
        SetNoCacheHeaders();
        return Redirect(authorizeUrl);
    }

    /// <summary>
    /// Token - 用授权码换取访问令牌
    /// </summary>
    /// <param name="grant_type">授权类型，必须是authorization_code</param>
    /// <param name="code">授权码</param>
    /// <param name="redirect_uri">回调地址，必须与授权时一致</param>
    /// <param name="client_id">客户端ID</param>
    /// <param name="client_secret">客户端密钥</param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("Token")]
    public IActionResult Token(
        string grant_type,
        string code,
        string redirect_uri,
        string client_id,
        string client_secret)
    {
        // 1. 验证grant_type
        if (grant_type != "authorization_code")
        {
            return BadRequest(new { error = "unsupported_grant_type" });
        }

        // 2. 验证必需参数
        if (code.IsNullOrEmpty() || client_id.IsNullOrEmpty() || redirect_uri.IsNullOrEmpty())
        {
            return BadRequest(new { error = "invalid_request", error_description = "缺少必需参数" });
        }

        // 3. 验证授权码并获取scope信息
        var authCodeValidation = ValidateAuthorizationCodeWithScope(code, client_id, redirect_uri);
        if (authCodeValidation.UserId <= 0)
        {
            return BadRequest(new { error = "invalid_grant", error_description = "授权码无效或已过期" });
        }

        // 4. 验证客户端密钥
        if (!ValidateClientSecret(client_id, client_secret))
        {
            return BadRequest(new { error = "invalid_client", error_description = "客户端认证失败" });
        }

        // 5. 生成访问令牌，包含授权的scope信息
        var payload = new Dictionary<string, string>
        {
            ["clientId"] = YunSettings.Current.BaiduClientId,
            [ClaimTypes.Sid] = authCodeValidation.UserId.SafeString(),
            [ClaimTypes.GroupSid] = ProjectAppId.SafeString(),
            ["From"] = "AppOauth",
            ["scope"] = authCodeValidation.Scope ?? "read", // 将OAuth scope添加到JWT payload
        };
        var resultToken = TokenBuilder.Create(payload);  //获取Token
        var accessToken = resultToken.AccessToken; // 访问令牌
        var refreshToken = resultToken.RefreshToken; // 刷新令牌        

        // 7. 返回令牌，包含实际授权的scope
        return Ok(new
        {
            access_token = accessToken,
            token_type = "Bearer",
            expires_in = 7200, // 2小时
            refresh_token = refreshToken,
            scope = authCodeValidation.Scope
        });
    }

    /// <summary>
    /// 受保护的资源接口 - 需要有效的访问令牌
    /// </summary>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpGet("Resource")]
    [HttpPost("Resource")]
    public IActionResult Resource()
    {
        // 1. 从请求头获取访问令牌
        var authHeader = Request.Headers["Authorization"].FirstOrDefault();
        if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
        {
            return Unauthorized(new { error = "invalid_token", error_description = "缺少访问令牌" });
        }

        // 2. 验证访问令牌并获取授权scope
        var accessToken = authHeader.Substring("Bearer ".Length);
        var tokenValidation = ValidateAccessToken(accessToken);
        if (tokenValidation.UserId <= 0)
        {
            return Unauthorized(new { error = "invalid_token", error_description = "访问令牌无效或已过期" });
        }

        // 3. 基于scope进行访问控制
        var authorizedScope = tokenValidation.Scope;
        var scopeArray = authorizedScope?.Split(' ', StringSplitOptions.RemoveEmptyEntries) ?? new[] { "read" };

        // 检查是否有读取权限
        if (!scopeArray.Any(s => s.Equals("read", StringComparison.OrdinalIgnoreCase) ||
                                s.Equals("all", StringComparison.OrdinalIgnoreCase)))
        {
            return StatusCode(403, new
            {
                error = "insufficient_scope",
                error_description = "访问令牌缺少读取权限",
                required_scope = "read",
                provided_scope = authorizedScope
            });
        }

        // 4. 根据用户ID获取用户信息
        // TODO: 从数据库获取用户详细信息        
        // 5. 根据授权scope返回相应的资源数据
        var responseData = new
        {
            user_id = tokenValidation.UserId,
            username = "测试用户", // 这里应该从数据库获取真实用户名
            scope = authorizedScope,
            message = "成功访问受保护资源",
            timestamp = DateTime.Now
        };

        // 6. 如果有设备权限，添加设备信息
        if (scopeArray.Any(s => s.Equals("device", StringComparison.OrdinalIgnoreCase) ||
                               s.Equals("all", StringComparison.OrdinalIgnoreCase)))
        {
            // TODO: 添加设备相关数据
            return Ok(new
            {
                user_id = tokenValidation.UserId,
                username = "测试用户",
                scope = authorizedScope,
                message = "成功访问受保护资源",
                timestamp = DateTime.Now,
                devices = new[] { "设备1", "设备2" } // 示例设备数据
            });
        }

        return Ok(responseData);
    }

    /// <summary>
    /// OAuth专用登录页面
    /// </summary>
    /// <param name="client_id">客户端ID</param>
    /// <param name="redirect_uri">回调地址</param>
    /// <param name="scope">权限范围</param>
    /// <param name="state">状态参数</param>
    /// <param name="response_type">响应类型</param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpGet("/oauth/login")]
    public IActionResult OAuthLogin(
        string client_id,
        string redirect_uri,
        string scope = "read",
        string state = "",
        string response_type = "code")
    {
        ViewBag.ClientId = client_id;
        ViewBag.RedirectUri = redirect_uri;
        ViewBag.Scope = scope;
        ViewBag.State = state;
        ViewBag.ResponseType = response_type;

        return View();
    }

    /// <summary>
    /// OAuth登录提交处理
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="password">密码</param>
    /// <param name="client_id">客户端ID</param>
    /// <param name="redirect_uri">回调地址</param>
    /// <param name="scope">权限范围</param>
    /// <param name="state">状态参数</param>
    /// <param name="response_type">响应类型</param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("/oauth/login")]
    public IActionResult OAuthLoginSubmit(
        [FromForm] String username,
        [FromForm] String password,
        [FromForm] String client_id,
        [FromForm] String redirect_uri,
        [FromForm] String scope = "read",
        [FromForm] String state = "",
        [FromForm] String response_type = "code")
    {
        try
        {
            // 验证用户名密码
            var user = AppUser.Login(username, password, ProjectAppId);
            if (user == null)
            {
                ViewBag.ErrorMessage = "用户名或密码错误";
                ViewBag.ClientId = client_id;
                ViewBag.RedirectUri = redirect_uri;
                ViewBag.Scope = scope;
                ViewBag.State = state;
                ViewBag.ResponseType = response_type;
                return View("OAuthLogin");
            }

            var ExpireTime = 30; // 设置过期时间
            var model = new OAuthUserInfo
            {
                UserId = user.Id,
                UserName = user.Name,
                Email = user.Mail ?? String.Empty,
            };

            _oauthCookieService.SetOAuthUser(HttpContext, model, ExpireTime);

            // 登录成功，重定向到授权确认页
            var consentUrl = $"/oauth/consent?client_id={client_id}&redirect_uri={Uri.EscapeDataString(redirect_uri)}&scope={scope}&state={state}&response_type={response_type}";

            // 添加防缓存头，确保每次都执行服务器端逻辑
            SetNoCacheHeaders();
            return Redirect(consentUrl);
        }
        catch (Exception ex)
        {
            ViewBag.ErrorMessage = "登录失败：" + ex.Message;
            ViewBag.ClientId = client_id;
            ViewBag.RedirectUri = redirect_uri;
            ViewBag.Scope = scope;
            ViewBag.State = state;
            ViewBag.ResponseType = response_type;
            return View("OAuthLogin");
        }
    }

    /// <summary>
    /// OAuth授权确认页面
    /// </summary>
    /// <param name="client_id">客户端ID</param>
    /// <param name="redirect_uri">回调地址</param>
    /// <param name="scope">权限范围</param>
    /// <param name="state">状态参数</param>
    /// <param name="response_type">响应类型</param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpGet("/oauth/consent")]
    public IActionResult OAuthConsent(
        String client_id,
        String redirect_uri,
        String scope = "read",
        String state = "",
        String response_type = "code")
    {

        // 检查用户是否已登录
        var user = _oauthCookieService.GetOAuthUser(HttpContext);
        if (user == null)
        {
            // 未登录，重定向回登录页
            var loginUrl = $"/oauth/login?client_id={client_id}&redirect_uri={Uri.EscapeDataString(redirect_uri)}&scope={scope}&state={state}&response_type={response_type}";

            // 添加防缓存头，确保每次都执行服务器端逻辑
            SetNoCacheHeaders();
            return Redirect(loginUrl);
        }

        // 再次验证scope参数（确保不会显示无效权限的确认页）
        if (!IsValidScopeForClient(client_id, scope))
        {
            return BadRequest(new { error = "invalid_scope", error_description = $"请求的权限范围 '{scope}' 无效或超出允许范围" });
        }

        // 获取客户端配置信息
        var clientConfig = GetClientScopeConfiguration(client_id);
        var appName = clientConfig?.AppName ?? "未知应用";

        // 获取动态权限描述
        var scopeDescriptions = GetScopeDescriptions(scope);

        // 传递数据给视图
        ViewBag.AppName = appName;
        ViewBag.ClientId = client_id;
        ViewBag.RedirectUri = redirect_uri;
        ViewBag.Scope = scope;
        ViewBag.ScopeDescriptions = scopeDescriptions; // 新增：动态权限描述
        ViewBag.State = state;
        ViewBag.ResponseType = response_type;
        ViewBag.Username = user.UserName;

        XTrace.WriteLine($"OAuth授权确认页面：用户 {user.UserName} 正在确认客户端 {client_id}({appName}) 的权限请求：{scope}");

        return View();
    }

    /// <summary>
    /// OAuth授权确认提交处理
    /// </summary>
    /// <param name="client_id">客户端ID</param>
    /// <param name="redirect_uri">回调地址</param>
    /// <param name="scope">权限范围</param>
    /// <param name="state">状态参数</param>
    /// <param name="authorize">用户是否同意授权</param>
    /// <returns></returns>    
    [AllowAnonymous]
    [HttpPost("/oauth/consent")]
    public IActionResult OAuthConsentSubmit(
        [FromForm] String client_id,
        [FromForm] String redirect_uri,
        [FromForm] String scope = "read",
        [FromForm] String state = "",
        [FromForm] String authorize = "")
    {
        // 检查用户是否已登录
        var user = _oauthCookieService.GetOAuthUser(HttpContext);
        if (user == null)
        {
            XTrace.WriteLine("OAuthConsentSubmit - 用户未登录");
            return BadRequest(new { error = "access_denied", error_description = "用户未登录" });
        }
        // 用户拒绝授权
        if (authorize != "allow")
        {
            XTrace.WriteLine($"OAuthConsentSubmit - 用户拒绝授权或authorize参数无效: '{authorize}'");
            var errorDescription = Uri.EscapeDataString("用户拒绝授权");
            var errorUrl = $"{redirect_uri}?error=access_denied&error_description={errorDescription}&state={state}";

            // 添加防缓存头，确保每次都执行服务器端逻辑
            SetNoCacheHeaders();
            return Redirect(errorUrl);
        }

        // 用户同意授权，生成授权码
        XTrace.WriteLine($"OAuthConsentSubmit - 用户同意授权，准备生成授权码");
        var authorizationCode = GenerateAuthorizationCode(client_id, user.UserId, scope, redirect_uri, state);

        // 重定向回第三方应用
        var callbackUrl = $"{redirect_uri}?code={authorizationCode}&state={state}";
        XTrace.WriteLine($"要重定向的路径：{callbackUrl}");

        // 添加防缓存头，确保每次都执行服务器端逻辑
        SetNoCacheHeaders();
        return Redirect(callbackUrl);
    }

    /// <summary>
    /// 生成授权码
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="userId">用户ID</param>
    /// <param name="scope">权限范围</param>
    /// <param name="redirectUri">重定向URI</param>
    /// <param name="state">状态参数</param>
    /// <returns></returns>
    private String GenerateAuthorizationCode(String clientId, Int32 userId, String scope, String redirectUri, String state)
    {
        // 生成一个唯一的授权码
        var code = Guid.NewGuid().ToString("N");

        // 创建授权码对象
        var authCodeInfo = new OAuthAuthorizationCode
        {
            Code = code,
            ClientId = clientId,
            UserId = userId.ToString(),
            RedirectUri = redirectUri,
            Scope = scope,
            State = state,
            CreateTime = DateTime.UtcNow,
            ExpireTime = DateTime.UtcNow.AddMinutes(10), // 10分钟有效期
            IsUsed = false
        };

        // 存储授权码信息（使用code作为键，方便根据code查找）
        Cache.Default.Set($"oauth:code:{code}", authCodeInfo, 600); // 10分钟有效期

        // 同时存储一个反向索引（用于清理同一用户的旧授权码）
        var userCodeKey = $"oauth:user:{clientId}:{userId}:code";
        var existingCode = Cache.Default.Get<string>(userCodeKey);
        if (!existingCode.IsNullOrEmpty())
        {
            // 移除旧的授权码
            Cache.Default.Remove($"oauth:code:{existingCode}");
        }
        Cache.Default.Set(userCodeKey, code, 600); return code;
    }

    #region OAuth辅助方法

    /// <summary>
    /// 验证客户端的scope权限
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="requestedScope">请求的权限范围</param>
    /// <returns>是否有效</returns>
    private Boolean IsValidScopeForClient(String clientId, String requestedScope)
    {
        try
        {
            // TODO: 从数据库获取应用的允许权限范围
            // 这里使用临时的硬编码配置，实际应用中应该从数据库的OAuth应用表中获取
            var clientScopeConfig = GetClientScopeConfiguration(clientId);

            if (clientScopeConfig == null)
            {
                XTrace.WriteLine($"未找到客户端 {clientId} 的权限配置");
                return false;
            }

            // 使用OAuthApp.IsValidScope方法进行验证
            var oauthApp = new OAuthApp
            {
                ClientId = clientId,
                AllowedScopes = clientScopeConfig.Value.AllowedScopes
            };

            var isValid = oauthApp.IsValidScope(requestedScope);

            if (!isValid)
            {
                XTrace.WriteLine($"客户端 {clientId} 请求的权限 '{requestedScope}' 超出允许范围 '{clientScopeConfig.Value.AllowedScopes}'");
            }
            else
            {
                XTrace.WriteLine($"客户端 {clientId} 权限验证通过：'{requestedScope}'");
            }

            return isValid;
        }
        catch (Exception ex)
        {
            XTrace.WriteLine($"验证客户端权限时发生异常：{ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 获取客户端权限配置
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>权限配置</returns>
    private (String AllowedScopes, String AppName)? GetClientScopeConfiguration(String clientId)
    {
        // TODO: 从数据库获取真实的客户端配置
        // 这里是临时的硬编码配置，实际应用中应该查询OAuth应用管理表
        var clientConfigs = new Dictionary<String, (String AllowedScopes, String AppName)>
        {
            // 小度智能音箱配置
            [YunSettings.Current.BaiduClientId] = ("read,device,control,all", "小度智能音箱"),

            // 可以添加更多客户端配置
            //["test_client_1"] = ("read,device", "测试应用1"),
            //["test_client_2"] = ("read", "测试应用2"),
        };

        return clientConfigs.TryGetValue(clientId, out (string AllowedScopes, string AppName) value) ? value : null;
    }

    /// <summary>
    /// 解析并格式化scope权限描述
    /// </summary>
    /// <param name="scope">权限范围</param>
    /// <returns>权限描述列表</returns>
    private List<String> GetScopeDescriptions(String scope)
    {
        var descriptions = new List<String>();

        if (String.IsNullOrEmpty(scope))
            return descriptions;

        var scopes = scope.Split(' ', StringSplitOptions.RemoveEmptyEntries);

        foreach (var s in scopes)
        {
            var trimmedScope = s.Trim().ToLower();
            switch (trimmedScope)
            {
                case "read":
                    descriptions.Add("读取您的基本信息");
                    break;
                case "device":
                    descriptions.Add("访问您的设备数据");
                    break;
                case "control":
                    descriptions.Add("控制您的设备");
                    break;
                case "all":
                    descriptions.Add("完全访问您的账户（包括读取信息、设备访问和控制权限）");
                    break;
                default:
                    descriptions.Add($"未知权限：{s}");
                    break;
            }
        }

        return descriptions;
    }

    /// <summary>
    /// 验证授权码并返回用户ID和授权的scope
    /// </summary>
    /// <param name="code">授权码</param>
    /// <param name="clientId">客户端ID</param>
    /// <param name="redirectUri">重定向URI</param>
    /// <returns>包含用户ID和授权scope的验证结果</returns>
    private (Int32 UserId, String Scope) ValidateAuthorizationCodeWithScope(String code, String clientId, String redirectUri)
    {
        try
        {
            // 基本参数验证
            if (code.IsNullOrWhiteSpace() || code.Length < 10)
            {
                XTrace.WriteLine($"授权码验证失败：授权码格式无效 - {code}");
                return (0, string.Empty);
            }

            if (clientId.IsNullOrWhiteSpace())
            {
                XTrace.WriteLine($"授权码验证失败：客户端ID为空");
                return (0, string.Empty);
            }

            // 从缓存中获取授权码信息
            var cacheKey = $"oauth:code:{code}";
            var authCodeInfo = Cache.Default.Get<OAuthAuthorizationCode>(cacheKey);

            if (authCodeInfo == null)
            {
                XTrace.WriteLine($"OAuth授权码验证失败：未找到授权码记录 - {code}");
                return (0, string.Empty);
            }

            // 验证授权码是否有效（检查过期时间和使用状态）
            if (!authCodeInfo.IsValid())
            {
                XTrace.WriteLine($"OAuth授权码验证失败：授权码已过期或已使用 - {code}, 过期时间: {authCodeInfo.ExpireTime}, 已使用: {authCodeInfo.IsUsed}");

                // 清理无效的授权码
                Cache.Default.Remove(cacheKey);
                Cache.Default.Remove($"oauth:user:{authCodeInfo.ClientId}:{authCodeInfo.UserId}:code");

                return (0, string.Empty);
            }

            // 验证客户端ID是否匹配
            if (!authCodeInfo.ClientId.Equals(clientId, StringComparison.OrdinalIgnoreCase))
            {
                XTrace.WriteLine($"授权码验证失败：客户端ID不匹配 - 期望: {authCodeInfo.ClientId}, 实际: {clientId}");
                return (0, string.Empty);
            }

            // 验证重定向URI是否匹配
            if (!redirectUri.IsNullOrEmpty() &&
                !authCodeInfo.RedirectUri.Equals(redirectUri, StringComparison.OrdinalIgnoreCase))
            {
                XTrace.WriteLine($"授权码验证失败：重定向URI不匹配 - 期望: {authCodeInfo.RedirectUri}, 实际: {redirectUri}");
                return (0, string.Empty);
            }

            // 标记授权码为已使用
            authCodeInfo.IsUsed = true;
            Cache.Default.Set(cacheKey, authCodeInfo, 60); // 保留1分钟用于防重放攻击

            // 清理用户的授权码索引
            Cache.Default.Remove($"oauth:user:{authCodeInfo.ClientId}:{authCodeInfo.UserId}:code");

            // 解析并返回用户ID和scope
            if (int.TryParse(authCodeInfo.UserId, out var userId))
            {
                XTrace.WriteLine($"授权码验证成功：用户ID = {userId}, 客户端ID = {clientId}, 授权scope = {authCodeInfo.Scope}");
                return (userId, authCodeInfo.Scope ?? "read"); // 如果scope为空，默认返回read
            }

            XTrace.WriteLine($"授权码验证失败：用户ID格式无效 - {authCodeInfo.UserId}");
            return (0, string.Empty);
        }
        catch (Exception ex)
        {
            XTrace.WriteLine($"授权码验证异常：{ex.Message}");
            return (0, string.Empty);
        }
    }

    /// <summary>
    /// 验证客户端密钥
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="clientSecret">客户端密钥</param>
    /// <returns></returns>
    private bool ValidateClientSecret(string clientId, string clientSecret)
    {
        // 验证必要参数
        if (clientId.IsNullOrEmpty() || clientSecret.IsNullOrEmpty())
            return false;

        // 使用YunSettings中的配置进行验证
        try
        {
            // 百度小度客户端验证
            if (clientId == YunSettings.Current.BaiduClientId)
            {
                return clientSecret == YunSettings.Current.BaiduClientSecret;
            }

            // 这里可以扩展为查询OAuth应用表，验证client_id和client_secret是否匹配
            // var client = await _oAuthService.GetClientByIdAsync(clientId);
            // return client != null && client.ClientSecret == clientSecret;

            // 未知客户端ID
            return false;
        }
        catch (Exception ex)
        {
            XTrace.WriteLine($"验证客户端密钥时发生异常：{ex.Message}");
            // 记录异常信息
            // 验证过程中发生错误，返回失败
            return false;
        }
    }

    /// <summary>
    /// 验证访问令牌并返回包含scope的信息
    /// </summary>
    /// <param name="accessToken">访问令牌</param>
    /// <returns>包含用户ID和授权scope的验证结果</returns>
    private (int UserId, string Scope) ValidateAccessToken(string accessToken)
    {
        try
        {
            if (accessToken.IsNullOrEmpty())
            {
                return (0, string.Empty);
            }

            // 使用JWT基础设施验证令牌
            var isValid = TokenStore.IsValidAndExists(accessToken);
            if (!isValid)
            {
                XTrace.WriteLine("JWT令牌验证失败：令牌无效或已过期");
                return (0, String.Empty);
            }

            // 从JWT payload中获取用户信息和scope
            var payload = PayloadStore.Get(accessToken);
            if (payload == null)
            {
                XTrace.WriteLine("JWT令牌验证失败：无法获取payload");
                return (0, String.Empty);
            }

            // 验证这是一个OAuth令牌
            if (!payload.TryGetValue("From", out string? value1) || value1 != "AppOauth")
            {
                XTrace.WriteLine("JWT令牌验证失败：不是OAuth令牌");
                return (0, String.Empty);
            }

            // 提取用户ID
            if (!payload.TryGetValue(ClaimTypes.Sid, out string? value) || !Int32.TryParse(value, out var userId))
            {
                XTrace.WriteLine("JWT令牌验证失败：无效的用户ID");
                return (0, String.Empty);
            }

            // 提取scope信息
            var scope = payload.ContainsKey("scope") ? payload["scope"] : "read";

            XTrace.WriteLine($"JWT令牌验证成功：用户ID = {userId}, scope = {scope}");
            return (userId, scope);
        }
        catch (Exception ex)
        {
            XTrace.WriteLine($"访问令牌验证异常：{ex.Message}");
            return (0, string.Empty);
        }
    }

    /// <summary>
    /// 刷新令牌
    /// </summary> 
    /// <param name="RefreshToken">刷新令牌值</param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("RefreshToken")]
    [AppApiSign]
    public IActionResult RefreshToken([FromForm] String RefreshToken)
    {
        var result = new DGResult();
        var _cache = EngineContext.Current.Resolve<ICache>();
        var key = $"dh-{RefreshToken}";
        if (_cache.ContainsKey(key))
        {
            result.Code = StateCode.Ok;
            result.Data = new { Token = _cache.Get<JsonWebToken>(key) };
            return result;
        }

        var token = TokenBuilder.Refresh(RefreshToken, 10);
        _cache.Set(key, token, 10);

        result.Code = StateCode.Ok;
        result.Data = new { Token = token };
        return result;
    }

    #endregion

    #region 私有辅助方法

    /// <summary>
    /// 设置防缓存响应头
    /// 防止302重定向被浏览器缓存，确保每次请求都执行服务器端逻辑
    /// </summary>
    private void SetNoCacheHeaders()
    {
        Response.Headers["Cache-Control"] = "no-cache, no-store, must-revalidate";
        Response.Headers["Pragma"] = "no-cache";
        Response.Headers["Expires"] = "0";
        // 添加随机参数防止代理缓存
        Response.Headers["X-No-Cache"] = DateTime.UtcNow.Ticks.ToString();
    }

    #endregion
}
