﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechIoT.Entity;

/// <summary>续期支付表</summary>
[Serializable]
[DataObject]
[Description("续期支付表")]
[BindTable("DH_RenewalPay", Description = "续期支付表", ConnName = "DH", DbType = DatabaseType.None)]
public partial class RenewalPay : IRenewalPay, IEntity<IRenewalPay>
{
    #region 属性
    private Int64 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, false, false, 0)]
    [BindColumn("Id", "编号", "", DataScale = "time")]
    public Int64 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String? _TradeNo;
    /// <summary>支付订单号</summary>
    [DisplayName("支付订单号")]
    [Description("支付订单号")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("TradeNo", "支付订单号", "")]
    public String? TradeNo { get => _TradeNo; set { if (OnPropertyChanging("TradeNo", value)) { _TradeNo = value; OnPropertyChanged("TradeNo"); } } }

    private String _Devices = null!;
    /// <summary>设备DeviceName</summary>
    [DisplayName("设备DeviceName")]
    [Description("设备DeviceName")]
    [DataObjectField(false, false, false, 50)]
    [BindColumn("Devices", "设备DeviceName", "")]
    public String Devices { get => _Devices; set { if (OnPropertyChanging("Devices", value)) { _Devices = value; OnPropertyChanged("Devices"); } } }

    private Int32 _RenewalYear;
    /// <summary>续期年限</summary>
    [DisplayName("续期年限")]
    [Description("续期年限")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("RenewalYear", "续期年限", "")]
    public Int32 RenewalYear { get => _RenewalYear; set { if (OnPropertyChanging("RenewalYear", value)) { _RenewalYear = value; OnPropertyChanged("RenewalYear"); } } }

    private Decimal _Amount;
    /// <summary>金额</summary>
    [DisplayName("金额")]
    [Description("金额")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Amount", "金额", "")]
    public Decimal Amount { get => _Amount; set { if (OnPropertyChanging("Amount", value)) { _Amount = value; OnPropertyChanged("Amount"); } } }

    private Int32 _Status;
    /// <summary>状态(0待支付 1支付成功 2支付失败 3已取消)</summary>
    [DisplayName("状态(0待支付1支付成功2支付失败3已取消)")]
    [Description("状态(0待支付 1支付成功 2支付失败 3已取消)")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Status", "状态(0待支付 1支付成功 2支付失败 3已取消)", "")]
    public Int32 Status { get => _Status; set { if (OnPropertyChanging("Status", value)) { _Status = value; OnPropertyChanged("Status"); } } }

    private Int32 _PayWay;
    /// <summary>支付方式(1支付宝 2微信)</summary>
    [DisplayName("支付方式(1支付宝2微信)")]
    [Description("支付方式(1支付宝 2微信)")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("PayWay", "支付方式(1支付宝 2微信)", "")]
    public Int32 PayWay { get => _PayWay; set { if (OnPropertyChanging("PayWay", value)) { _PayWay = value; OnPropertyChanged("PayWay"); } } }

    private DateTime _PayTime;
    /// <summary>支付时间</summary>
    [DisplayName("支付时间")]
    [Description("支付时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("PayTime", "支付时间", "")]
    public DateTime PayTime { get => _PayTime; set { if (OnPropertyChanging("PayTime", value)) { _PayTime = value; OnPropertyChanged("PayTime"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IRenewalPay model)
    {
        Id = model.Id;
        TradeNo = model.TradeNo;
        Devices = model.Devices;
        RenewalYear = model.RenewalYear;
        Amount = model.Amount;
        Status = model.Status;
        PayWay = model.PayWay;
        PayTime = model.PayTime;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "TradeNo" => _TradeNo,
            "Devices" => _Devices,
            "RenewalYear" => _RenewalYear,
            "Amount" => _Amount,
            "Status" => _Status,
            "PayWay" => _PayWay,
            "PayTime" => _PayTime,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToLong(); break;
                case "TradeNo": _TradeNo = Convert.ToString(value); break;
                case "Devices": _Devices = Convert.ToString(value); break;
                case "RenewalYear": _RenewalYear = value.ToInt(); break;
                case "Amount": _Amount = Convert.ToDecimal(value); break;
                case "Status": _Status = value.ToInt(); break;
                case "PayWay": _PayWay = value.ToInt(); break;
                case "PayTime": _PayTime = value.ToDateTime(); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static RenewalPay? FindById(Int64 id)
    {
        if (id < 0) return null;

        return Find(_.Id == id);
    }
    #endregion

    #region 数据清理
    /// <summary>清理指定时间段内的数据</summary>
    /// <param name="start">开始时间。未指定时清理小于指定时间的所有数据</param>
    /// <param name="end">结束时间</param>
    /// <returns>清理行数</returns>
    public static Int32 DeleteWith(DateTime start, DateTime end)
    {
        return Delete(_.Id.Between(start, end, Meta.Factory.Snow));
    }
    #endregion

    #region 字段名
    /// <summary>取得续期支付表字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>支付订单号</summary>
        public static readonly Field TradeNo = FindByName("TradeNo");

        /// <summary>设备DeviceName</summary>
        public static readonly Field Devices = FindByName("Devices");

        /// <summary>续期年限</summary>
        public static readonly Field RenewalYear = FindByName("RenewalYear");

        /// <summary>金额</summary>
        public static readonly Field Amount = FindByName("Amount");

        /// <summary>状态(0待支付 1支付成功 2支付失败 3已取消)</summary>
        public static readonly Field Status = FindByName("Status");

        /// <summary>支付方式(1支付宝 2微信)</summary>
        public static readonly Field PayWay = FindByName("PayWay");

        /// <summary>支付时间</summary>
        public static readonly Field PayTime = FindByName("PayTime");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得续期支付表字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>支付订单号</summary>
        public const String TradeNo = "TradeNo";

        /// <summary>设备DeviceName</summary>
        public const String Devices = "Devices";

        /// <summary>续期年限</summary>
        public const String RenewalYear = "RenewalYear";

        /// <summary>金额</summary>
        public const String Amount = "Amount";

        /// <summary>状态(0待支付 1支付成功 2支付失败 3已取消)</summary>
        public const String Status = "Status";

        /// <summary>支付方式(1支付宝 2微信)</summary>
        public const String PayWay = "PayWay";

        /// <summary>支付时间</summary>
        public const String PayTime = "PayTime";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
