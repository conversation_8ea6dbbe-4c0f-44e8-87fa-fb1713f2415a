﻿using DG.Web.Framework;

using HlktechIoT.Data;
using HlktechIoT.Dto;

using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

using Microsoft.AspNetCore.Mvc;

using NewLife.Data;

using Pek.Models;

using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;

using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Device.Controllers;

/// <summary>模组固件升级日志</summary>
[DisplayName("模组固件升级日志")]
[Description("模组固件升级日志管理")]
[DeviceArea]
[DHMenu(70, ParentMenuName = "FileManag", CurrentMenuUrl = "~/{area}/UpgradeLog", CurrentMenuName = "UpgradeLog", LastUpdate = "20240604")]
public class UpgradeLogController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 70;

    /// <summary>
    /// 日志管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("日志管理")]
    public IActionResult Index()
    {
        ViewBag.UId = ManageProvider.User?.ID;
        return View();
    }

    /// <summary>
    /// 日志管理
    /// </summary>
    /// <returns></returns>
    [DisplayName("日志管理")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult GetList(String key, Int32 page = 1, Int32 limit = 10, Int16 Status = -1)
    {
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = Role._.ID,
            Desc = true
        };
        var lists = UpgradeLogs.Search(key, Status, pages).Select(x =>
        {
            return new
            {
                x.Id,
                x.FirmwareInfoId,
                x.FirmwareInfoOriginFileName,
                x.IotId,
                x.Remark,
                x.Status,
                x.LastSVersion,
                x.SVersions,
                x.CreateTime,
                x.CreateIP,
                x.UpdateUser,
                x.UpdateTime,
                x.UpdateIP
            };
        });

        return Json(new { code = 0, msg = "", count = pages.TotalCount, data = lists });
    }

    /// <summary>删除日志</summary>
    /// <returns></returns>
    [DisplayName("删除日志")]
    [EntityAuthorize(PermissionFlags.Delete)]
    public IActionResult Delete(Int32 Id)
    {
        var result = new DResult();
        if (Id <= 0)
        {
            result.msg = GetResource("数据有误");
            return Json(result);
        }

        UpgradeLogs.Delete(UpgradeLogs._.Id == Id);

        result.success = true;
        result.msg = GetResource("删除成功");
        return Json(result);
    }

    /// <summary>
    /// 导出
    /// </summary>
    /// <returns></returns>
    [DisplayName("导出")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public async Task<IActionResult> ExportDevices(String keys, Int32 status, String Ids)
    {
        //UpgradeLogs.Meta.Session.Dal.Db.ShowSQL = true;  // 输出sql语句

        IEnumerable<UpgradeLogs> things = UpgradeLogs.FindAllByIdsAndKeysAndStatus(Ids, keys, status);

        var list = things.Select(e =>
        {
            return new UpgradeLogExport
            {
                FirmwareInfoId = e.FirmwareInfoId,
                IotId = e.IotId,
                Status = e.Status,
                Remark = e.Remark,
                LastSVersion = e.LastSVersion,
                SVersions = e.SVersions,
                CreateUser = e.CreateUser,
                CreateTime = e.CreateTime,
                CreateIP = e.CreateIP,
                UpdateUser = e.UpdateUser,
                UpdateTime = e.UpdateTime,
                UpdateIP = e.UpdateIP
            };
        });

        IExporter exporter = new ExcelExporter();
        var result = await exporter.ExportAsByteArray(list.ToList()).ConfigureAwait(false);

        return File(result, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{GetResource("网关设备列表")} {DateTime.Now:yyyyMMddhhmm}.xlsx");
    }
    /// <summary>
    /// 明细
    /// </summary>
    /// <returns></returns>
    [DisplayName("查看明细")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult Detail(int lId)
    {
        dynamic viewModel = new ExpandoObject();
        viewModel.lId = lId;
        return View(viewModel);
    }
    [DisplayName("日志明细记录")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult GetUpgradeALogs(Int32 lId, Int32 page = 1, Int32 limit = 5)
    {
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = FirmwareInfo._.Id,
            Desc = true
        };

        var list = UpgradeALogs.FindAll(UpgradeALogs._.LId == lId, pages).OrderByDescending(order => order.CreateTime).Select(e =>
        {
            return new
            {
                e.Id,
                e.LId,
                e.IotId,
                e.Content,
                e.CreateTime,
            };
        });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data = list });
    }
}
