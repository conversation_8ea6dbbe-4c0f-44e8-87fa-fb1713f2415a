﻿@{
    Html.AppendTitleParts(T("修改密码").Text);
}
<style asp-location="true">
    @if (language.UniqueSeoCode == "en")
    {
        <text>
        .layui-form-label{
            width: 110px;
        }
        </text>
    }
</style>
<script src="~/js/crypto.js" asp-append-version="true"></script>
<div class="layui-card">
    <div class="layui-card-header">@T("修改密码")</div>
    <div class="layui-card-body">
        <form class="layui-form" action="javascript:void(0);">
            <div class="layui-form-item">
                <label class="layui-form-label">@T("当前密码")</label>
                <div class="layui-input-inline">
                    <input type="password" name="oldPassword" id="oldPassword" lay-verify="required" lay-verType="tips" lay-reqText="@T("必填项不能为空")" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("新密码")</label>
                <div class="layui-input-inline">
                    <input type="password" name="password" lay-verify="pass" lay-verType="tips" autocomplete="off" id="password" class="layui-input">
                </div>
                <div class="layui-form-mid layui-word-aux">@T("8到32个字符")</div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("确认新密码")</label>
                <div class="layui-input-inline">
                     <input type="password" name="repassword" id="repassword" lay-verify="repass" lay-verType="tips" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="pear-btn pear-btn-primary" lay-submit lay-filter="password_submit">@T("提交")</button>
                </div>
            </div>
        </form>
    </div>
</div>
<script asp-location="Footer">
        layui.use(['abp', 'form'], function () {
            var $ = layui.jquery;
            var abp = layui.abp;
            var form = layui.form;
            /** 计算hash  */
            const generateHash = function (str) {
                return CryptoJS.MD5(str).toString().toUpperCase();
            };
            form.on('submit(password_submit)', function (data) {
                data.field.oldPassword = generateHash(data.field.oldPassword)
                data.field.password = generateHash(data.field.password)
                data.field.repassword = generateHash(data.field.repassword)
                var waitIndex = parent.layer.load(2);
                abp.ajax({
                    url: "@Url.Action("UpdatePassword", "PassWord")",
                    contentType : "application/x-www-form-urlencoded; charset=utf-8",
                    data: data.field,
                    abpHandleError: false
                }).done(function (data) {
                    if (!data.success) {
                        abp.notify.error(data.msg);
                        return false;
                    }
                    abp.notify.success(data.msg);
                }).fail(function (jqXHR) {
                    parent.layer.msg(jqXHR.message, { icon: 5 });
                }).always(function () {
                    parent.layer.close(waitIndex);
                });

                return false;
            });
        });
</script>