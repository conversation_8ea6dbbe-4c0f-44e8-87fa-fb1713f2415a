﻿@{
    Html.AppendTitleParts(T("调试设备").Text);

    var modelDeviceProperty = Model.DeviceProperty as IList<DeviceProperty>;
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .layui-form-switch {
        margin-left: -10px !important;
        margin-top: 2px;
        width: 80px;
        height: 30px;
        margin-left: 10px;
        font-size: 18px;
    }

        .layui-form-switch > i {
            margin-top: 2px;
            width: 18px;
            height: 18px;
        }

        .layui-form-switch > div {
            width: 50px;
            height: 30px;
            text-align: left !important;
            text-indent: 7px;
            font-size: 16px;
            line-height: 30px;
        }
</style>
<style>
    ._form{
        width: 98.5%;
        margin-left: 1%;
    }
    ._box{
        width: 98%;
        height: 100%;
        margin: 0 auto;
        display:flex;
        justify-content:space-evenly;
        justify-items: center;
    }
    .label{
        line-height: 40px;
        padding: 5px 0px;
    }
    .name{
        width: fit-content;
        min-width: 170px;
        max-width: 300px;
        padding: 5px 0px;
        line-height: 40px;
        color: #3b3b3b;
    }
    ._input{
        width: 80%;
        padding: 5px;
    }
</style>
<form class="layui-form _form" lay-filter="organization-form" style="margin: -5px 0 0 0;">
        <input type="text" name="Id" value="@Model.Device.Id" hidden="hidden" />
    @foreach (var property in modelDeviceProperty)
    {
        <div class="_box">
            <div class="label"><input type="checkbox" name="[@property.Id].IfCheckd" id="<EMAIL>" class="layui-input checkbox" style="flex:1;" /></div>
            
            <div class="name">
                @property.Name
            </div>
            
            <div class="_input">
                 @if (property.Type == "bool")  // 布尔型
                {
                    <input type="checkbox" name="[@property.Id].Value" id="@property.Id" class="switch" lay-skin="switch" lay-text="ON|OFF" disabled="disabled">
                }
                else if (property.Type == "byte" || property.Type == "short" || property.Type == "int" || property.Type == "long")
                {
                    <input type="text" name="[@property.Id].Value" id="@property.Id" placeholder="@T("指令下发")" class="layui-input" disabled="disabled" style="flex:10;" onkeyup="temperatureInput(this)" />
                }
                else if (property.Type == "float" || property.Type == "double")
                {
                    <input type="text" name="[@property.Id].Value" id="@property.Id" placeholder="@T("指令下发")" class="layui-input" disabled="disabled" style="flex:10;" onkeyup="sanitizeInput(this)" />
                }
                else
                {
                    <input type="text" name="[@property.Id].Value" id="@property.Id" placeholder="@T("指令下发")" class="layui-input" disabled="disabled" style="flex:10;" />
                }
            </div>
        </div>
    }

    <div class="layui-form-item layui-hide">
        <div class="layui-input-block">
            <button class="layui-btn layui-hide" lay-submit lay-filter="organization-submit" id="organization-submit">@T("提交")</button>
        </div>
    </div>
</form>
<script asp-location="Footer">
    layui.use(['abp', 'form'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        window.submitForm = () => {
            $("#organization-submit").click();
        }
        form.on('checkbox', function (data) {
            var checkboxId = data.elem.id;
            var inputId = checkboxId.replace("checkbox_", "");
            var input = $("#" + inputId);
            if (data.elem.checked) {
                input.prop("disabled", false);
                if (input.prop("type")=="checkbox")
                {
                    input.next("div").removeClass("layui-checkbox-disabled layui-disabled");
                }
            } else {
                input.prop("disabled", true);
            }
        });
        form.on('submit(organization-submit)', (e) => {
            var data = e.field
            for (const key in data) {
                if (typeof data[key] === 'string' && data[key]) {
                    // 去除空格
                    data[key] = data[key].replace(/\s+/g,"")          
                    // console.log('每个',data[key]);
                }
            }
            var waitIndex = parent.layer.load(2);
            abp.ajax({
                method: 'POST',
                url: "@Url.Action("Transmissions")",
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                data: data,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                data.index = index;

                parent.saveCallback(data);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

    });
    function sanitizeInput(input) {
        input.value = input.value.replace(/[^-?\d.]/g, ''); // 只保留数字、正负号和小数点
        input.value = input.value.replace(/(\..*)\./g, '$1'); // 防止多个小数点
        input.value = input.value.replace(/(-.*)-/g, '$1'); // 防止多个负号
        //input.value = input.value.replace(/(-)?(\d+\.\d{2}).*/g, '$1$2'); // 限制为两位小数
    }

    function temperatureInput(input) {
        input.value = input.value.replace(/[^-?\d]/g, ''); // 只保留数字和负号
        input.value = input.value.replace(/(-.*)-/g, '$1'); // 防止多个负号
    }
</script>