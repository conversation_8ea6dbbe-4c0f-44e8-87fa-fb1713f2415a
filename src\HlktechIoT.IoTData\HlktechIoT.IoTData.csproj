﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFrameworks>net9.0</TargetFrameworks>
    <AssemblyTitle>物联网数据平台</AssemblyTitle>
    <Description>IoT数据平台</Description>
    <Company>深圳市海凌科电子有限公司</Company>
	<Authors>丁川</Authors>
    <Copyright>版权所有(C) 深圳市海凌科电子有限公司 2009-2024</Copyright>
    <VersionPrefix>0.1</VersionPrefix>
    <VersionSuffix>$([System.DateTime]::Now.ToString(`yyyy.MMdd`))</VersionSuffix>
    <Version>$(VersionPrefix).$(VersionSuffix)</Version>
    <FileVersion>$(Version)</FileVersion>
    <AssemblyVersion>$(VersionPrefix).*</AssemblyVersion>
    <Deterministic>false</Deterministic>
    <OutputPath>..\Bin\IoTData</OutputPath>
    <AppendTargetFrameworkToOutputPath>true</AppendTargetFrameworkToOutputPath>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <ImplicitUsings>enable</ImplicitUsings>
    <LangVersion>latest</LangVersion>
	  
	<NoWarn>$(NoWarn);1701;1702;1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="RpcControllers\**" />
    <Content Remove="RpcControllers\**" />
    <EmbeddedResource Remove="RpcControllers\**" />
    <None Remove="RpcControllers\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Common\ApiFilterAttribute.cs" />
    <Compile Remove="Common\ApiService.cs" />
    <Compile Remove="Common\ApiServiceInstance.cs" />
    <Compile Remove="Common\BaseController.cs" />
    <Compile Remove="Controllers\AppController.cs" />
	<Compile Remove="Controllers\DataPushSettingController.cs" />
    <Compile Remove="Controllers\DeviceChannelController.cs" />
    <Compile Remove="Controllers\DeviceOnlineController.cs" />
    <Compile Remove="Controllers\ProductFunctionsModelController.cs" />
    <Compile Remove="Controllers\ProductsModelController.cs" />
    <Compile Remove="Controllers\ThingModelController.cs" />
	<Compile Remove="Models\DataPushSettingSearchModel.cs" />
    <Compile Remove="Models\IdentifierModel.cs" />
    <Compile Remove="Models\IdModel.cs" />
    <Compile Remove="Models\IdsModel.cs" />
    <Compile Remove="Models\NameModel.cs" />
    <Compile Remove="Models\ResultContent.cs" />
    <Compile Remove="Models\ResultContents.cs" />
    <Compile Remove="Models\ThingModelModel.cs" />
    <Compile Remove="Services\AlarmConsumer.cs" />
    <Compile Remove="Services\RpcService.cs" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="..\HlktechIoT.IoTServer\Services\PushService.cs" Link="Services\PushService.cs" />
    <Compile Include="..\HlktechIoT.IoTService\Services\IPushService.cs" Link="Services\IPushService.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="DH.NMQTT" Version="4.12.2025.620-beta1203" />
    <PackageReference Include="DH.NRedis" Version="4.12.2025.619-beta1116" />
    <PackageReference Include="DH.NStardust.Extensions" Version="4.12.2025.621-beta0339" />
    <PackageReference Include="HlktechIoT.Data" Version="1.7.2025.6210229" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\HlktechIoT.IoTPush\HlktechIoT.IoTPush.csproj" />
  </ItemGroup>

</Project>
