﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>续期支付表</summary>
public partial class RenewalPayModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int64 Id { get; set; }

    /// <summary>支付订单号</summary>
    public String? TradeNo { get; set; }

    /// <summary>设备DeviceName</summary>
    public String Devices { get; set; } = null!;

    /// <summary>续期年限</summary>
    public Int32 RenewalYear { get; set; }

    /// <summary>金额</summary>
    public Decimal Amount { get; set; }

    /// <summary>状态(0待支付 1支付成功 2支付失败 3已取消)</summary>
    public Int32 Status { get; set; }

    /// <summary>支付方式(1支付宝 2微信)</summary>
    public Int32 PayWay { get; set; }

    /// <summary>支付时间</summary>
    public DateTime PayTime { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IRenewalPay model)
    {
        Id = model.Id;
        TradeNo = model.TradeNo;
        Devices = model.Devices;
        RenewalYear = model.RenewalYear;
        Amount = model.Amount;
        Status = model.Status;
        PayWay = model.PayWay;
        PayTime = model.PayTime;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
