﻿@model XCode.Membership.Menu
@{
    Html.AppendTitleParts(T("编辑菜单").Text);
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }
</style>
<form class="layui-form" lay-filter="organization-form" style="padding: 15px 0 0 0;">
    <input type="hidden" name="Id" value="@Model?.ID" />
    <div class="layui-form-item">
        <label class="layui-form-label">@T("名称")</label>
        <div class="layui-input-block">
            <input type="text" name="Name" value="@Model?.Name" lay-verify="required" lay-verType="tips" placeholder="请输入菜单名称" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">@T("显示名称")</label>
        <div class="layui-input-block">
            <input type="text" name="DisplayName" value="@Model?.DisplayName" lay-verify="required" lay-verType="tips" placeholder="请输入菜单显示名称" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">@T("图标")</label>
        <div class="layui-input-block">
            <input type="text" name="Icon" value="@Model?.Icon" lay-verType="tips" placeholder="请输入菜单图标" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">@T("排序")</label>
        <div class="layui-input-block">
            <input type="number" name="Sort" lay-verify="required" lay-verType="tips" placeholder="请输入排序" value="@Model?.Sort" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn layui-hide" lay-submit lay-filter="organization-submit" id="organization-submit">@T("提交")</button>
        </div>
    </div>
</form>

<script asp-location="Footer">
    layui.use(['abp', 'form'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        window.submitForm = function () {
            $("#organization-submit").click();
        }

        form.on('submit(organization-submit)', function (data) {
            var field = data.field;

            var waitIndex = parent.layer.load(2);
            abp.ajax({
                url: "@Url.Action("EditMenu")",
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                data: field,
                abpHandleError: false
            }).done(function (data) {
                if (data.success) {
                    data.index = index;
                    let parentWindow = parent.selectedWindow().window;
                    parentWindow.saveCallback(data);
                } else {
                    let parentWindow = parent.selectedWindow().window;
                    parentWindow.warning(data.msg);
                }
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

    });
</script>