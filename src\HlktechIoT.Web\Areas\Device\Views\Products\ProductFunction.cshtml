@model Product
@{
    Html.AppendTitleParts(T("功能定义管理").Text);
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .selectLabel {
        min-width: 50px !important;
        min-height: 30px !important;
        line-height: 30px !important;
    }

    .selectBox {
        width: 500px !important;
        height: 40px !important;
        display: flex !important;
        background: transparent !important;
        /* border:2px solid black; */
    }

    .dg-form {
        /* position:absolute; */
        /* top: 30px; */
        z-index: 1000;
    }
    /* .layui-table-cell {
                    white-space: normal;
                } */

    /* 操作列自适应样式 */
    .layui-table-fixed-r .layui-table-body {
        overflow-x: hidden !important;
    }

    .operation-column {
        white-space: nowrap;
        padding: 0 5px; /* 添加左右边距 */
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center; /* 垂直居中 */
        flex-wrap: nowrap;
        height: 100%; /* 确保容器占满单元格高度 */
        width: 100%; /* 确保容器占满单元格宽度 */
        box-sizing: border-box; /* 确保内边距不增加总宽度 */
    }

        .operation-column .pear-btn {
            margin-right: 6px; /* 保持原有按钮间距 */
            padding: 0 8px; /* 保持按钮内边距 */
            height: 30px; /* 保持按钮高度 */
            line-height: 30px; /* 行高与高度一致 */
            min-width: 44px; /* 按钮最小宽度 */
            text-align: center;
            flex-grow: 1; /* 按钮自动扩展占用空间 */
            flex-basis: 0; /* 所有按钮基础宽度相同 */
        }

            .operation-column .pear-btn:last-child {
                margin-right: 0;
            }

    /* 确保表格单元格内容居中 */
    .layui-table-cell {
        text-align: center;
        padding: 0 2px; /* 保持单元格内边距 */
    }

    /* 固定列样式优化 */
    .layui-table-fixed {
        height: auto !important;
    }

    /* 确保表格右侧固定列正确显示 */
    .layui-table-fixed-r {
        right: 0 !important;
    }

        /* 优化表格右侧固定列的单元格 */
        .layui-table-fixed-r .layui-table-cell {
            padding: 0;
        }
</style>

<form class="layui-form dg-form">
    <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width: auto">@T("关键词")：</label>
            <div class="layui-input-inline">
                <input type="text" name="key" id="key" placeholder="@T("功能名称")" autocomplete="off" class="layui-input" lay-filter="key">
            </div>
        </div>
    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;

        var iframeName = window.name;
        var index = iframeName.replace("iframe", "");

                 // 按钮配置集中定义
        var operationButtons = [
            { text: '@T("编辑")', event: 'EditPF', class: 'pear-btn-primary' },
            @if (this.Has((PermissionFlags)8))
            {
                @:{ text: '@T("删除")', event: 'del', class: 'pear-btn-danger' },
            }
        ];

        // 将按钮配置保存到全局变量，供模板使用
        window.operationButtons = operationButtons;

        // 动态计算操作列宽度的函数
        function calculateOperationColumnWidth() {
            try {
                var buttonMargin = 6; // 按钮间距
                var extraPadding = 24; // 额外边距
                var baseButtonPadding = 20; // 按钮内边距

                if (operationButtons.length === 0) {
                    return 100;
                }

                var totalWidth = 0;

                operationButtons.forEach(function(button) {
                    // 调整文字宽度估算：中文字符约16px，英文字符约9px
                    var text = button.text;
                    var textWidth = 0;
                    for (var i = 0; i < text.length; i++) {
                        var char = text.charAt(i);
                        if (/[\u4e00-\u9fa5]/.test(char)) {
                            textWidth += 16; // 中文字符
                        } else {
                            textWidth += 9;  // 英文字符
                        }
                    }

                    var buttonWidth = textWidth + baseButtonPadding;
                    totalWidth += buttonWidth;
                });

                // 加上按钮间距和额外边距
                totalWidth += (operationButtons.length - 1) * buttonMargin + extraPadding;

                // 设置最小宽度
                if (totalWidth < 120) {
                    totalWidth = 120;
                }

                console.log('计算的操作列宽度:', totalWidth, '按钮数量:', operationButtons.length, '按钮配置:', operationButtons);
                return totalWidth;
            } catch (error) {
                console.error('计算操作列宽度时出错:', error);
                return 200;
            }
        }

        // 应用操作列宽度的函数
        function applyOperationColumnWidth() {
            try {
                var calculatedWidth = calculateOperationColumnWidth();

                var fixedRightHeader = $('.layui-table-fixed-r .layui-table-header table thead tr th:last-child');
                var fixedRightBody = $('.layui-table-fixed-r .layui-table-body table tbody tr td:last-child');

                if (fixedRightHeader.length > 0) {
                    fixedRightHeader.css('width', calculatedWidth + 'px');
                    fixedRightHeader.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                if (fixedRightBody.length > 0) {
                    fixedRightBody.css('width', calculatedWidth + 'px');
                    fixedRightBody.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                var mainTableHeader = $('#tablist').next('.layui-table-view').find('.layui-table-header table thead tr th:last-child');
                var mainTableBody = $('#tablist').next('.layui-table-view').find('.layui-table-body table tbody tr td:last-child');

                if (mainTableHeader.length > 0) {
                    mainTableHeader.css('width', calculatedWidth + 'px');
                    mainTableHeader.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                if (mainTableBody.length > 0) {
                    mainTableBody.css('width', calculatedWidth + 'px');
                    mainTableBody.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                console.log('已应用操作列宽度:', calculatedWidth);
            } catch (error) {
                console.error('应用操作列宽度时出错:', error);
            }
        }

        // 监听窗口大小变化
        $(window).on('resize', function() {
            try {
                setTimeout(function() {
                    applyOperationColumnWidth();
                    console.log('窗口大小变化，已重新调整操作列宽度');
                }, 100);
            } catch (error) {
                console.error('窗口resize时出错:', error);
            }
        });

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("ProductFunctionList", new { Id = Model.Id })'
            , page: false //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 70
            , cols: [[
                { field: 'Id', title: '@T("编号")', width: 60 }
                , { field: 'Name', title: '@T("名称")', minWidth: 130 }
                , { field: 'ProductName', title: '@T("产品")', minWidth: 150 }
                , { field: 'Kind', title: '@T("种类")', width: 50, toolbar: '#Kind' }
                , { field: 'Identifier', title: '@T("标识")', minWidth: 140 }
                , { field: 'AccessMode', title: '@T("访问模式")', minWidth: 100, toolbar: '#AccessMode' }
                , { field: 'DataType', title: '@T("类型")', width: 80, toolbar: '#DataType' }
                , { field: 'Length', title: '@T("长度")', width: 70 }
                , { field: 'Min', title: '@T("最小值")', width: 70 }
                , { field: 'Max', title: '@T("最大值")', width: 70 }
                , { field: 'Step', title: '@T("步长")', width: 70 }
                , { field: 'MaxStep', title: '@T("最大间隔")', width: 90 }
                , { field: 'Unit', title: '@T("单位")', width: 70 }
                , { field: 'UnitName', title: '@T("单位名称")', width: 90 }
                , { field: 'Address', title: '@T("点位地址")', width: 90 }
                , { field: 'WriteRule', title: '@T("反解析规则")', width: 100 }
                , { field: 'ScalingFactor', title: '@T("缩放因子")', width: 90 }
                , { field: 'ConstantFactor', title: '@T("常量因子")', width: 90 }
                , { field: 'Mapping', title: '@T("枚举映射")', width: 90 }
                , { field: 'CallMethod', title: '@T("调用方法")', width: 90 }
                , { field: 'EventType', title: '@T("事件类型")', width: 90 }
                , { field: 'DefaultValue', title: '@T("默认值")', minWidth: 250 }
                , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: calculateOperationColumnWidth()  }
            ]]
            , limit: 13
            , page: 1
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-100'
            , id: 'ProductFunctionTables'
            , done: function (res, curr, count) {
                try {
                    // 延迟执行，确保DOM已经渲染完成
                    setTimeout(function() {
                        applyOperationColumnWidth();
                    }, 200);
                } catch (error) {
                    console.error('表格done回调中出错:', error);
                }
            }
        });

        window.active = {
            reload: function () {
                table.reload('ProductFunctionTables', {
                    where: {
                        key: $("#key").val(),
                    },
                });
            }
        };

        $("#key").on("input", function (e) {
            active.reload();
            // console.log($("#key").val());
            //  table.reload('ProductFunctionTables', {
            //         where: {
            //             Id: @Model.Id,
            //         }
            //     });
        });

        table.on('tool(tool)', function (obj) {
            //  console.log(obj);
            var data = obj.data;
            if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', function (index) {
                    // console.log(data.Id);
                    $.post('@Url.Action("DeletePF")', { Id: data.Id, Code: data.Code }, function (data) {
                        if (data.success) {
                            abp.notify.success('@T("删除成功")');
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            } else if (obj.event === 'EditPF') {
                window.EditPF(data);
            }
        });

        table.on('toolbar(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === 'select') {
                window.select();
            } else if (obj.event === 'addPF') {
                window.addPF();
            } else if (obj.event === 'share') {
                window.share();
            } else if (obj.event === 'refresh') {
                active.reload();
            }else if (obj.event === 'TSL') {
                window.TSL();
            } else if (obj.event === 'ExportTSL') {
                window.ExportTSL();
            } else if (obj.event === 'ImportTSL') {
                window.ImportTSL();
            }
        });

        window.addPF = function () {
            // console.log(data);
            top.layui.dg.popupRight({
                id: 'addPF'
                , title: ' @T("新增功能定义")'
                , closeBtn: 1
                , area: ['580px']
                , success: function (obj, index) {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("addPF")' + abp.utils.formatString("?id={0}", @Model.Id) + '" frameborder="0" class="layadmin-iframe" name="' + iframeName + "_" + index + '"></iframe>');
                }
            });
        }

        window.EditPF = function (data) {
            // console.log(data);
            top.layui.dg.popupRight({
                id: 'EditPF'
                , title: ' @T("编辑功能定义")'
                , closeBtn: 1
                , area: ['680px']
                , success: function (obj, index) {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("EditPF")' +
                     abp.utils.formatString("?id={0}", data.Id) + '" frameborder="0" class="layadmin-iframe" name="' 
                     + iframeName + "_" + index + '"></iframe>');
                }
            });
        }

        window.addPF = function () {
            // console.log(data);
            top.layui.dg.popupRight({
                id: 'addPF'
                , title: ' @T("新增功能定义")'
                , closeBtn: 1
                , area: ['680px']
                , success: function (obj, index) {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("addPF")' +
                     abp.utils.formatString("?id={0}", @Model.Id) + '" frameborder="0" class="layadmin-iframe" name="' + 
                     iframeName + "_" + index + '"></iframe>');
                }
            });
        }

        window.share = function () {
            parent.layer.confirm('@T("确认发布吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                $.post('@Url.Action("PublishBatch")', { Id: @Model.Id }, function (data) {
                    if (data.success) {
                        abp.notify.success(data.msg);
                        active.reload();
                    } else {
                        abp.notify.warn(data.msg);
                    }
                });
                parent.layer.close(index);
            });
        }

         window.TSL = function () {
            // console.log(data);
            top.layui.dg.popupRight({
                id: 'TSL'
                , title: ' @T("功能定义TSL")'
                , closeBtn: 1
                , area: ['580px']
                , success: function (obj, index) {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("TSL")' + abp.utils.formatString("?id={0}", @Model.Id) + '" frameborder="0" class="layadmin-iframe" name="' + iframeName + "_" + index + '"></iframe>');
                }
            });
        }

        window.ExportTSL = function () {
            var link = document.createElement('a');
            link.href = '@Url.Action("ExportTSL")?id=' + @Model.Id;
            // 模拟点击下载
            link.click();
            var href = '@Url.Action("ExportTSL")?id=' + @Model.Id;
            $("#export").attr("href", href);
        }

        window.ImportTSL = function () {
            // console.log(data);
            top.layui.dg.popupRight({
                id: 'ImportTSL'
                , title: ' @T("导入TSL")'
                , closeBtn: 1
                , area: ['580px']
                , success: function (obj, index) {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("ImportTSL")' + abp.utils.formatString("?id={0}", @Model.Id) + '" frameborder="0" class="layadmin-iframe" name="' + iframeName + "_" + index + '"></iframe>');
                }
            });
        }

        window.warning = function (msg) {
            abp.notify.warn(msg);
        }

        window.success = function (msg) {
            abp.notify.success(msg);
        }

    });
</script>

<script type="text/html" id="tool">
    <div class="operation-column">
    {{#  layui.each(window.operationButtons, function(index, button){ }}
        <a class="pear-btn {{button.class}} pear-btn-xs" lay-event="{{button.event}}">{{button.text}}</a>
    {{#  }); }}
    </div>
</script>
<script type="text/html" id="user-toolbar">
    <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="addPF">
        <i class="layui-icon layui-icon-add-1"></i>
        @T("新增")
    </button>
    <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="share">
        <i class="layui-icon layui-icon-share"></i>
        @T("发布")
    </button>
        <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="TSL">
        <i class="layui-icon layui-icon-share"></i>
        @T("功能定义TSL")
    </button>
    </button>
        <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="ExportTSL">
        <i class="layui-icon layui-icon-share"></i>
        @T("导出TSL")
    </button>
    </button>
        <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="ImportTSL">
        <i class="layui-icon layui-icon-share"></i>
        @T("导入TSL")
    </button>
</script>

<script type="text/html" id="Kind">
    {{# if(d.Kind == "Property") { }}
    @T("属性")
    {{# } else if(d.Kind == "Service") { }}
    @T("服务")
    {{# } else if(d.Kind == "Event") { }}
    @T("事件")
    {{# } else if(d.Kind == "6") { }}}
</script>
<script type="text/html" id="AccessMode">
    {{# if(d.AccessMode == "Readonly") { }}
    @T("只读")
    {{# } else if(d.AccessMode == "ReadWrite") { }}
    @T("读写")
    {{# } else if(d.AccessMode == "WriteOnly") { }}
    @T("只写")
    {{# } else if(d.AccessMode == "6") { }}}
</script>
<script type="text/html" id="DataType">
    {{# if(d.DataType == "short") { }}
    @T("短整数")
    {{# } else if(d.DataType == "int") { }}
    @T("整数")
    {{# } else if(d.DataType == "float" || d.DataType == "Single") { }}
    @T("小数")
    {{# } else if(d.DataType == "bool") { }}
    @T("布尔型")
    {{# } else if(d.DataType == "byte") { }}
    @T("字节")
    {{# } else if(d.DataType == "long" || d.DataType == "UInt64") { }}
    @T("长整数")
    {{# } else if(d.DataType == "double" || d.DataType == "Double") { }}
    @T("双精度")
    {{# } else if(d.DataType == "text" || d.DataType == "String") { }}
    @T("文本")
    {{# } else if(d.DataType == "time" || d.DataType == "DateTime") { }}
    @T("时间")
    {{# } }}
</script>