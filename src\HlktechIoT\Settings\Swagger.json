{
  "SwaggerOption": {
    "Enabled": true,
    "MiniProfilerEnabled": false,
    "XmlComments": [ "YRY.Api.Framework.xml", "" ],
    "Theme": 0,
    "RoutePrefix": "help",
    "Description": "湖北登灏科技有限公司提供的DH.Api HTTP API 接口",
    "Title": "DH.Api接口文档",
    "Version": "first version",
    "TermsOfService": "deng-hao.com",
    "Contact": {
      "Email": "<EMAIL>",
      "Name": "DH.Api",
      "Url": "https://www.deng-hao.com"
    },
    "License": {
      "Name": "DH.Api 官方文档",
      "Url": "https://www.deng-hao.com"
    },
    "Headers": [ //swagger默认头参数
      {
        "Name": "Signature",
        "Description": "通信加密签名"
      },
      {
        "Name": "TimeStamp",
        "Description": "时间戳"
      },
      {
        "Name": "Nonce",
        "Description": "随机数"
      }
    ],
    "Query": [ //swagger默认url公共参数
      //{
      //  "Name": "sign",
      //  "Description": "签名"
      //},
      //{
      //  "Name": "timestamp",
      //  "Description": "客户端时间戳"
      //}
    ]
  }
}
