﻿using NewLife.Configuration;

using System.ComponentModel;

namespace HlktechIoT.Common;

[DisplayName("云云对接设置")]
[Config("YunSettings")]
public class YunSettings : Config<YunSettings> {
    /// <summary>
    /// 百度小度Client_Id
    /// </summary>
    public String BaiduClientId { get; set; } = "xiaodu";

    /// <summary>
    /// 百度小度ClientSecret
    /// </summary>
    public String BaiduClientSecret { get; set; } = "asdfsdfaasdfasdfasdf";

    /// <summary>
    /// 百度小度回调地址
    /// </summary>
    public String BaiduRedirectUri { get; set; } = "https://xiaodu-dbp.baidu.com/saiya/auth/767596c03e3c3acc79571aef99cac0d7";
}
