﻿using DG.SafeOrbit.Extensions;

using HlktechIoT.Data;

using NewLife.Data;

using XCode;

namespace HlktechIoT.Entity;

public class ProjectShareEX : ProjectShare {
    /// <summary>根据列表projectId和key获取列表</summary>
    /// <param name="projectId">项目Id</param>
    /// <param name="key">关键字</param>
    public static IList<ProjectShare> SearchById(int projectId, string key, PageParameter page)
    {
        WhereExpression where = new WhereExpression();
        if (projectId >= 0)
        {
            where &= @_.ProjectId == projectId;
        }
        if (!key.IsNullOrEmpty())
        {
            where &= (Expression)(@_.ProjectKey.Contains(key) | @_.ProductIds.Contains(key) | @_.ProductKeys.Contains(key) | @_.RScope.Contains(key) | @_.UName.Contains(key) | @_.UserName.Contains(key) | @_.CreateUser.Contains(key) | @_.CreateIP.Contains(key) | @_.UpdateUser.Contains(key) | @_.UpdateIP.Contains(key));
        }
        return Entity<ProjectShare>.FindAll(where, page);
    }

    /// <summary>根据username判断</summary>
    /// <param name="userName">项目成员</param>
    /// <param name="projectId">项目成员</param>
    public static bool FindByNameAndId(String userName, int projectId)
    {
        WhereExpression where = new WhereExpression();
        if (!userName.IsNullOrEmpty())
        {
            where &= @_.UserName == userName;
        }
        if (projectId > 0)
        {
            where &= @_.ProjectId == projectId;
        }
        return Entity<ProjectShare>.Find(where) == null;
    }
}
