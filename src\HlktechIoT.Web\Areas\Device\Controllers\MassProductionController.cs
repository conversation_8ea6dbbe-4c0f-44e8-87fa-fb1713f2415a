﻿using DG.Web.Framework;

using DH.Core.Infrastructure;
using DH.Entity;
using DH.Helpers;
using DH.Npoi;

using HlktechIoT.Data;
using HlktechIoT.Dto;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Caching;
using NewLife.Data;
using NewLife.Serialization;

using Pek.Compress;
using Pek.Helpers;
using Pek.Models;

using System.ComponentModel;

using XCode.Membership;

using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Device.Controllers;

/// <summary>量产管理</summary>
[DisplayName("量产管理")]
[Description("项目或者产品下的设备量产管理")]
[DeviceArea]
[DHMenu(30, ParentMenuName = "MassManager", ParentMenuDisplayName = "量产管理", ParentMenuUrl = "", ParentMenuOrder = 80, ParentIcon = "layui-icon-util", CurrentMenuUrl = "~/{area}/MassProduction", CurrentMenuName = "MassProduction", LastUpdate = "20241009")]
public class MassProductionController : BaseAdminControllerX {

    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 30;

    /// <summary>
    /// 量产管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("量产管理")]
    [HttpGet]
    public IActionResult Index()
    {
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);

        var HasRole = modelRole.IsAdmin || (!modelRole.Roles.IsNullOrWhiteSpace() && modelRole.Roles.Contains(",003,"));

        ViewBag.HasRole = HasRole;

        return View();
    }

    /// <summary>
    /// 根据条件搜索
    /// </summary>
    /// <param name="key">产品名称/产品密钥</param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("量产订单查询")]
    public IActionResult GetList(String key, Int32 page = 1, Int32 limit = 10)
    {
        if (!key.IsNullOrWhiteSpace()) key = key.Trim();
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = MassProduction._.Id,
            Desc = true
        };

        var UId = 0;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin && (modelRole.Roles.IsNullOrWhiteSpace() || !modelRole.Roles.Contains(",003,")))
        {
            UId = ManageProvider.User?.ID ?? -1;
        }

        var list = MassProduction.Search(UId, key, pages);

        var data = list.Select(x => new { x.Id, x.PId, x.ProductName, x.ProductCode, x.Count, Status = x.Status == 1 ? GetResource("审核中") : x.Status == 2 ? GetResource("已审核") : x.Status == 3 ? GetResource("审核失败") : GetResource("未审核"), x.Remark, x.CreateUserID, x.CreateUser, x.CreateTime, x.UpdateTime, x.UpdateUser, x.UpdateUserID, State = x.Status });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 搜索产品
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize]
    [HttpPost]
    [DisplayName("搜索产品")]
    public IActionResult SearchProduct(String keyword, Int32 page)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
        };

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin && (modelRole.Roles.IsNullOrWhiteSpace() || !modelRole.Roles.Contains(",003,")))
        {
            res.data = Product.Search(keyword, ManageProvider.User?.ID ?? -1, pages).Select(e =>
            {
                var selected = false;

                return new Xmselect<Int32>
                {
                    name = e.Name + $"({e.Code})",
                    value = e.Id,
                    selected = selected
                };
            });
        }
        else
        {
            res.data = Product.Search(keyword, 0, pages).Select(e =>
            {
                var selected = false;

                return new Xmselect<Int32>
                {
                    name = e.Name + $"({e.Code})",
                    value = e.Id,
                    selected = selected
                };
            });
        }

        res.extdata = pages.PageCount;
        res.success = true;
        return Json(res);
    }

    /// <summary>
    /// 新增量产订单
    /// </summary>
    /// <returns></returns>
    [DisplayName("新增量产订单")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult Add()
    {
        return View();
    }

    /// <summary>
    /// 新增量产订单
    /// </summary>
    /// <returns></returns>
    [DisplayName("新增量产订单")]
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    public IActionResult Add(Int32 Count, Int32 select, String Remark)
    {
        var res = new DResult();

        var model = new MassProduction();
        model.PId = select;
        model.ProductName = model.Product?.Name ?? String.Empty;
        model.ProductCode = model.Product?.Code ?? String.Empty;
        model.Count = Count;
        model.Status = 0;
        model.Remark = Remark;
        model.ProjectId = model.Product?.ProjectId ?? 0;
        model.Insert();

        res.success = true;
        res.msg = GetResource("新增成功，待审核");
        return Json(res);
    }

    /// <summary>
    /// 编辑量产订单
    /// </summary>
    /// <returns></returns>
    [DisplayName("编辑量产订单")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult Edit(Int32 Id)
    {
        var model = MassProduction.FindById(Id);

        if (model == null)
        {
                return Content(GetResource("订单不存在"));
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin && (modelRole.Roles.IsNullOrWhiteSpace() || !modelRole.Roles.Contains(",003,")))
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                return Content("只能编辑自己的订单");
            }
        }

        var modelProduct = Product.FindById(model.PId);
        ViewBag.ProductList = new List<NameValueL<Int32?>>() { new() { name = modelProduct?.Name + $"({modelProduct?.Code})", value = modelProduct?.Id } }.ToJson();

        return View(model);
    }

    /// <summary>
    /// 编辑量产订单
    /// </summary>
    /// <returns></returns>
    [DisplayName("编辑量产订单")]
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    public IActionResult Edit(Int32 Id, Int32 Count, Int32 select, String Remark)
    {
        var res = new DResult();

        var model = MassProduction.FindById(Id);

        if (model == null)
        {
                return Json(new { success = false, msg = GetResource("订单不存在") });
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin && (modelRole.Roles.IsNullOrWhiteSpace() || !modelRole.Roles.Contains(",003,")))
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                return Json(new { success = false, msg = GetResource("只能编辑自己的订单") });
            }
        }

        model.PId = select;
        model.ProductName = model.Product?.Name ?? String.Empty;
        model.ProductCode = model.Product?.Code ?? String.Empty;
        model.Count = Count;
        model.Status = 0;
        model.Remark = Remark;
        model.ProjectId = model.Product?.ProjectId ?? 0;
        model.Update();

        res.success = true;
        res.msg = GetResource("编辑成功，待审核");
        return Json(res);
    }

    /// <summary>
    /// 删除订单
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除订单")]
    [HttpPost]
    public IActionResult Delete(Int32 Id)
    {
        var res = new DResult();

        var model = MassProduction.FindById(Id);

        if (model == null)
        {
            return Json(new { success = false, msg = GetResource("订单不存在") });
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin && (modelRole.Roles.IsNullOrWhiteSpace() || !modelRole.Roles.Contains(",003,")))
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                return Json(new { success = false, msg = GetResource("只能删除自己的订单") });
            }
        }

        model.Delete();

        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 审核订单
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)16)]
    [DisplayName("审核")]
    [HttpPost]
    public IActionResult Audit(Int32 Id)
    {
        var res = new DResult();

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin && (modelRole.Roles.IsNullOrWhiteSpace() || !modelRole.Roles.Contains(",003,")))
        {
            return Json(new { success = false, msg = GetResource("您没有量产管理权限操作") });
        }

        var model = MassProduction.FindById(Id);
        if (model == null)
        {
            return Json(new { success = false, msg = GetResource("订单不存在") });
        }

        if (model.Status != 0)
        {
            return Json(new { success = false, msg = GetResource("状态不为待审核") });
        }

        model.Status = 1;
        model.Update();

        Task.Run(() =>
        {
            var cacheProvider = EngineContext.Current.Resolve<ICacheProvider>();
            model.CreateDeviceKey(cacheProvider);
        });

        res.success = true;
        res.msg = GetResource("生成中,请等待一会儿刷新查看结果");
        return Json(res);
    }

    /// <summary>
    /// 导出量产订单
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)32)]
    [DisplayName("导出")]
    [HttpGet]
    public IActionResult Export(Int32 Id)
    {
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin && (modelRole.Roles.IsNullOrWhiteSpace() || !modelRole.Roles.Contains(",003,")))
        {
            return Content("您没有量产管理权限操作");
        }

        var model = MassProduction.FindById(Id);
        if (model == null)
        {
            return Content("订单不存在");
        }

        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                return Content("只能导出自己的订单");
            }
        }

        //var list = Data.Device.FindAllByMId(Id).Select(e => new MassProductionDto()
        //{
        //    ProjectID = model.Product.ProjectKey,
        //    ProductKey = model.ProductCode,
        //    ProductSecret = model.Product?.Secret,
        //    DeviceName = e.Code,
        //    DeviceSecret = e.Secret,
        //});
        var list = Data.Device.FindAllByMId(Id).Select(e =>
        {
            var content = $"{e.Code}|{model.Product.ProjectKey}|{model.ProductCode}|{model.Product?.Secret}|{e.Secret}";
            var key = DHLZW.GetKeyFromText(e.Code);

            return new MassProductionEncryptDto()
            {
                DeviceName = e.Code,
                Content = Base64Helper.StringToBase64(DHLZW.Compress(content, key).Join()),
                CreateTime = e.CreateTime,
            };
        });

        if (list.Count() == 1)
        {
            string fileName = $"{GetResource("量产订单")}_{Id}_{DateTime.Now:yyyyMMddhhmm}.txt";

            // 创建文件内容
            string fileContent = $"{list.First().DeviceName}     {list.First().Content}     {list.First().CreateTime.ToString("yyyy-MM-dd HH:mm:ss")}";
            byte[] fileBytes = System.Text.Encoding.UTF8.GetBytes(fileContent);

            return File(fileBytes, "text/plain", fileName);
        }

        //IExporter exporter = new ExcelExporter();
        //var result = await exporter.ExportAsByteArray(list.ToList()).ConfigureAwait(false);

        var result = list.ToExcelBytes(ExcelFormat.Xlsx);

        return File(result, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{GetResource("量产订单")}_{Id}_{DateTime.Now:yyyyMMddhhmm}.xlsx");
    }
}
