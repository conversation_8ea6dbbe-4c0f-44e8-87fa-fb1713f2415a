﻿@using DH.Core.Domain.Common;
@using DH.Core.Configuration;
@using DH.Core.Domain.Seo;
@using DH.Core.Events;
@using YRY.FastAdmin.Components;

@inject AppSettings appSettings
@inject CommonSettings commonSettings
@inject IEventPublisher eventPublisher
@inject SeoSettings seoSettings

@{
    // 当 jQuery migrate 脚本日志记录处于活动状态时，您将在浏览器控制台中看到日志
    if (commonSettings.JqueryMigrateScriptLoggingActive)
    {
        DHHtml.AppendScriptParts(ResourceLocation.Footer, "~/lib_npm/jquery-migrate/jquery.migrate.js", debugSrc: "", excludeFromBundle: true, isVirtual: true);
    }
    else
    {
        DHHtml.AppendScriptParts(ResourceLocation.Footer, "~/lib_npm/jquery-migrate/jquery.migrate.min.js", debugSrc: "", excludeFromBundle: true, isVirtual: true);
    }

    DHHtml.AppendScriptParts(location:ResourceLocation.Footer, src: "~/lib_npm/jquery/jquery.min.js", debugSrc: "", excludeFromBundle: true, isVirtual: true);

    //custom tag(s);
    if (!string.IsNullOrEmpty(seoSettings.CustomHeadTags))
    {
        DHHtml.AppendHeadCustomParts(seoSettings.CustomHeadTags);
    }

    var displayMiniProfiler = appSettings.Get<CommonConfig>().MiniProfilerEnabled && RoleEx.CheckRole("AccessProfiling");

    //event
    await eventPublisher.PublishAsync(new PageRenderingEvent(DHHtml));

    var title = await DHHtml.GenerateTitleAsync();
    var description = await @DHHtml.GenerateMetaDescriptionAsync();
    var keywords = await DHHtml.GenerateMetaKeywordsAsync();
}
<!DOCTYPE html>
<html lang="@CultureInfo.CurrentUICulture.TwoLetterISOLanguageName" dir="@Html.GetUIDirection(!await Html.ShouldUseRtlThemeAsync())" class="@DHHtml.GeneratePageCssClasses()">
<head>
    <title>@title</title>
    <meta http-equiv="Content-type" content="text/html;charset=UTF-8" />
    <meta name="description" content="@description" />
    <meta name="keywords" content="@keywords" />
    <meta name="generator" content="denghao" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    @DHHtml.GenerateHeadCustom()
    @*这是为了主题可以将内容注入到标题中*@
    @await Html.PartialAsync("Head")

    @DHHtml.GenerateCssFiles()

    @await Component.InvokeAsync(typeof(WidgetViewComponent), new { widgetZone = PublicWidgetZones.HeadHtmlTag })
    @DHHtml.GenerateCanonicalUrls()
    @await Component.InvokeAsync(typeof(NewsRssHeaderLinkViewComponent))
    @await Component.InvokeAsync(typeof(BlogRssHeaderLinkViewComponent))
    @*插入图标图标和应用程序图标头代码*@
    @await Component.InvokeAsync(typeof(FaviconViewComponent))
    @if (displayMiniProfiler)
    {
        <mini-profiler />
    }
    @DHHtml.GenerateScripts(ResourceLocation.Head)
    @DHHtml.GenerateInlineScripts(ResourceLocation.Head)
    <!--Powered by DengHao - https://www.deng-hao.com-->

    @Html.Raw(commonSettings.HeaderCustomHtml)
</head>
<body>
    <dh-antiforgery-token />
    @RenderBody()

    @DHHtml.GenerateScripts(ResourceLocation.Footer)
    @DHHtml.GenerateInlineScripts(ResourceLocation.Footer)

    @Html.Raw(commonSettings.FooterCustomHtml)
</body>
</html>
