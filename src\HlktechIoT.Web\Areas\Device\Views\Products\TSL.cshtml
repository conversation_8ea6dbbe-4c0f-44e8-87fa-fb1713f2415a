@model Product
@{
    Html.AppendTitleParts(T("功能定义TSL").Text);

}
<style asp-location="true">
    .pear-container{
        height:80vh;
        /* border:2px solid ; */
    }
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        text-indent:0px;
        margin-left:0px;
    }

    .layui-form-item.btn {
        display: flex;
        justify-content: center;
        place-items:center;
    }
    .cancelBtn{
        margin-left:20px;
    }

    .label-width span {
        color: #f00;
    }

    .layui-form-label {
        width: 80px;
    }

    .upload {
        cursor: pointer;
        max-width: 120px;
        max-height: 90px;
    }
    .select{
        max-width: 420px !important;
        border: 1px solid #e6e6e6;
    }
    .layui-textarea{
        text-indent:0px;
        margin-left:0px;
        height:64vh;
    }
</style>
<div class="containers">
   <form class="layui-form">  
        <div class="layui-form-item">
            <div class="layui-input-inline" style="min-width:100%;height:auto;">
                <textarea  placeholder="@T("请输入模板")"
                  class="layui-textarea" name="Tsl">@ViewBag.TSL</textarea>
            </div>
        </div>

        <div class="layui-form-item btn">
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" value="@T("保存")" />
            <input type="button" lay-submit lay-filter="Cancel" id="Cancel" class="pear-btn layui-btn-primary layui-border cancelBtn" value="@T("取消")" />
        </div>

    </form>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;

        var iframeName = window.name;
        var iframeArray = iframeName.split('_');
        var index = iframeArray[1];
        var parentIframe = iframeArray[0];

        var targetIframe = null;

        var parentIframes = window.parent.document.getElementsByTagName('iframe');
        for (var i = 0; i < parentIframes.length; i++) {
            if (parentIframes[i].name == parentIframe) {
                targetIframe = parentIframes[i];
                break;
            }
        }
         form.on('submit(Cancel)', function (data) {
                // 关闭当前页面
                parent.layer.close(index);
         })

        form.on('submit(Submit)', function (data) {
            data.field.Id = @Model.Id
            // console.log(data.field);

            if (data.field.Tsl.length == 0) {
                abp.notify.warn("@T("文本值不能为空")");
                return;
            }

            var waitIndex = parent.layer.load(2);

            abp.ajax({
                url: "@Url.Action("TSL")",
                contentType : "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }

                // 关闭当前编辑页面
                parent.layer.close(index);

                targetIframe.contentWindow.active.reload();
                targetIframe.contentWindow.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

    });
</Script>
