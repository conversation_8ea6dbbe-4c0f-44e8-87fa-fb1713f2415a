@{
    Layout = null;
    ViewData["Title"] = "授权确认";
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewData["Title"]</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .consent-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 480px;
            overflow: hidden;
            animation: slideInUp 0.6s ease-out;
        }        
        
        @@keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .consent-header {
            background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
            color: white;
            padding: 40px 30px 30px;
            text-align: center;
            position: relative;
        }

        .consent-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 2px,
                rgba(255, 255, 255, 0.05) 2px,
                rgba(255, 255, 255, 0.05) 4px
            );
            animation: move 20s linear infinite;
        }        
        
        @@keyframes move {
            0% { transform: translateX(-50%) translateY(-50%) rotate(0deg); }
            100% { transform: translateX(-50%) translateY(-50%) rotate(360deg); }
        }

        .user-avatar {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            position: relative;
            z-index: 1;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }

        .user-avatar i {
            font-size: 36px;
            color: white;
        }

        .consent-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .consent-subtitle {
            font-size: 14px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .consent-body {
            padding: 40px 30px;
        }

        .user-info {
            text-align: center;
            margin-bottom: 30px;
        }

        .user-name {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .user-status {
            font-size: 14px;
            color: #059669;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .app-request {
            background: #f0f9ff;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 30px;
            border: 1px solid #e0f2fe;
            border-left: 4px solid #0ea5e9;
        }

        .app-request h6 {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .app-request p {
            font-size: 14px;
            color: #4b5563;
            margin-bottom: 16px;
            line-height: 1.5;
        }

        .permissions-list {
            background: #ffffff;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #e5e7eb;
        }

        .permissions-title {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .permissions-list ul {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .permissions-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
            font-size: 14px;
            color: #4b5563;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .permissions-list li:last-child {
            border-bottom: none;
        }

        .permissions-list li::before {
            content: '✓';
            color: #059669;
            font-weight: bold;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #ecfdf5;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            flex-shrink: 0;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .btn {
            padding: 16px 24px;
            border: none;
            border-radius: 12px;
            font-size: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-deny {
            background: #f3f4f6;
            color: #374151;
            border: 2px solid #e5e7eb;
        }

        .btn-deny:hover {
            background: #e5e7eb;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        .btn-allow {
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            color: white;
        }

        .btn-allow:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(5, 150, 105, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .consent-footer {
            background: #f8fafc;
            padding: 24px 30px;
            text-align: center;
        }

        .footer-warning {
            font-size: 13px;
            color: #6b7280;
            line-height: 1.5;
            margin-bottom: 12px;
        }

        .security-info {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .security-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            background: #ecfdf5;
            color: #059669;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .app-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            background: #eff6ff;
            color: #2563eb;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }        
        
        /* Mobile Responsive */
        @@media (max-width: 480px) {
            body {
                padding: 15px;
            }

            .consent-container {
                max-width: 100%;
                border-radius: 16px;
            }

            .consent-header {
                padding: 30px 20px 25px;
            }

            .consent-body {
                padding: 30px 20px;
            }

            .consent-footer {
                padding: 20px;
            }

            .consent-title {
                font-size: 22px;
            }

            .action-buttons {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .btn {
                padding: 14px 20px;
                font-size: 14px;
            }

            .security-info {
                flex-direction: column;
                gap: 8px;
            }
        }        
        
        /* Dark mode support */
        @@media (prefers-color-scheme: dark) {
            .consent-container {
                background: rgba(31, 41, 55, 0.95);
                color: #f9fafb;
            }

            .user-name {
                color: #f9fafb;
            }

            .app-request {
                background: #374151;
                border-color: #4b5563;
                border-left-color: #0ea5e9;
            }

            .app-request h6 {
                color: #f9fafb;
            }

            .app-request p {
                color: #d1d5db;
            }

            .permissions-list {
                background: #4b5563;
                border-color: #6b7280;
            }

            .permissions-title {
                color: #f3f4f6;
            }

            .permissions-list li {
                color: #d1d5db;
                border-bottom-color: #6b7280;
            }

            .btn-deny {
                background: #4b5563;
                color: #f9fafb;
                border-color: #6b7280;
            }

            .btn-deny:hover {
                background: #6b7280;
            }

            .consent-footer {
                background: #374151;
                color: #9ca3af;
            }

            .footer-warning {
                color: #9ca3af;
            }
        }
    </style>
</head>
<body>
    <div class="consent-container">
        <div class="consent-header">
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <h1 class="consent-title">授权确认</h1>
            <p class="consent-subtitle">请确认应用权限</p>
        </div>

        <div class="consent-body">
            <div class="user-info">
                <div class="user-name">@ViewBag.Username</div>
                <div class="user-status">
                    <i class="fas fa-check-circle"></i>
                    已登录验证
                </div>
            </div>

            <div class="app-request">
                <h6>
                    <i class="fas fa-mobile-alt"></i>
                    应用授权请求
                </h6>
                <p><strong>@ViewBag.AppName</strong> 正在请求访问您的账户，以便为您提供服务。</p>
                
                <div class="permissions-list">
                    <div class="permissions-title">
                        <i class="fas fa-shield-alt"></i>
                        请求的权限
                    </div>
                    <ul>
                        @{
                            var scopeDescriptions = ViewBag.ScopeDescriptions as List<string>;
                            if (scopeDescriptions != null && scopeDescriptions.Any())
                            {
                                foreach (var description in scopeDescriptions)
                                {
                                    <li>@description</li>
                                }
                            }
                            else
                            {
                                <li>访问基本账户信息</li>
                                <li>读取用户配置文件</li>
                                <li>代表您执行操作</li>
                            }
                        }
                    </ul>
                </div>
            </div>
            
            <form method="post" action="/oauth/consent">
                <input type="hidden" name="client_id" value="@ViewBag.ClientId" />
                <input type="hidden" name="redirect_uri" value="@ViewBag.RedirectUri" />
                <input type="hidden" name="scope" value="@ViewBag.Scope" />
                <input type="hidden" name="state" value="@ViewBag.State" />
                <input type="hidden" name="response_type" value="@ViewBag.ResponseType" />
                
                <div class="action-buttons">
                    <button type="submit" name="authorize" value="deny" class="btn btn-deny">
                        <i class="fas fa-times"></i>
                        拒绝授权
                    </button>
                    <button type="submit" name="authorize" value="allow" class="btn btn-allow">
                        <i class="fas fa-check"></i>
                        同意授权
                    </button>
                </div>
            </form>
        </div>

        <div class="consent-footer">
            <p class="footer-warning">
                授权后，<strong>@ViewBag.AppName</strong> 将获得上述权限。您可以随时在账户设置中撤销此授权。
            </p>
            <div class="security-info">
                <div class="security-badge">
                    <i class="fas fa-lock"></i>
                    安全连接
                </div>
                <div class="app-badge">
                    <i class="fas fa-certificate"></i>
                    已验证应用
                </div>
            </div>
        </div>
    </div>    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 按钮点击动画
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // 防止重复提交
                    if (this.disabled) return;
                    
                    // 防止默认提交，我们要手动处理
                    e.preventDefault();
                    
                    const form = this.form;
                    const authorizeValue = this.value;
                    const isAllow = authorizeValue === 'allow';
                    const originalText = this.innerHTML;
                    
                    // 确保authorize参数被正确添加到表单中
                    let authorizeInput = form.querySelector('input[name="authorize"]');
                    if (!authorizeInput) {
                        authorizeInput = document.createElement('input');
                        authorizeInput.type = 'hidden';
                        authorizeInput.name = 'authorize';
                        form.appendChild(authorizeInput);
                    }
                    authorizeInput.value = authorizeValue;
                    
                    if (isAllow) {
                        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在授权...';
                    } else {
                        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在处理...';
                    }
                    
                    this.disabled = true;
                    
                    // 禁用所有按钮防止重复提交
                    form.querySelectorAll('button').forEach(button => {
                        button.disabled = true;
                    });
                    
                    // 延迟提交表单，确保authorize参数被包含
                    setTimeout(() => {
                        form.submit();
                    }, 500);
                });
            });

            // 权限列表动画
            const permissionItems = document.querySelectorAll('.permissions-list li');
            permissionItems.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateX(-20px)';
                setTimeout(() => {
                    item.style.transition = 'all 0.3s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateX(0)';
                }, 200 + index * 100);
            });
        });
    </script>
</body>
</html>
