{"name": "NewLife.QuickVue", "version": "2.4.3", "description": "vue3 vite next admin template", "author": "lyt_20201208", "license": "MIT", "scripts": {"dev": "vite --force", "build": "vite build", "lint-fix": "eslint --fix --ext .js --ext .jsx --ext .vue src/"}, "dependencies": {"@element-plus/icons-vue": "^2.0.10", "@form-create/element-ui": "^3.1.18", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.3.3", "countup.js": "^2.4.2", "cropperjs": "^1.5.13", "echarts": "^5.4.1", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.2.32", "js-cookie": "^3.0.1", "js-table2excel": "^1.0.3", "jsplumb": "^2.15.6", "mitt": "^3.0.0", "nprogress": "^0.2.0", "pinia": "^2.0.32", "print-js": "^1.6.0", "qrcodejs2-fixes": "^0.0.2", "qs": "^6.11.0", "screenfull": "^6.0.2", "sortablejs": "^1.15.0", "splitpanes": "^3.1.5", "ts-enum-util": "^4.0.2", "vue": "^3.3.8", "vue-clipboard3": "^2.0.0", "vue-grid-layout": "^3.0.0-beta1", "vue-i18n": "^9.2.2", "vue-router": "^4.1.6", "vuedraggable": "^4.1.0"}, "devDependencies": {"@iconify/vue": "^4.1.0", "@types/node": "^18.14.0", "@types/nprogress": "^0.2.0", "@types/sortablejs": "^1.15.0", "@typescript-eslint/eslint-plugin": "^5.53.0", "@typescript-eslint/parser": "^5.53.0", "@vitejs/plugin-vue": "^4.0.0", "@vue/compiler-sfc": "^3.2.47", "autoprefixer": "^10.4.13", "eslint": "^8.34.0", "eslint-plugin-vue": "^9.9.0", "postcss": "^8.4.21", "prettier": "^2.8.4", "sass": "^1.58.3", "tailwindcss": "^3.2.7", "typescript": "^4.9.5", "vite": "^4.1.4", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-eslint-parser": "^9.1.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "bugs": {"url": "https://gitee.com/lyt-top/vue-next-admin/issues"}, "engines": {"node": ">=16.0.0", "npm": ">= 7.0.0"}, "keywords": ["vue", "vue3", "vuejs/vue-next", "element-ui", "element-plus", "vue-next-admin", "next-admin"], "repository": {"type": "git", "url": "https://gitee.com/lyt-top/vue-next-admin.git"}}