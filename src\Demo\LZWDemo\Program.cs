﻿using NewLife;
using NewLife.Log;

using Pek.Helpers;
using Pek.Timing;

public class LZW {
    // 压缩算法
    public List<int> Compress(string uncompressed, int key)
    {
        var dictionary = new Dictionary<string, int>();
        for (int i = 0; i < 256; i++)
        {
            dictionary.Add(((char)i).ToString(), i);
        }

        string w = string.Empty;
        var result = new List<int>();

        foreach (char c in uncompressed)
        {
            string wc = w + c;
            if (dictionary.ContainsKey(wc))
            {
                w = wc;
            }
            else
            {
                result.Add(dictionary[w] ^ key); // 加密
                dictionary[wc] = dictionary.Count;
                w = c.ToString();
            }
        }

        if (!string.IsNullOrEmpty(w))
        {
            result.Add(dictionary[w] ^ key); // 加密
        }

        return result;
    }

    // 解压缩算法
    public string Decompress(List<int> compressed, int key)
    {
        var dictionary = new Dictionary<int, string>();
        for (int i = 0; i < 256; i++)
        {
            dictionary.Add(i, ((char)i).ToString());
        }

        int firstCode = compressed[0] ^ key; // 解密
        string w = dictionary[firstCode];
        compressed.RemoveAt(0);
        var result = new StringWriter();
        result.Write(w);

        foreach (int k in compressed)
        {
            int decryptedK = k ^ key; // 解密
            string entry;
            if (dictionary.ContainsKey(decryptedK))
            {
                entry = dictionary[decryptedK];
            }
            else if (decryptedK == dictionary.Count)
            {
                entry = w + w[0];
            }
            else
            {
                throw new ArgumentException("Compressed k is invalid.");
            }

            result.Write(entry);
            dictionary[dictionary.Count] = w + entry[0];
            w = entry;
        }

        return result.ToString();
    }

    // 位压缩
    public byte[] BitCompress(List<int> compressed)
    {
        using (var ms = new MemoryStream())
        using (var bw = new BinaryWriter(ms))
        {
            foreach (int code in compressed)
            {
                bw.Write((ushort)code); // 使用ushort存储
            }
            return ms.ToArray();
        }
    }

    // 位解压缩
    public List<int> BitDecompress(byte[] compressed)
    {
        var result = new List<int>();
        using (var ms = new MemoryStream(compressed))
        using (var br = new BinaryReader(ms))
        {
            while (br.BaseStream.Position != br.BaseStream.Length)
            {
                result.Add(br.ReadUInt16()); // 读取ushort
            }
        }
        return result;
    }
}

// 示例使用
class Program {
    static void Main()
    {
        XTrace.UseConsole();

        LZW lzw = new LZW();

        string text = "kpQ9LMXW009|a123DNmJqZdJ7fvp|adsfb35tasd|ERsL6EVjuMUNmFkW|1HC21Q4eWjz2imt6";
        int key = GetKeyFromText(text); // 获取加密密钥
        Console.WriteLine("原始字符串: " + text);
        Console.WriteLine("密钥: " + key);

        // 压缩
        var compressed = lzw.Compress(text, key);
        Console.WriteLine("压缩结果: " + string.Join(", ", compressed));

        // 位压缩
        var bitCompressed = lzw.BitCompress(compressed);
        Console.WriteLine("位压缩结果: " + BitConverter.ToString(bitCompressed));

        // 位解压缩
        var bitDecompressed = lzw.BitDecompress(bitCompressed);
        Console.WriteLine("位解压缩结果: " + string.Join(", ", bitDecompressed));

        // 解压缩
        var decompressed = lzw.Decompress(bitDecompressed, key);
        Console.WriteLine("解压缩结果: " + decompressed);

        XTrace.WriteLine($"------------------");
        XTrace.WriteLine($"------------------");

        key = GetKeyFromText("T4TztQ48tTSvGGtMool6U7lepHgqtUHq");  // 取Key
        text = $"IdentityId=0HNBENR07M3D87312344559144628224,AppTime={UnixTime.ToTimestamp(DateTime.Now)}";  // 要加密的内容
        compressed = lzw.Compress(text, key);  // 压缩
        XTrace.WriteLine($"压缩结果：{Base64Helper.StringToBase64(compressed.Join())}");  // base64编码
    }

    static int GetKeyFromText(string text)
    {
        var frequency = new Dictionary<char, int>();

        foreach (var c in text)
        {
            if (frequency.ContainsKey(c))
                frequency[c]++;
            else
                frequency[c] = 1;
        }

        var top3 = frequency
                    .OrderByDescending(kvp => kvp.Value) // 按频次降序排列
                    .ThenBy(kvp => kvp.Key) // 如果频次相同则按ASCII码升序排列
                    .Take(3)
                    .Select(kvp => (int)kvp.Key)
                    .ToArray();

        return top3.Sum();
    }

}
