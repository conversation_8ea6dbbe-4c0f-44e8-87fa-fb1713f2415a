﻿@{
    Html.AppendTitleParts(T("管理员管理").Text);
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }
</style>

<form class="layui-form dg-form">
    <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width: auto">@T("用户名")：</label>
            <div class="layui-input-inline">
                <input type="text" name="name" id="name" placeholder="@T("请输入用户名")" autocomplete="off" class="layui-input" lay-filter="name">
            </div>
        </div>

    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetpageAdmin")'
            , page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 120
            , cols: [[
                { field: 'ID', width: 80, title: '@T("ID")', sort: true }
                , { field: 'Name', width: 120, title: '@T("用户名")' }
                , { field: 'Sex', width: 80, title: '@T("性别")', sort: true, width: 92 }
                , { field: 'RoleName', title: '@T("角色")', sort: true }
                , { field: 'LastLoginIP', title: '@T("最后登录ip")', sort: true }
                , { field: 'RegisterTime', title: '@T("注册时间")' }
                , { title: '@T("操作")', templet: '#tool', width: 150 }
            ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-100'
            , id: 'tables'
        });

        window.active = {
            reload: function () {
                table.reload('tables',
                    {
                        where: {
                            name: $("#name").val(),
                        },
                        page: {
                            curr: 1
                        }
                    });
            }
        }

        $("#name").on("input", function (e) {
            active.reload();
        });

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === 'detail') {
                layer.msg('ID：' + data.ID + ' @T("的查看操作")');
            } else if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', function (index) {
                    $.post('@Url.Action("MemberDelete")', { Id: data.ID }, function (data) {
                        if (data.success) {
                            abp.notify.success('@T("删除成功")');
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            } else if (obj.event === 'edit') {
                var dgheight = "480px";
                if (data.ID > 1) {
                    dgheight = "541px";
                }

                // os.OpenNoTop
                layuiIndex = os.Open('@T("编辑管理员")', "@Url.Action("EditUser")/" + data.ID, '420px', dgheight, function () {
                    if ($("#state").val() == 1) abp.notify.success("@T("编辑成功")");
                    active.reload();
                });
            }
        });

        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'add') {
                // os.OpenNoTop
                layuiIndex = os.Open('@T("新增管理员")', "@Url.Action("EditUser")", '420px', '541px', function (layero, index) {
                    if ($("#state").val() == 1) abp.notify.success("@T("新增成功")");
                    active.reload();
                });
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });

    });
</script>

<script type="text/html" id="tool">
    {{# if(d.ID == 1) { }}
    @if (this.Has((PermissionFlags)4))
    {
                <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="edit">@T("编辑")</a>
    }
    {{# } else { }}
    @if (this.Has((PermissionFlags)4))
    {
                <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="edit">@T("编辑")</a>
    }
    @if (this.Has((PermissionFlags)8))
    {
                <a class="pear-btn pear-btn-danger pear-btn-xs" lay-event="del">@T("删除")</a>
    }
    {{# } }}
</script>

<script type="text/html" id="user-toolbar">
    @if (this.Has((PermissionFlags)2)){
    <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        @T("新增")
    </button>
    }
</script>