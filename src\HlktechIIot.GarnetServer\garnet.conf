{
	/**********************************************************************************************************/
	/*                                      Garnet Configuration File                                         */
	/**********************************************************************************************************/
    /* Use this file to configure any values different from the defaults specified in libs/host/defaults.conf */
	/*                                                                                                        */
	/* Example:                                                                                               */
	/* "Port" : 1234,                                                                                         */
	/* "Address" : "*******",                                                                                 */
	/* "MemorySize" : "32g",                                                                                  */
	/* ...                                                                                                    */
	/*                                                                                                        */
	/* In order to use this file, run GarnetServer with the --config-import-path command line argument        */
	/* (e.g. GarnetServer.exe --config-import-path /path/to/garnet.conf).                                     */
    /**********************************************************************************************************/


/******************************************************************************************************/
/*                                  Garnet Default Configuration File                                 */
/******************************************************************************************************/

	/* Port to run server on */
	"Port" : 3278,

	/* IP address to bind server to (default: any) */
	"Address" : null,

	/* Total log memory used in bytes (rounds down to power of 2) */
	"MemorySize" : "16g",

	/* Size of each page in bytes (rounds down to power of 2) */
	"PageSize" : "32m",

	/* Size of each log segment in bytes on disk (rounds down to power of 2) */
	"SegmentSize" : "1g",

	/* Size of hash index in bytes (rounds down to power of 2) */
	"IndexSize" : "8g",

	/* Max size of hash index in bytes (rounds down to power of 2) */
	"IndexMaxSize": "",

	/* Percentage of log memory that is kept mutable */
	"MutablePercent" : 90,

	/* Total object store log memory used including heap memory in bytes */
	"ObjectStoreTotalMemorySize" : "",

	/* Object store log memory used in bytes excluding heap memory */
	"ObjectStoreLogMemorySize" : "32m",

	/* Size of each object store page in bytes (rounds down to power of 2) */
	"ObjectStorePageSize" : "1m",

	/* Size of each object store log segment in bytes on disk (rounds down to power of 2) */
	"ObjectStoreSegmentSize" : "32m",

	/* Size of object store hash index in bytes (rounds down to power of 2) */
	"ObjectStoreIndexSize" : "1g",

	/* Max size of object store hash index in bytes (rounds down to power of 2) */
	"ObjectStoreIndexMaxSize": "",

	/* Percentage of object store log memory that is kept mutable */
	"ObjectStoreMutablePercent" : 90,

	/* Enable tiering of records (hybrid log) to storage, to support a larger-than-memory store. Use --logdir to specify storage directory. */
	"EnableStorageTier" : false,

	/* When records are read from the main store's in-memory immutable region or storage device, copy them to the tail of the log. */
	"CopyReadsToTail" : false,

	/* When records are read from the object store's in-memory immutable region or storage device, copy them to the tail of the log. */
	"ObjectStoreCopyReadsToTail" : false,

	/* Storage directory for tiered records (hybrid log), if storage tiering (--storage) is enabled. Uses current directory if unspecified. */
	"LogDir" : null,

	/* Storage directory for checkpoints. Uses logdir if unspecified. */
	"CheckpointDir" : null,

	/* Recover from latest checkpoint and log, if present. */
	"Recover" : true,

	/* Disable pub/sub feature on server. */
	"DisablePubSub" : false,

	/* Enable incremental snapshots. */
	"EnableIncrementalSnapshots" : false,

	/* Page size of log used for pub/sub (rounds down to power of 2) */
	"PubSubPageSize" : "4k",

	/* Disable support for data structure objects. */
	"DisableObjects" : false,

	/* Enable cluster. */
	"EnableCluster" : false,

	/* Start with clean cluster config. */
	"CleanClusterConfig" : false,

	/* Authentication mode of Garnet. This impacts how AUTH command is processed and how clients are authenticated against Garnet. Value options: NoAuth, Password, Aad, ACL*/
	"AuthenticationMode" : "NoAuth",

	/* Authentication string for password authentication. */
	"Password" : null,

	/* Username to authenticate intra-cluster communication with. */
	"ClusterUsername" : null,

	/* Password to authenticate intra-cluster communication with. */
	"ClusterPassword" : null,

	/* External ACL user file. */
	"AclFile" : null,

	/* The authority of AAD authentication. */
	"AadAuthority" : "https://login.microsoftonline.com",

	/* The audiences of AAD token for AAD authentication. Should be a comma separated string. */
	"AadAudiences" : null,

	/* The issuers of AAD token for AAD authentication. Should be a comma separated string. */
	"AadIssuers" : null,

	/* The authorized client app Ids for AAD authentication. Should be a comma separated string. */
	"AuthorizedAadApplicationIds" : null,

	/* Enable write ahead logging (append-only file). */
	"EnableAOF" : true,

	/* Total AOF memory buffer used in bytes (rounds down to power of 2) - spills to disk after this limit */
	"AofMemorySize" : "64m",

	/* Size of each AOF page in bytes(rounds down to power of 2) */
	"AofPageSize" : "4m",

	/* Write ahead logging (append-only file) commit issue frequency in milliseconds. 0 = issue an immediate commit per operation, -1 = manually issue commits using COMMITAOF command */
	"CommitFrequencyMs" : 0,

	/* Wait for AOF to flush the commit before returning results to client. Warning: will greatly increase operation latency. */
	"WaitForCommit" : false,

	/* Maximum size of AOF (rounds down to power of 2) after which unsafe truncation will be applied. Left empty AOF will grow without bound unless a checkpoint is taken */
	"AofSizeLimit" : "",

	/* Background hybrid log compaction frequency in seconds. 0 = disabled (compaction performed before checkpointing instead) */
	"CompactionFrequencySecs" : 0,

	/* Hybrid log compaction type. Value options: */ 
	/* None - No compaction */
	/* Shift - shift begin address without compaction (data loss) */ 
	/* ShiftForced - shift begin address without compaction (data loss). Immediately deletes files - do not use if you plan to recover after failure */ 
	/* Scan - scan old pages and move live records to tail (no data loss - take a checkpoint to actually delete the older data files from disk) */
	/* Lookup - Lookup each record in compaction range, for record liveness checking using hash chain (no data loss - take a checkpoint to actually delete the older data files from disk) */
	"CompactionType" : "None",

	/* Number of log segments created on disk before compaction triggers. */
	"CompactionMaxSegments" : 32,

	/* Number of object store log segments created on disk before compaction triggers. */
	"ObjectStoreCompactionMaxSegments" : 32,

	/* Percent of cluster nodes to gossip with at each gossip iteration. */
	"GossipSamplePercent" : 100,

	/* Cluster mode gossip protocol per node sleep (in seconds) delay to send updated config. */
	"GossipDelay" : 5,

	/* Cluster node timeout is the amount of seconds a node must be unreachable. */
	"ClusterTimeout" : 60,

	/* Name for the client target host when using TLS connections in cluster mode. */
	"ClusterTlsClientTargetHost" : "GarnetTest",

	/* Enable TLS. */
	"EnableTLS" : false,

	/* TLS certificate file name (example: testcert.pfx). */
	"CertFileName" : null,

	/* TLS certificate password (example: placeholder). */
	"CertPassword" : null,

	/* TLS certificate subject name. */
	"CertSubjectName" : null,

	/* TLS certificate refresh frequency in seconds (0 to disable). */
	"CertificateRefreshFrequency" : 0,

	/* Whether TLS client certificate required. */
	"ClientCertificateRequired" : true,

	/* Certificate revocation check mode for certificate validation (NoCheck, Online, Offline). */
	"CertificateRevocationCheckMode" : "NoCheck",

	/* Full path of file with issuer certificate for validation. If empty or null, validation against issuer will not be performed. */
	"IssuerCertificatePath" : "",

	/* Track latency of various events. */
	"LatencyMonitor" : false,

	/* Metrics sampling frequency in seconds. Value of 0 disables metrics monitor task. */
	"MetricsSamplingFrequency" : 0,

	/* Enabling quiet mode does not print server version and text art. */
	"QuietMode" : false,

	/* Logging level. Value options: Trace, Debug, Information, Warning, Error, Critical, None */
	"LogLevel" : "Warning",

	/* Disable console logger. */
	"DisableConsoleLogger" : false,

	/* Enable file logger and write to the specified path. */
	"FileLogger" : null,

	/* Minimum worker and completion threads in thread pool, 0 uses the system default. */
	"ThreadPoolMinThreads" : 0,

	/* Maximum worker and completion threads in thread pool, 0 uses the system default. */
	"ThreadPoolMaxThreads" : 0,

	/* Use Azure Page Blobs for storage instead of local storage. */
	"UseAzureStorage" : false,

	/* The connection string to use when establishing connection to Azure Blobs Storage. */
	"AzureStorageConnectionString" : null,

	/* Whether and by how much should we throttle the disk IO for checkpoints: -1 - disable throttling; >= 0 - run checkpoint flush in separate task, sleep for specified time after each WriteAsync */
	"CheckpointThrottleFlushDelayMs" : 0,

	/* Use FastCommit when writing AOF. */
	"EnableFastCommit" : false,

	/* Throttle FastCommit to write metadata once every K commits. */
	"FastCommitThrottleFreq" : 1000,

	/* Throttle the maximum outstanding network sends per session. */
	"NetworkSendThrottleMax" : 8,

	/* Whether we use scatter gather IO for MGET or a batch of contiguous GET operations - useful to saturate disk random read IO. */
	"EnableScatterGatherGet" : false,

	/* Whether and by how much (milliseconds) should we throttle the replica sync: 0 - disable throttling */
	"ReplicaSyncDelayMs" : 5,

	/* Use main-memory replication model. */
	"MainMemoryReplication" : false,

	/* Used with main-memory replication model. Take on demand checkpoint to avoid missing data when attaching */
	"OnDemandCheckpoint" : false,

	/* With main-memory replication, use null device for AOF. Ensures no disk IO, but can cause data loss during replication. */
	"UseAofNullDevice" : false,

	/* Use native device on Linux for local storage */
	"UseNativeDeviceLinux" : false,

	/* #,#,...,#: For the main store, the sizes of records in each revivification bin, in order of increasing size. Supersedes the default --enable-reviv; cannot be used with --reviv-in-chain-only */
	"RevivBinRecordSizes" : null,

	/* #,#,...,#: For the main store, the number of records in each bin: Default (not specified): If reviv-bin-record-sizes is specified, each bin is 256 records # (one value): If reviv-bin-record-sizes is specified, then all bins have this number of records, else error #,#,...,# (multiple values): If reviv-bin-record-sizes is specified, then it must be the same size as that array, else error Supersedes the default --enable-reviv; cannot be used with --reviv-in-chain-only */
	"RevivBinRecordCounts" : null,

	/* #: Fraction of mutable in-memory log space, from the highest log address down to the read-only region, that is eligible for revivification. Applies to both main and object store. */
	"RevivifiableFraction" : 1.0,

	/* A shortcut to specify revivification with default power-of-2-sized bins. This default can be overridden by --reviv-in-chain-only or by the combination of reviv-bin-record-sizes and reviv-bin-record-counts. */
	"EnableRevivification" : false,

	/* Search this number of next-higher bins if the search cannot be satisfied in the best-fitting bin. Requires --enable-reviv or the combination of rconeviv-bin-record-sizes and reviv-bin-record-counts */
	"RevivNumberOfBinsToSearch" : 0,

	/* Number of records to scan for best fit after finding first fit. Requires --enable-reviv or the combination of reviv-bin-record-sizes and reviv-bin-record-counts 0: Use first fit #: Limit scan to this many records after first fit, up to the record count of the bin */
	"RevivBinBestFitScanLimit" : 0,

	/* Revivify tombstoned records in tag chains only (do not use free list). Cannot be used with reviv-bin-record-sizes or reviv-bin-record-counts. Propagates to object store by default. */
	"RevivInChainOnly" : false,

	/* Number of records in the single free record bin for the object store. The Object store has only a single bin, unlike the main store. Ignored unless the main store is using the free record list. */
	"RevivObjBinRecordCount" : 256,

	/* Limit of items to return in one iteration of *SCAN command */
	"ObjectScanCountLimit" : 1000,

	/* List of directories on server from which custom command binaries can be loaded by admin users */
	"ExtensionBinPaths": null,

	/* Allow loading custom commands from digitally unsigned assemblies (not recommended) */
	"ExtensionAllowUnsignedAssemblies": false,

	/* Index resize check frequency in seconds */
	"IndexResizeFrequencySecs": 60,

	/* Overflow bucket count over total index size in percentage to trigger index resize */
	"IndexResizeThreshold": 50,
}