﻿@using System.Security.Cryptography
@using System.Text
@{
    Html.AppendTitleParts(T("设备管理").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");

    var calculateMenuId = new Func<string, string, string>((Name, Url) =>
    {
        using (var md5 = MD5.Create())
        {
            var hashTarget = string.IsNullOrEmpty(Url) ? Name : Url.ToUpper();
            var md5ComputeHash = md5.ComputeHash(Encoding.UTF8.GetBytes(hashTarget));
            var bitConverterResult = BitConverter.ToString(md5ComputeHash);
            var replaceResult = bitConverterResult.Replace("-", "");
            return replaceResult;
        }
    });

    var url = Url.Action("Index", "Products", new { area = HlktechIoT.Areas.Device.DeviceArea.AreaName });

    var width = 0;
    if (this.Has((PermissionFlags)1)) {
        width += 128;
    }
    if (this.Has((PermissionFlags)16)) {
        width += 64;
    }
    if (this.Has((PermissionFlags)128)) {
        width += 128;
    }
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    .layui-layer-title {
        background-color: #fff;
    }

    .dtree-nav-item {
        padding-left: 0;
    }

    .layui-table-tool {
        z-index: 1;
    }

    .online {
        color: 'mediumaquamarine' !important;
    }

    .unline {
        color: 'gray' !important;
    }

    /* 操作列自适应样式 */
    .layui-table-fixed-r .layui-table-body {
        overflow-x: hidden !important;
    }

    .operation-column {
        white-space: nowrap;
        padding: 0 5px; /* 添加左右边距 */
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center; /* 垂直居中 */
        flex-wrap: nowrap;
        height: 100%; /* 确保容器占满单元格高度 */
        width: 100%; /* 确保容器占满单元格宽度 */
        box-sizing: border-box; /* 确保内边距不增加总宽度 */
    }

        .operation-column .pear-btn {
            margin-right: 6px; /* 保持原有按钮间距 */
            padding: 0 8px; /* 保持按钮内边距 */
            height: 30px; /* 保持按钮高度 */
            line-height: 30px; /* 行高与高度一致 */
            min-width: 44px; /* 按钮最小宽度 */
            text-align: center;
            flex-grow: 1; /* 按钮自动扩展占用空间 */
            flex-basis: 0; /* 所有按钮基础宽度相同 */
        }

            .operation-column .pear-btn:last-child {
                margin-right: 0;
            }

    /* 确保表格单元格内容居中 */
    .layui-table-cell {
        text-align: center;
        padding: 0 2px; /* 保持单元格内边距 */
    }

    /* 固定列样式优化 */
    .layui-table-fixed {
        height: auto !important;
    }

    /* 确保表格右侧固定列正确显示 */
    .layui-table-fixed-r {
        right: 0 !important;
    }

        /* 优化表格右侧固定列的单元格 */
        .layui-table-fixed-r .layui-table-cell {
            padding: 0;
        }
</style>

<form class="layui-form dg-form">
    <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width: auto">@T("关键词")：</label>
            <div class="layui-input-inline">
                <input type="text" name="key" id="key" placeholder="@T("名称、编码等")" autocomplete="off" class="layui-input" lay-filter="key">
            </div>
        </div>
        <div class="layui-inline select">
            <label class="layui-form-label" style="padding-top: 10px;">@T("产品")：</label>
            <div class="layui-input-inline select">
                <input type="hidden" value="0" id="PId" name="PId" />
                <div id="demo1" style=" width: 100%;"></div>
            </div>
        </div>
        <div class="layui-inline select">
            <label class="layui-form-label" style="width: auto;margin-top:5px;">@T("状态")：</label>
            <div class="layui-input-inline select">
                <input type="hidden" id="status" name="status" />
                <div id="demo2" style=" width: 100%;"></div>
            </div>
        </div>
    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    $("body").on("click", function (event) {
        $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
        $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon', 'dtree'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;
        var dtree = layui.dtree;

         // 按钮配置集中定义
        var operationButtons = [
            @if (this.Has((PermissionFlags)1))
            {
                @:{ text: '@T("查看")', event: 'details', class: 'pear-btn-primary' },
            }
            @if (this.Has((PermissionFlags)512))
            {
                @:{ text: '@T("续期")', event: 'renewal', class: 'pear-btn-danger' },
            }
            @if (this.Has((PermissionFlags)16))
            {
                @:{ text: '@T("升级")', event: 'upgrade', class: 'pear-btn-warming' },
            }
            @if (this.Has((PermissionFlags)128))
            {
                @:{ text: '@T("调试")', event: 'debug', class: 'pear-btn-danger' },
            }
            @if (this.Has((PermissionFlags)1))
            {
                @:{ text: '@T("用户")', event: 'BindUser', class: 'pear-btn-warming' },                 
            }
        ];

        // 将按钮配置保存到全局变量，供模板使用
        window.operationButtons = operationButtons;

        // 动态计算操作列宽度的函数
        function calculateOperationColumnWidth() {
            try {
                var buttonMargin = 6; // 按钮间距
                var extraPadding = 24; // 额外边距
                var baseButtonPadding = 20; // 按钮内边距

                if (operationButtons.length === 0) {
                    return 100;
                }

                var totalWidth = 0;

                operationButtons.forEach(function(button) {
                    // 调整文字宽度估算：中文字符约16px，英文字符约9px
                    var text = button.text;
                    var textWidth = 0;
                    for (var i = 0; i < text.length; i++) {
                        var char = text.charAt(i);
                        if (/[\u4e00-\u9fa5]/.test(char)) {
                            textWidth += 16; // 中文字符
                        } else {
                            textWidth += 9;  // 英文字符
                        }
                    }

                    var buttonWidth = textWidth + baseButtonPadding;
                    totalWidth += buttonWidth;
                });

                // 加上按钮间距和额外边距
                totalWidth += (operationButtons.length - 1) * buttonMargin + extraPadding;

                // 设置最小宽度
                if (totalWidth < 120) {
                    totalWidth = 120;
                }

                console.log('计算的操作列宽度:', totalWidth, '按钮数量:', operationButtons.length, '按钮配置:', operationButtons);
                return totalWidth;
            } catch (error) {
                console.error('计算操作列宽度时出错:', error);
                return 200;
            }
        }

        // 应用操作列宽度的函数
        function applyOperationColumnWidth() {
            try {
                var calculatedWidth = calculateOperationColumnWidth();

                var fixedRightHeader = $('.layui-table-fixed-r .layui-table-header table thead tr th:last-child');
                var fixedRightBody = $('.layui-table-fixed-r .layui-table-body table tbody tr td:last-child');

                if (fixedRightHeader.length > 0) {
                    fixedRightHeader.css('width', calculatedWidth + 'px');
                    fixedRightHeader.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                if (fixedRightBody.length > 0) {
                    fixedRightBody.css('width', calculatedWidth + 'px');
                    fixedRightBody.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                var mainTableHeader = $('#tablist').next('.layui-table-view').find('.layui-table-header table thead tr th:last-child');
                var mainTableBody = $('#tablist').next('.layui-table-view').find('.layui-table-body table tbody tr td:last-child');

                if (mainTableHeader.length > 0) {
                    mainTableHeader.css('width', calculatedWidth + 'px');
                    mainTableHeader.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                if (mainTableBody.length > 0) {
                    mainTableBody.css('width', calculatedWidth + 'px');
                    mainTableBody.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                console.log('已应用操作列宽度:', calculatedWidth);
            } catch (error) {
                console.error('应用操作列宽度时出错:', error);
            }
        }

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetList")'
            , page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 80
            , cols: [[
                { type: 'checkbox' },
                { field: 'Code', title: '@T("DeviceName")', minWidth: 120 }
                , { field: 'NickName', title: '@T("昵称")', minWidth: 120 }
                , {
                    title: '@T("ProductId")', minWidth: 150, align: 'center', templet: (d) => {

                        return `<div data-row_id=${d.ProductId} data-field="Product" style="cursor: pointer;">` + d.ProductId + '</div>';
                    }
                }
                , {
                    title: '@T("状态")', width: 90, templet: (d) => {
                        // console.log(d);
                        if (d.RegisterTime === true) {
                            if (d.Online === true) {
                            return '<span style="color:mediumseagreen !important;">@T("在线")<i class="layui-icon layui-icon-rss" style="margin-left:1px"></i></span>'
                            }
                            return '<span style="color:gray !important;">@T("离线")<i class="layui-icon layui-icon-close" style="color:red;margin-left:2px"></i></span>'
                        }
                        return '<span style="color:gray !important;">@T("未激活")</span>'
                    }
                }
                , { field: 'Enable', title: '@T("启用")', templet: '#switchTpl', width: 90 }
                , { field: 'Version', title: '@T("版本")', width: 180 }
                , { field: 'ProductModule', title: '@T("产品型号")', width: 120 }
                // , { field: 'PostPeriod', title: '@T("上报间隔")', width: 120 }
                , {
                    field: 'LastLogin', title: '@T("最后上线时间")', width: 160, templet: function (d) {
                        if (d.LastLogin && !/^0001/.test(d.LastLogin)) {
                            return d.LastLogin;
                        }
                        return "";
                    }
                }
                 , { field: 'ExpiredTime', title: '@T("授权到期时间")', width: 160 }
                , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: calculateOperationColumnWidth() }
            ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-100'
            , id: 'tables'
            , done: function (res, curr, count) {
                try {
                    // 延迟执行，确保DOM已经渲染完成
                    setTimeout(function() {
                        applyOperationColumnWidth();
                        console.log('表格渲染完成，已应用操作列样式');
                    }, 200);
                } catch (error) {
                    console.error('表格done回调中出错:', error);
                }

                $("div[data-field='Product']").each(function () {
                    // 在这里执行您想要的操作
                    $(this).css("cursor", "pointer") //设置鼠标移入变形

                    $(this).click(function () {
                        let UserCount_CurrentRow_Id = $(this).attr('data-row_id')
                        window.Product(UserCount_CurrentRow_Id)
                    })
                });
        
            }
        });

        var demo1 = xmSelect.render({
            el: '#demo1',
            //radio: true,
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            clickClose: true,
            pageRemote: true,  // 分页
            multiple:false, // 开启多选功能
            remoteMethod: function (val, cb, show, pageIndex) {  // 远程方法
                var obj = [];

                // 接口数据
                $.post('@Url.Action("SearchProduct")', { keyword: val, page: pageIndex }, function (res) {
                    if (res.success) {
                        if (res.data != null) {
                            cb(res.data, res.extdata);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });

            },
            on: function (data) {  // 监听选择
                // console.log(data);
                if (data.arr.length > 0) {
                    var a = "";
                    for (var i = 0; i < data.arr.length; i++) {
                        if (i == 0) {
                            a = data.arr[i].value;
                        }
                        else {
                            a += "," + data.arr[i].value;
                        }
                    }
                    $("[name=PId]").val(a);
                }
                else {
                    $("[name=PId]").val("");
                }

                if (data.change.length > 0) {
                    active.reload();
                }
            }
        });
        // 静态搜索
        xmSelect.render({
            el: '#demo2',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            clickClose: true,
            multiple:false, // 开启多选功能
            data:[{name:'@T("全部")',value:-1},{name:'@T("未激活")',value:2},{name:'@T("在线")',value:0},{name:'@T("离线")',value:1},{name:'@T("已禁用")',value:3}],
            on: function (data) {  // 监听选择
                // console.log(data);
                // 手动处理单选
                if (data.arr.length>1) {
                    data.arr.splice(0,1)
                }
                if (data.arr.length > 0) {
                    var a = "";
                    for (var i = 0; i < data.arr.length; i++) {
                        if (i == 0) {
                            a = data.arr[i].value;
                        }
                        else {
                            a += "," + data.arr[i].value;
                        }
                    }
                    $("[name=status]").val(a);
                }
                else {
                    $("[name=status]").val("");
                }

                if (data.change.length > 0) {
                    active.reload();
                }
            }
        });

        window.active = {
            reload: function () {
                table.reload('tables',
                    {
                        page: {
                            curr: 1
                        },
                        where: {
                            key: $("#key").val(),
                            PId: $("#PId").val(),
                            status:$("#status").val(),
                        },
                        done: function(res, curr, count) {
                            try {
                                setTimeout(function() {
                                    applyOperationColumnWidth();
                                    console.log('表格重载完成，已重新应用操作列样式');
                                }, 200);
                            } catch (error) {
                                console.error('表格重载done回调中出错:', error);
                            }
                        }
                    });
            }
        }


        // 监听窗口大小变化
        $(window).on('resize', function() {
            try {
                setTimeout(function() {
                    applyOperationColumnWidth();
                    console.log('窗口大小变化，已重新调整操作列宽度');
                }, 100);
            } catch (error) {
                console.error('窗口resize时出错:', error);
            }
        });

        $("#key").on("input", function (e) {
            active.reload();
        });
        
        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("Delete")', { Id: data.Id }, function (data) {
                        if (data.success) {
                            abp.notify.success(data.msg);
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            } else if (obj.event === 'details') {
                window.details(data);
            } else if (obj.event === 'edit') {
                window.edit(data);
            } else if (obj.event === 'upgrade') {
                window.upgrade(data);
            } else if (obj.event === 'debug') {
                window.debug(data);
            } else if (obj.event === 'BindUser') {
                window.BindUser(data)
            } else if (obj.event === 'productFunction') {
                window.productFunction(data);
            } else if (obj.event === 'Product') {
                window.Product(data);
            }else if (obj.event === 'renewal') {
                window.renewal(data);
            }
        });

        window.renewal = function (data) {
            top.layui.dg.popupRight({
                id: 'Renewal'
                , title: ' @T("续期")' + '(' + data.Code + ')'
                , closeBtn: 1
                , area: ['1080px']
                , success: function () {
                    localStorage.setItem('myData', 2);
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Renewal")' + abp.utils.formatString("?id={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }

        window.saveCallback = function (data) {
            parent.layer.close(data.index);
            abp.notify.success(data.msg);
            table.reload("tables");
        }

        window.details = function (data) {
            top.layui.dg.popupRight({
                id: 'DeviceDetails'
                , title: ' @T("查看")' + '(' + data.Code + ')'
                , closeBtn: 1
                , area: ['1080px']
                , success: function () {
                    localStorage.setItem('myData', 2);
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Details")' + abp.utils.formatString("?code={0}", data.Code) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }

        window.debug = function (data) {
            top.layui.dg.popupRight({
                id: 'DeviceDebug'
                , title: ' @T("调试")' + '(' + data.Code + ')'
                , closeBtn: 1
                , area: ['980px']
                , success: function (obj, index) {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Debugging")/' + data.Id + '" frameborder="0" class="layadmin-iframe" name="iframe' + index + '"></iframe>');
                }
            });
        }

        window.upgrade = function (data) {
            parent.layer.open({
                type: 2,
                title: "@T("固件升级")" + '(' + data.Code + ')',
                content: "@Url.Action("Upgrade")" + abp.utils.formatString("?code={0}", data.Code),
                area: ["710px", "536px"],
                shade: 0.1,
                btn: ['@T("确定")', '@T("取消")'],
                yes: function (index, layero) {
                    parent.window['layui-layer-iframe' + index].submitForm();
                }
            });
        }

        window.BindUser = function (data) {
            top.layui.dg.popupRight({
                id: 'BindUser'
                , title: ' @T("设备绑定用户")'
                , closeBtn: 1
                , area: ['780px']
                , success: function () {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("BindUser")/' + data.Id + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }

        window.Product = function (data) {
            parent.layui.admin.closeTab('@calculateMenuId("产品管理(设备)", url!)');
            parent.layui.admin.addTab('@calculateMenuId("产品管理(设备)", url!)', '@T("产品管理(设备)")', '@url' + abp.utils.formatString("?key={0}", data));
        }

        //监听开关事件
        form.on('switch(statusedit)', function (data) {
            //开关是否开启，true或者false
            var checked = data.elem.checked;
            //获取所属属性值
            var id = data.elem.attributes['data-id'].nodeValue;
            $.post('@Url.Action("ModifyState")', { id: id, status: checked }, function (res) {
                if (res.success) {
                    abp.notify.success(res.msg);
                }
                else {
                    abp.notify.error(res.msg);
                }
            });
        });

        window.warning = function (msg) {
            os.warning(msg);
        }
    });
</script>

<script type="text/html" id="tool">
    <div class="operation-column">
    {{#  layui.each(window.operationButtons, function(index, button){ }}
        <a class="pear-btn {{button.class}} pear-btn-xs" lay-event="{{button.event}}">{{button.text}}</a>
    {{#  }); }}
    </div>
</script>

<script type="text/html" id="online">
    {{# if(d.Online) { }}
      <i class="layui-icon layui-icon-ok" style="color: #16b777"></i>
    {{# } else { }}
      <i class="layui-icon layui-icon-close" style="color: #ff5722"></i>
    {{# } }}
</script>

<script type="text/html" id="switchTpl">
    @if (this.Has((PermissionFlags)4))
    {
        <input type="checkbox" name="status" data-id="{{d.Id}}" lay-skin="switch" lay-text="ON|OFF" lay-filter="statusedit" {{ d.Enable == "1" ? 'checked' : ''}}>
    }
    else
    {
        <input type="checkbox" name="status" data-id="{{d.Id}}" lay-skin="switch" lay-text="ON|OFF" lay-filter="statusedit" {{ d.Enable == "1" ? 'checked' : ''}} disabled>
    }
</script>

<script type="text/html" id="user-toolbar">
    @* <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        @T("上传")
    </button> *@
</script>