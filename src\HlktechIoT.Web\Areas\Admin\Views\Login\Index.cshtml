﻿@using DG.Web.Framework.Models
@inject IWorkContext workContext
@model LoginViewModel
@{
    Layout = null;

    var language = workContext.WorkingLanguage;

    var adminarea = YRY.Web.Controllers.Areas.Admin.AdminArea.AreaName.ToLower();
    var bgRnd = NewLife.Security.Rand.Next(1, 9);
    var ReturnUrl = Model!.ReturnUrl;

    var site = SiteInfo.FindDefault();

    var localizationSettings = LocalizationSettings.Current;
}

<!DOCTYPE html>
<html lang="@language.LanguageCulture" @(this.ShouldUseRtlTheme() ? Html.Raw(" dir=\"rtl\"") : null) @Html.DGPageCssClasses()>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>@T("登录") - @T($"{site?.SiteName}")</title>
    <link rel="shortcut icon" href="~/images/favicon.ico" asp-append-version="true">
    <link rel="stylesheet" href="~/libs/pear/css/pear.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/views/login.css" asp-append-version="true" />
    <style>
        @if (language.UniqueSeoCode == "en")
        {
            <text>
                .title
                {
                font-size: 21px !important;
                }
            </text>
        }
    </style>
    <script>
        var sslEnabled = @DHSetting.Current.SslEnabled;
        var currentURL = window.location.href;

        if (!currentURL.includes("localhost")) {
        if (sslEnabled == 1) {
        if (currentURL.startsWith("http://")) {
        var newURL = currentURL.replace(/http:\/\//, 'https://');
        window.location.href = newURL;
        }
        }
        else if (sslEnabled == 2) {
        if (currentURL.startsWith("https://")) {
        var newURL = currentURL.replace(/https:\/\//, 'http://');
        window.location.href = newURL;
        }
        }
        }
    </script>
    <script>
        var pleaseSelect = '@T("请选择")';
        var layuiPrint = '@T("打印")';
        var layuiExport = '@T("导出")';
        var layuiFilterColumn = '@T("筛选列")';
        var layuiArticlePage = '@T("条/页")';
        var layuiTotal = '@T("共")';
        var layuiBtn = '@T("确定")';
        var layuiGoPage = '@T("到第")';
        var layuiPage = '@T("页")';
        var layuiNumber = '@T("个")';
        var layuiPrev = '@T("上一页")';
        var layuiNext = '@T("下一页")';
        var layuiNoData = '@T("无数据")';
        var layuiAsc = '@T("升序")';
        var layuiDesc = '@T("降序")';
        var layuiCloseCurrent = '@T("关 闭 当 前")';
        var layuiCloseOther = '@T("关 闭 其 他")';
        var layuiCloseAll = '@T("关 闭 全 部")';
        var layuiMenuStyle = '@T("菜单风格")';
        var layuiTopStyle = '@T("顶部风格")';
        var layuiThemeColor = '@T("主题配色")';
        var layuiMoreSettings = '@T("更多设置")';
        var layuiOpen = '@T("开")';
        var layuiClose = '@T("关")';
        var layuiMenu = '@T("菜单")';
        var layuiView = '@T("视图")';
        var layuiBanner = '@T("通栏")';
        var layuiThroughColor = '@T("通色")';
        var layuiFooter = '@T("页脚")';
        var layuiSelectAll = '@T("全选")';
        var layuiClear = '@T("清空")';
        var layuiReverseSelection = '@T("反选")';
        var layuiPeeling = '@T("换肤")';
        var layuiNoDataYet = '@T("暂无数据")';
        var layuiSearch = '@T("搜索")';
        var layuiPrevious = '@T("上一页")';
        var layuiNext = '@T("下一页")';
        var layuiNotAllowClose = '@T("前页面不允许关闭")';
        var layuiOpenAtMost = '@T("最多打开")';
        var layuiTabs = '@T("个标签页")';
    </script>
</head>
<body style="background-image: url(/images/background.svg);background-color:#e5e5e5;">

    <div class="user-login">
        <div class="layui-container">
            <div class="layui-row">
                <div class="layui-col-md7 layui-col-sm6 layui-hide-xs">
                    <img src="~/images/guoqing.jpg" />
                </div>
                <div class="layui-col-sm6 layui-col-md5 layui-col-xs12">
                    <form class="layui-form" action="javascript:void(0);">
                        <div class="layadmin-user-login-body">
                            <input type="hidden" name="returnUrl" value="@ReturnUrl" />
                            <div class="layui-form-item">
                                <img class="logo" src="~/images/logo.png" />
                                <div class="title">@T($"{site?.SiteName}")</div>
                                <div class="desc">
                                    @T("一 万 年 太 久，只 争 朝 夕")
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layadmin-user-login-icon layui-icon layui-icon-username" for="LAY-user-login-username"></label>
                                <input type="text" name="username" placeholder="@T("用户名/邮箱/手机/编码")" hover class="layui-input dhvalidate" autofocus maxlength="256" id="LAY-user-login-username" />
                            </div>
                            <div class="layui-form-item">
                                <label class="layadmin-user-login-icon layui-icon layui-icon-password" for="LAY-user-login-password"></label>
                                <input type="password" name="password" placeholder="@T("密码")" hover class="layui-input dhvalidate" maxlength="32" id="LAY-user-login-password" />
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-col-xs7">
                                    <label class="layadmin-user-login-icon layui-icon layui-icon-vercode" for="LAY-user-login-vercode"></label>
                                    <input type="text" name="checkcode" placeholder="@T("图形验证码")" hover class="layui-input" id="LAY-user-login-vercode" />
                                </div>
                                <div class="layui-col-xs5">
                                    <div style="margin-left: 10px;">
                                        <img src="@DG.Setting.Current.CaptChaUrl" class="layadmin-user-login-codeimg" id="LAY-user-get-vercode" style="width: 100%; cursor: pointer;">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-form-item" style="display: flex; align-items: flex-end;">
                                <span style="display: inline-block;">
                                    <input type="checkbox" name="keeplogin" title="@T("记住密码")" lay-skin="primary" checked>
                                </span>
                                <span style="margin-left: auto; font-size: inherit; line-height: inherit;">
                                    @if (DG.Setting.Current.AllowManageRegister)
                                    {
                                        <a href="@Url.Action("Index", "Register")" style="margin-left: 5px;" target="_blank">@T("免费注册")</a>
                                    }
                                    @if (DG.Setting.Current.AllowManageRegister && DG.Setting.Current.AllowManageFindPassword)
                                    {
                                        @: |
                                    }
                                    @if (DG.Setting.Current.AllowManageFindPassword)
                                    {
                                        <a href="@Url.Action("Index", "FindPwd")" style="margin-right: 5px;" target="_blank">@T("忘记密码")</a>
                                    }
                                </span>
                            </div>
                            <div class="layui-form-item" style="margin-top: 20px !important;">
                                <button class="pear-btn pear-btn-primary login" lay-submit lay-filter="LAY-user-login-submit" id="dglogin">@T("登录")</button>
                            </div>
                            @if (localizationSettings.IsEnable)
                            {
                                <div class="layui-trans layui-form-item layadmin-user-login-other">
                                    @foreach (var item in languagelist)
                                    {
                                        <a href="?lang=@item.UniqueSeoCode" class="layadmin-link">@item.DisplayName</a>
                                    }
                                </div>
                            }
                            @*<div class="external-login">
                                <img src="~/images/miniprogram.png" title="小程序登录" alt="小程序登录" class="wx-logo">
                                </div>*@
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="user-login-foot">
        2009-2024 © IOT @DHSetting.Current.CurrentVersion
    </div>

    <script src="~/libs/layui/layui.js" asp-append-version="true"></script>
    <script src="~/libs/pear/pear.js" asp-append-version="true"></script>
    <script src="~/js/Storage.js" asp-append-version="true"></script>
    <script src="~/js/crypto.js" asp-append-version="true"></script>
    <script type="text/javascript">
        layui.use(['abp', 'form', 'element', 'jquery', 'dgcommon'], function () {
            var $ = layui.jquery;
            var abp = layui.abp;
            var form = layui.form;
            var element = layui.element;
            var os = layui.dgcommon;
            /** 计算hash  */
            const generateHash = function (str) {
                return CryptoJS.MD5(str).toString().toUpperCase();
            };
            form.render();

            if(window != top){
                top.location.href=location.href;
            }

            $("#LAY-user-get-vercode").on('click', function() {
                this.src = '@(DG.Setting.Current.CaptChaUrl)?t=' + new Date().getTime()
            });

            $('.dhvalidate').on('input', function () {
                var passwordValue = $(this).val();
                var trimmedValue = passwordValue.replace(/\s/g, '');
                $(this).val(trimmedValue);
            });

            form.on('submit(LAY-user-login-submit)', function (data) {
                data.field.keeplogin = data.field.keeplogin == "on";//默认值是on
                var field = data.field; //获取提交的字段

                if (field.username.length == 0) {
                    os.error('@T("账号不能为空")');
                    return;
                }

                if (field.password.length == 0) {
                    os.error('@T("密码不能为空")');
                    return;
                }

                if (field.checkcode.length == 0) {
                    os.error('@T("验证码不能为空")');
                    return;
                }
                field.password = generateHash(field.password);
                var waitIndex = layer.load(2);
                abp.ajax({
                    url: "@Url.Action("Login", "Login")",
                    data: field,
                    contentType : "application/x-www-form-urlencoded; charset=utf-8",
                    abpHandleError: false
                }).fail(function (jqXHR) {
                    $('#LAY-user-get-vercode').attr('src', '@(DG.Setting.Current.CaptChaUrl)?' + (new Date().getTime()));
                    $("#LAY-user-login-vercode").val("");

                    layer.msg(jqXHR.message, { icon: 5 });
                }).always(function (res) {
                    layer.close(waitIndex);

                    if (res.success) {
                        var now = new Date().getTime(); // 获取当前时间戳
                        var seconds = res.data.RefreshUtcExpires - now; // 计算时间戳与当前时间之间的毫秒数

                        const storage = new Storage();  // new Storage(3)

                        if (data.field.keeplogin) {
                            storage.set("remember", 365 * 24 * 60 * 60, seconds);
                        }
                        else {
                            storage.set("remember", 2 * 60 * 60, seconds);
                        }

                        storage.set("AccessToken", res.data.AccessToken, seconds);
                        storage.set("RefreshToken", res.data.RefreshToken, seconds);
                        storage.set("AccessTokenUtcExpires", res.data.AccessTokenUtcExpires, seconds);
                        storage.set("RefreshUtcExpires", res.data.RefreshUtcExpires, seconds);

                        location.href = res.locate; //后台主页
                    }
                    else {
                        os.error(res.msg);
                        $('#LAY-user-get-vercode').attr('src', '@(DG.Setting.Current.CaptChaUrl)?' + (new Date().getTime()));
                        $("#LAY-user-login-vercode").val("");
                    }
                });

                return false;
            });
        })
    </script>
</body>
</html>