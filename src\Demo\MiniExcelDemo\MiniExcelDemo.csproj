﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="DH.MiniExcel" Version="4.1.2025.318-beta0914" />
    <PackageReference Include="DH.NCore" Version="4.12.2025.619-beta1104" />
    <PackageReference Include="Pek.Common" Version="4.12.2025.617-beta0444" />
    <PackageReference Include="Pek.MDB" Version="4.11.2025.321-beta1013" />
  </ItemGroup>

  <ItemGroup>
    <None Update="量产订单_1_202408291236.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
