﻿@{
    Html.AppendTitleParts(T("设备管理").Text);

    var dgPage = (Pek.Webs.HttpContext.Current.Request.RouteValues["controller"] + "_" + Pek.Webs.HttpContext.Current.Request.RouteValues["action"]).ToLower();

    var id = Model.Device.Id;
    var deviceName = Model.Device.Code;
    // Css

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/Storage.js");
    @* Html.AppendScriptParts(ResourceLocation.Footer, "~/js/initSignalr.js"); *@
}
<style>
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .selectLabel {
        min-width: 50px !important;
        min-height: 30px !important;
        line-height: 30px !important;
    }

    .selectBox {
        width: 500px !important;
        height: 40px !important;
        display: flex !important;
        background: transparent !important;
        /* border:2px solid black; */
    }

    .dg-form {
        /* position:absolute; */
        /* top: 30px; */
        z-index: 1000;
    }

    .Remark {
        overflow: hidden; /*超出部分隐藏*/
        white-space: nowrap; /*禁止换行*/
        text-overflow: ellipsis; /*省略号*/
        cursor: pointer;
    }

    .mask {
        position: fixed;
    }
</style>

<div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
    <input hidden id="cacheTags" />
    <ul class="layui-tab-title" id="TabControl1">
        <li lay-id="11" id="layuiTab_1" class="layui-this isLoaded">@T("设备数据")</li>
        <li lay-id="22" id="layuiTab_2">@T("运行状态")</li>
        <li lay-id="44" id="layuiTab_4">@T("用户")</li>
        <li lay-id="44" id="layuiTab_4">@T("绑定日志")</li>
    </ul>
    <!-- 选项 -->
    <div class="layui-tab-content">
        <div class="layui-tab-item layui-show" id="tab-item1">
            @await Html.PartialAsync("_deviceData") @* 01.设备数据 *@
        </div>
        <div class="layui-tab-item" id="tab-item2">
            @await Html.PartialAsync("_deviceWorkStatus") @* 02.设备工作状态 *@
        </div>
        <div class="layui-tab-item">
            @await Html.PartialAsync("BindUser") @* 03.设备绑定用户 *@
        </div>
        <div class="layui-tab-item">
            @await Html.PartialAsync("_bindLogs") @* 04.设备绑定日志 *@
        </div>
    </div>
</div>
<script asp-location="Footer">
    var id = '@id';

    const storage1 = new Storage(3);
    var layid = storage1.get(id + "details:refresh");

    $("#cacheTags").val(layid);

    $(window).on("unload", function (e) {

    });

    $(window).on("beforeunload", function (e) {
        storage1.set(id + "debugging:refresh", $("#cacheTags").val()); 
    });


    window.onload = function () {
        console.log('页面已加载完成');
        const myData = localStorage.getItem('myData');
        if (myData==1) {
            const title1 = document.getElementById('layuiTab_1');
            const title2 = document.getElementById('layuiTab_2');
            title1.classList.remove("layui-this", "isLoaded");
            title2.classList.add("layui-this", "isLoaded");
            const item1 = document.getElementById('tab-item1');
            const item2 = document.getElementById('tab-item2');
            item1.classList.remove("layui-show");
            item2.classList.add("layui-show");
        }
    };

    function SearchTime2(){
        active.reload();
    }

    layui.use(['abp', 'dg', 'form','table','dgcommon','common','laydate'], () => {
         var os = layui.dgcommon;
         var laydate = layui.laydate;
         laydate.render({
             elem:'#datetimes2'
         });
         laydate.render({
             elem:'#datetimes3'
         });
         var table = layui.table;
         table.render({
            elem: '#tablist2',
            url: '@Url.Action("GetBindLogsList", new { deviceName = deviceName })',
            loading: true,
            toolbar: '#user-toolbar',
            defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print'],
            cols:[[
                // { field: 'Id', title: '@T("编号")', align: "left", rowspan: 2, width: 60,align:'center' },
                { field: 'Operating', title: '@T("内容")', rowspan: 2, width: 550},
                 { field: 'DType', title: '@T("操作类型")', width: 90,align:'center',templet:function(d){
                            if(d.DType == 1){
                                return '@T("绑定")'
                            }else if(d.DType == 2){
                                return '@T("解绑")'
                            }else if(d.DType == 3){
                                return '@T("分享")'
                            }else if(d.DType == 4){
                                return '@T("取消分享")'
                            }
                 } },
                 { field: 'CreateUser', title: '@T("操作人")', rowspan: 2, width: 160,align:'center' },
                 { field: 'CreateTime', title: '@T("创建时间")', rowspan: 2, width: 160,align:'center' },
            ]],
            page: { curr: os.GetPageNum("GetBindLogsList") },
            limit: 10,
            limits: [20, 50, 100, 200, 500],
            id: 'tablist2',
            smartReloadModel: true,
            text: { none: '@T("无数据")' },
         });

         window.active = {
             reload: function() {
                 table.reload('tablist2',{
                     page:{curr:1},
                     where:{
                          strat: $("#datetimes2").val(),
                          end: $("#datetimes3").val(),
                     },
                     text: { none: '@T("无数据")' },
                 })
             }
         };


    })
    
</script>