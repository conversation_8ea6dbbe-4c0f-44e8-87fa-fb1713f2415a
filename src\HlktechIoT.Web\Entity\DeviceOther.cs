﻿using DG.SafeOrbit.Extensions;
using DH.Entity;
using HlktechIoT.Data;
using NewLife;
using NewLife.Data;
using Pek;
using XCode;
namespace HlktechIoT.Entity
{
    public class DeviceEx : Device
    {
        /// <summary>根据会员Code、设备ID集合获取</summary>
        /// <param name="Code">会员Code</param>
        /// <param name="Iots">设备ID集合</param>
        /// <returns>数量</returns>
        public static Int64 FindByManuFacturerId(String Code, String[] Iots)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000)
            {
                return Meta.Cache.FindAll(e => e.Manufacturers == Code && Iots.Contains(e.Id.ToString())).Count;
            }

            return FindCount(_.Manufacturers == Code & _.Id.In(Iots));
        }

        /// <summary>根据阿里云设备名称查找</summary>
        /// <param name="deviceName">阿里云设备名称</param>
        /// <returns>实体对象</returns>
        public static Device? FindByDeviceName(String? deviceName)
        {
            if (deviceName.IsNullOrEmpty()) return null;

            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Code.EqualIgnoreCase(deviceName));

            // 单对象缓存
            return Meta.SingleCache.GetItemWithSlaveKey(deviceName) as Device;

            //return Find(_.DeviceName == deviceName);
        }

        //
        // 摘要:
        //     高级查询
        //
        // 参数:
        //   productIds:
        //     产品
        //
        //   UId:
        //     用户Id
        //
        //   key:
        //     关键字
        //
        //   page:
        //     分页参数信息。可携带统计和数据权限扩展查询等信息
        //
        //   Status:
        //     状态。-1为全部，0为在线，1为不在线，2为未激活
        //
        //   parentId:
        //     父设备
        //
        // 返回结果:
        //     实体列表
        public static IList<Device> Search(string productIds, string key, int UId, int Status, PageParameter page, int parentId = 0)
        {
            WhereExpression whereExpression = new WhereExpression();
            if (!productIds.IsNullOrWhiteSpace())
            {
                whereExpression &= @_.ProductId.In(productIds.SplitAsInt());
            }

            if (UId != 0)
            {
                //var modelUserDetail = UserDetail.FindById(UId);
                var UserModel = UserE.FindByID(UId);
                whereExpression &= @_.VendorAgents.Contains(UserModel.Code);
                //if (modelUserDetail.UType == UserKinds.Agent)
                //{
                //    whereExpression &= @_.Agents.In(UserModel.Code);
                //}
                //if (modelUserDetail.UType == UserKinds.Vendor)
                //{
                //    whereExpression &= @_.Manufacturers.In(UserModel.Code);
                //}
            }

            switch (Status)
            {
                case 0:
                    whereExpression &= @_.Online == true;
                    break;
                case 1:
                    whereExpression &= @_.Online == false;
                    break;
                case 2:
                    whereExpression &= @_.RegisterTime.IsNull();
                    break;
            }

            if (!key.IsNullOrEmpty())
            {
                whereExpression &= (Expression)(@_.Name.Contains(key) | @_.Code.Contains(key) | @_.Version.Contains(key) | @_.IP.Contains(key) | @_.Address.Contains(key) | @_.Uuid.Contains(key) | @_.Remark.Contains(key));
            }

            if (whereExpression.IsEmpty && parentId == 0)
            {
                whereExpression &= (Expression)((@_.ParentId == parentId) | @_.ParentId.IsNull());
            }

            return Entity<Device>.FindAll(whereExpression, page);
        }

        public static IList<Device> Search(string productIds, string key, int UId, PageParameter page, string version)
        {
            version = version.SafeString().Trim(',');
            WhereExpression whereExpression = new();
            if (!productIds.IsNullOrWhiteSpace())
            {
                whereExpression &= @_.ProductId.In(productIds.SplitAsInt());
            }

            if (UId != 0)
            {
                IList<Product> source = HlktechIoT.Data.Product.FindAllByCreateUserId(UId);
                if (!source.Any())
                {
                    return new List<Device>();
                }

                whereExpression &= @_.ProductId.In(source.Select((Product e) => e.Id));
            }

            if (!key.IsNullOrEmpty())
            {
                whereExpression &= (Expression)(@_.Name.Contains(key) | @_.Code.Contains(key) | @_.Version.Contains(key) | @_.IP.Contains(key) | @_.Address.Contains(key) | @_.Uuid.Contains(key) | @_.Remark.Contains(key) | @_.NickName.Contains(key));
            }

            if (!version.IsNullOrEmpty())
            {
                if (version.Contains("unknown"))
                {
                    whereExpression &= (@_.Version.In(version) | @_.Version.IsNullOrEmpty());
                }
                else
                {
                    whereExpression &= @_.Version.In(version);
                }
            }

            return Entity<Device>.FindAll(whereExpression, page);
        }
    }
}
