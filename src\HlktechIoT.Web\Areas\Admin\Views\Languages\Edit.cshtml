﻿@model List<LocaleStringResource>
@{
    Html.AppendTitleParts(T("编辑语言包").Text);

    var Ids = "";
}
<style asp-location="true">
    html {
        background-color: #f2f2f2;
        background-color: transparent !important;
        color: #666;
    }

    body {
        height: 100%;
    }

    .pear-container {
        background-color: white;
    }

    .container {
        padding:20px
    }
    .red {
        color:red
    }
    @if (language.UniqueSeoCode == "en")
    { 
        <text>
        .layui-form-label {
            width: 100px !important;
        }
        
        .layui-textarea {
            width: 90% !important;
        }
        .layui-input-block.btndiv {
            padding-right: 40px;
        }
        </text>
    }
    .layui-input-block.btndiv {
        text-align: center;
    }
</style>
<div class="container">
    @using (Html.BeginForm("EditLanguage", "Language", FormMethod.Post, new { @class = "layui-form" }))
    {
        <div class="layui-form-item">
            <label class="layui-form-label"><span class="red">* </span>@T("翻译键")</label>
            <div class="layui-input-inline">
                <input type="text" id="LanKey" name="LanKey" autocomplete="off" class="layui-input" value="@ViewBag.LanKey">
            </div>
            @* <div class="layui-form-mid layui-word-aux">请填写6到12位密码</div>*@
        </div>
        
        @foreach (var item in (IEnumerable<Language>)ViewBag.LanguageList)
        {
            <div class="layui-form-item layui-form-text">
                <label for="" class="layui-form-label">@T(item.Name)</label>
                <div class="layui-input-block">
                    <input type="hidden" name="[@item.Id].Id" value="@item.Id" />
                    @{
                        var modelL = Model!.Find(e => e.CultureId == item.Id);
                        if (modelL != null)
                        {
                            Ids += modelL.Id + ",";
                            <input type="hidden" name="[@item.Id].LanKey" value="@modelL.LanKey" />
                            <textarea placeholder="@T("请输入内容")" class="layui-textarea" name="[@item.Id].LanValue">@modelL.LanValue</textarea>
                        }
                        else
                        {
                            <input type="hidden" name="[@item.Id].LanKey" />
                            <input type="hidden" name="[@item.Id].LanType" />
                            <textarea placeholder="@T("请输入内容")" class="layui-textarea" name="[@item.Id].LanValue"></textarea>
                        }
                    }
                </div>
                <div class="col-sm-3"></div>
            </div>
        }
        <input type="hidden" id="ids" value="@Ids" />
        <div class="layui-form-item">
            <div class="layui-input-block btndiv">
                <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" value="@T("保存")" />
            </div>
        </div>
    }
</div>
<script asp-location="Footer">
     layui.use(['abp', 'dg', 'form'], function () {
            var $ = layui.jquery;
            var abp = layui.abp;
            var form = layui.form;
            var dg = layui.dg;


            form.on('submit(Submit)', function (data) {
                if (data.field.LanKey.length == 0) {
                    abp.notify.warn("@T("翻译键不为能空")");
                    return;
                }

                var waitIndex = parent.layer.load(2);

                abp.ajax({
                    url: "@Url.Action("EditLanguage")",
                    contentType : "application/x-www-form-urlencoded; charset=utf-8",
                    data: data.field,
                    abpHandleError: false
                }).done(function (data) {
                    if (!data.success) {
                        abp.notify.error(data.msg);
                        return false;
                    }
                    parent.layer.closeAll();
                    dg.reload('tables');
                    parent.layui.abp.notify.success(data.msg);
                }).fail(function (jqXHR) {
                    parent.layer.msg(jqXHR.message, { icon: 5 });
                }).always(function () {
                    parent.layer.close(waitIndex);
                });

                return false;
            });
     })
</script>