﻿@{
    Html.AppendTitleParts(T("新增固件").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        padding-top: 30px;
        padding-left: 30px;
    }

    .layui-form-item.btn {
        padding-top: 10px;
        padding-left: 115px;
    }

    .label-width span {
        color: #f00;
    }

    .layui-form-label {
        width: 94px;
    }

    .upload {
        cursor: pointer;
        max-width: 120px;
        max-height: 90px;
    }
</style>
<div class="containers">
    <form class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label label-width">
                <span>*</span>@T("文件")
            </label>
            <div class="layui-input-inline">
                <div class="layui-upload-drag" id="upload">
                    <i class="layui-icon"></i>
                    <p>@T("点击上传，或将文件拖拽到此处")</p>
                    <div class="layui-hide" id="uploadDemoView">
                        <hr>
                        <label id="excel" class="layui-form-label-left"></label>
                    </div>
                </div>
            </div>
        </div>
       @*  <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("软件版本号")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="Versions" id="Versions" readonly placeholder="@T("版本号")" autocomplete="off" class="layui-input" lay-filter="Versions" value="">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">@T("产品PK")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="ProductKey" id="ProductKey" placeholder="@T("所属产品PK")" autocomplete="off" class="layui-input" lay-filter="Versions" value=""/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">@T("产品型号选择")</label>
            <div class="layui-input-inline" style="width: 300px;">
                <div id="demo1" style=" width: 100%;"></div>
            </div>
        </div> *@
        <div class="layui-form-item">
            <label class="layui-form-label">@T("固件类型选择")</label>
            <div class="layui-input-inline" style="width: 300px;">
                <div style=" width: 100%;">
                      <select name="FType" id="FType">
                        <option value="0">@T("正式固件")</option>
                        <option value="1" selected>@T("测试固件")</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("更新后执行")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <textarea placeholder="@T("请输入更新后执行命令")" name="Executor" id="Executor" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("APP确认升级")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="checkbox" lay-filter="switch" name="Force" lay-skin="switch" lay-text="@T("是")|@T("否")">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("强制升级")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="checkbox" lay-filter="switch" name="ForcedUpgrade" lay-skin="switch" lay-text="@T("是")|@T("否")">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label label-width">@T("备注")</label>
            <div class="layui-input-inline" style="width: 300px;">
                <textarea placeholder="@T("请输入内容")" name="Remark" id="Remark" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item btn">
            <input hidden name="OriginFileName" id="OriginFileName" />
            <input hidden name="FileSize" id="FileSize" />
            <input hidden name="FileUrl" id="FileUrl" />
            <input hidden name="Versions" id="Versions"/>
            <input hidden name="ProductKey" id="ProductKey"/>
            <input hidden name="DataSize" id="DataSize"/>
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" value="@T("保存")" />
        </div>
    </form>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;
        var upload = layui.upload;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        //拖拽阈值表上传
        upload.render({
            elem: '#upload'
            , url: '@Url.Action("UploadFiles")' //改成您自己的上传接口
            , done: function (res) {
                if (!res.success) { //失败打印
                    os.error(res.msg);
                    return;
                }
                os.success('@T("上传成功")');

                layui.$('#uploadDemoView').removeClass('layui-hide');
                $("#excel").text(res.data.OriginFileName);
                $("#OriginFileName").val(res.data.OriginFileName);
                $("#FileSize").val(res.data.FileSize);
                $("#FileUrl").val(res.data.FileUrl);
                $("#Versions").val(res.data.Version);
                $("#ProductKey").val(res.data.ProductKey);
                $("#DataSize").val(res.data.DataSize);
            },
            before: function () {
                this.data = {
                    //Versions: $('#Versions').val(),
                    //expirationDate: $('#expirationDate').val()
                }
            }
            , accept: 'file' //允许上传的文件类型
            , exts: 'ota' //只允许上传s19、bin、img文件,以|分隔多个文件后缀
        });

        var demo1 = xmSelect.render({
            el: '#demo1',
            //radio: true,
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            clickClose: true,
            pageRemote: true,  // 分页
            remoteMethod: function (val, cb, show, pageIndex) {  // 远程方法
                var obj = [];

                // 接口数据
                $.post('@Url.Action("SearchProductModule")', { keyword: val, page: pageIndex }, function (res) {
                    if (res.success) {
                        if (res.data != null) {
                            cb(res.data, res.extdata);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });

            },
            on: function (data) {  // 监听选择
                console.log(data);
                if (data.arr.length > 0) {
                    var a = "";
                    for (var i = 0; i < data.arr.length; i++) {
                        if (i == 0) {
                            a = data.arr[i].value;
                        }
                        else {
                            a += "," + data.arr[i].value;
                        }
                    }
                }
            }
        });

        form.on('submit(Submit)', function (data) {
            if (data.field.FileUrl.length == 0) {
                abp.notify.warn("@T("请先上传固件")");
                return;
            }

            if (data.field.Versions.length == 0) {
                abp.notify.warn("@T("软件版本号不能为空")");
                return;
            }

            var waitIndex = parent.layer.load(2);

            abp.ajax({
                url: "@Url.Action("Add")",
                contentType : "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                dg.reload('tables');
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

    });
</Script>