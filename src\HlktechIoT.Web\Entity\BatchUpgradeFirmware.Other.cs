﻿using HlktechIoT.Data;

using NewLife.Data;

using Pek;

using XCode;

namespace HlktechIoT.Entity {
    public class BatchUpgradeFirmwareEx : BatchUpgradeFirmware
    {
        /// <summary>
        /// 通过UpgradeFirmwareId搜使用该固件升级的所有批次
        /// </summary>
        /// <param name="upgradeFirmwareId">固件</param>
        /// <param name="page"></param>
        /// <param name="batchId">批次</param>
        /// <returns></returns>
        public static IList<BatchUpgradeFirmware> SearchByIdAndUpgradeFirmwareId(string upgradeFirmwareId, PageParameter page, string batchId)
        {
            WhereExpression where = new();

            if (batchId.IsNotNullOrWhiteSpace())
            {
                where &= (Expression)(@_.Id.Equal(batchId));
            }

            where &= (Expression)(@_.FirmwareId.Equal(upgradeFirmwareId));

            return Entity<BatchUpgradeFirmware>.FindAll(where, page);
        }
    }
}
