﻿using DG.Web.Framework;

using DH.Core.Infrastructure;
using DH.Entity;

using HlktechIoT.Data;
using HlktechIoT.Services;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;
using NewLife.IoT.ThingModels;
using NewLife.Log;
using NewLife.Serialization;

using Pek;
using Pek.Models;
using Pek.Timing;

using System.ComponentModel;

using XCode.Membership;

using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Device.Controllers;

/// <summary>
/// APP用户列表
/// </summary>
[DisplayName("APP用户")]
[Description("APP用户")]
[DeviceArea]
[DHMenu(90, ParentMenuName = "DHUser", CurrentMenuUrl = "~/{area}/AppUser", CurrentMenuName = "AppUserList", LastUpdate = "20240305")]
public class AppUserController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 90;

    /// <summary>
    /// APP用户列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("APP用户列表")]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// 根据条件搜索
    /// </summary>
    /// <param name="name">登录名/昵称</param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <param name="PId">项目Id</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("APP用户查询")]
    public IActionResult GetList(String name, Int32 PId, Int32 page = 1, Int32 limit = 10)
    {
        if (name != null) name = name.Trim();
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = AppUser._.RegisterTime,
            Desc = true,
        };

        IEnumerable<AppUser> list = AppUser.Searchs(PId, name, pages);
        var data = list.Select(x => new { x.Id, x.Name, x.DisplayName, x.Enable, x.ProjectName, x.Mail, x.Mobile, x.RegisterTime, x.Online, Count = x.Devices.IsNullOrWhiteSpace() ? 0 : x.Devices.Split(',').Length, x.LastLogin });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 搜索项目
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [HttpPost]
    [DisplayName("搜索项目")]
    public IActionResult SearchProject(String keyword, Int32 PId, Int32 page)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = Project._.Id,
            Desc = true,
        };

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (modelRole.IsAdmin)
        {
            res.data = Project.Search(0, keyword, pages).Select(e =>
            {
                var selected = false;
                if (e.Id == PId)
                {
                    selected = true;
                }
                return new Xmselect<Int32>
                {
                    name = e.Name,
                    value = e.Id,
                    selected = selected
                };
            });
        }
        else
        {
            res.data = Project.Search(ManageProvider.User?.ID ?? -1, keyword, null).Select(e =>
            {
                var selected = false;
                if (e.Id == PId)
                {
                    selected = true;
                }

                return new Xmselect<Int32>
                {
                    name = e.Name,
                    value = e.Id,
                    selected = selected
                };
            });
        }

        res.success = true;
        return Json(res);
    }

    /// <summary>修改状态</summary>
    /// <returns></returns>
    [DisplayName("修改状态")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult ModifyState(Int32 Id, Boolean Status)
    {
        var result = new DResult();

        var model = AppUser.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("状态调整出错");
            return Json(result);
        }

        model.Enable = Status;
        model.Update();

        if (!Status)
        {
            var _queue = EngineContext.Current.Resolve<QueueService>();  // 队列通过服务给用户发指令

            _queue.Publish(new ServiceModel
            {
                Type = "UserTransmission",
                Name = $"{model.IdentityId} UserTransmission",
                InputData = new UserTransmissionModel
                {
                    Id = Common.CommonFields.Snowflake.NewId(),
                    IdentityId = model.IdentityId,
                    Type = 1,
                    //LastLoginTime = model.LastLogin.ToTimeStamp(),
                    Time = UnixTime.ToTimestamp(),
                    InputData = new UserTransmissionData
                    {
                    },
                }.ToJson(),
            });
        }

        result.success = true;
        result.msg = GetResource("状态调整成功");

        return Json(result);
    }

    /// <summary>用户绑定设备</summary>
    /// <returns></returns>
    [DisplayName("用户绑定设备")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult BindDevice(Int32 Id)
    {
        var model = AppUser.FindById(Id);
        if (model == null) return Content(GetResource("用户不存在"));

        return View(model);
    }

    /// <summary>
    /// 根据条件搜索
    /// </summary>
    /// <param name="Id">用户编号</param>
    /// <param name="Key">DeviceName</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("APP用户查询")]
    public IActionResult GetDeviceList(String Key, Int32 Id)
    {
        var model = AppUser.FindById(Id);
        if (model == null)
        {
            return Json(new { code = 1, msg = "fail", count = 0 });
        }

        // 清理无效绑定
        var listDeviceAssociatedUsers = DeviceAssociatedUsers.FindAllByIdentityIdOrSubUsers(model.IdentityId);
        if (listDeviceAssociatedUsers.Count != model.Devices?.Split(',').Length)
        {
            var hashDevices = new HashSet<String>();
            var listProductKeys = new List<String>();

            foreach (var item in listDeviceAssociatedUsers)
            {
                var modelDeviceBindLogs1 = new DeviceBindLogs
                {
                    DeviceName = item.DeviceName,
                    Operating = $"GetDeviceList无效绑定解绑，{model.IdentityId}",
                    IdentityId = model.IdentityId,
                    DType = 2,
                    CreateUserID = model.Id,
                    CreateUser = model.DisplayName,
                };
                modelDeviceBindLogs1.SaveAsync();

                hashDevices.Add(item.DeviceName);
                listProductKeys.Add(item.ProductKey);
            }

            model.Devices = hashDevices.Join();
            model.ProductKeys = listProductKeys.Join();
            model.Update();
        }

        var list = model.ProductKeys?.Split(',');
        var i = 0;
        var data = model.Devices?.Split(',').Select(e =>
        {
            if (i >= list?.Length) return null;

            if (!Key.IsNullOrWhiteSpace() && !e.Contains(Key, StringComparison.OrdinalIgnoreCase)) return null;

            i++;

            var ProductKey = list?[i - 1];

            var modelProduct = Product.FindByCode(ProductKey);
            if (modelProduct == null)
            {
                return null;
            }

            var modelDeviceAssociatedUsers = DeviceAssociatedUsers.FindByDeviceName(e);
            if (modelDeviceAssociatedUsers == null)
            {
                return null;
            }

            var owned = 1;
            if (modelProduct.BindType == 2)  // 抢占式
            {
                if (modelDeviceAssociatedUsers.IdentityId != model.IdentityId)
                {
                    return null;
                }
                else
                {
                    owned = 1;
                }
            }
            else if (modelProduct.BindType == 0)  // 独占式
            {
                if (modelDeviceAssociatedUsers.IdentityId != model.IdentityId)
                {
                    return null;
                }
                else
                {
                    owned = 1;
                }
            }
            else if (modelProduct.BindType == 1)  // 分享式
            {
                if (modelDeviceAssociatedUsers.IdentityId != model.IdentityId)
                {
                    if (modelDeviceAssociatedUsers.SubUsers.SafeString().Contains(model.IdentityId) == true)
                    {
                        owned = 0;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    owned = 1;
                }
            }

            //if (modelProduct.DBName != 0)
            //{
            //    Data.Device.Meta.TableName = $"Device_{modelProduct.DBName}"; //获取表名字
            //}
            //if (modelProduct.ConnName != 0)
            //{
            //    Data.Device.Meta.ConnName = $"Device_{modelProduct.ConnName}"; //获取连接名字
            //}

            try
            {
                var modelDevice = Data.Device.FindByCode(e);
                if (modelDevice == null)
                {
                    return null;
                }

                return new
                {
                    modelDevice.Id,
                    modelDevice.Name,
                    modelDevice.Online,
                    modelDevice.Code,
                    modelDevice.ProductName,
                    modelDevice.Enable,
                    modelDevice.Version,
                    modelDevice.Module,
                    modelDevice.PostPeriod,
                    modelDevice.LastLogin,
                    Owned = owned,
                };
            }
            catch (Exception ex)
            {
                XTrace.WriteException(ex);
            }
            //finally
            //{
            //    Data.Device.Meta.TableName = null;
            //    Data.Device.Meta.ConnName = null;
            //}

            return null;
        }).Where(e => e != null).ToList();

        return Json(new { code = 0, msg = "success", count = data?.Count, data });
    }
}
