﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
	<SatelliteResourceLanguages>en</SatelliteResourceLanguages>
	<!--允许你指定要在生成和发布过程中为哪些语言保留附属资源程序集-->
  </PropertyGroup>
	
  <ItemGroup>
    <None Remove="Protos\greet.proto" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="DH.Common" Version="4.0.2025.61700080" />
    <PackageReference Include="DH.NAgent" Version="4.12.2025.617-beta1020" />
    <PackageReference Include="DH.NMQTT" Version="4.12.2025.620-beta1203" />
    <PackageReference Include="DH.NStardust" Version="4.12.2025.621-beta0339" />
    <PackageReference Include="Google.Protobuf" Version="3.31.1" />
    <PackageReference Include="Grpc.Net.Client" Version="2.71.0" />
    <PackageReference Include="Grpc.Tools" Version="2.72.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="HlktechIoT.Core" Version="1.7.2025.6210229" />
  </ItemGroup>
	
  <ItemGroup>
    <Protobuf Include="Protos\greet.proto" GrpcServices="Client" /> 
  </ItemGroup>

</Project>
