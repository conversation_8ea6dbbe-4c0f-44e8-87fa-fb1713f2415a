菲凡物联网平台v1.8更新：
1. 客户端服务端支持根据点位数据改变而生成报警事件，优化对各种报警点位的支持
2. IoT标准库更新TSL物模型规范，支持属性点事件模式配置
3. 使用采集时间来生成雪花Id，数据存储序列即业务时间顺序
4. 服务端明确数据存储开关，属性数据、历史数据、状态数据、分段数据、数据队列和事件队列，都可以单独控制开关
5. 使用分布式缓存ICacheProvider，简化Redis配置，可在appsettings.json或配置中心配置RedisCache来启用，即使没有Redis也不影响基本功能
6. IoT客户端支持-server参数设置服务端地址，使用星尘发布时，可灵活设置IoT服务端地址
7. 支持向客户端下发未来某一时刻执行的命令，以免需要执行时网络状况不佳，暂时不支持取消
8. 重构IoTServer，抽象服务层IoTService，IoTMqtt和IoTRpc作为服务端插件，用户可参考它们去实现自定义协议插件，如某些DTU透传设备的私有协议。
9. 支持用户侧通过MQTT订阅设备数据，不支持订阅设备通过非MQTT协议接入的设备数据，如HTTP
10. 新增青春版IoT平台IoTZero，用于特定设备的物联网平台项目
11. DeviceData首次建表时，设置为压缩表，在MySql上空间占用缩减为四分之一
12. 优化IoTClient对BACnet协议的支持，支持扫描发现BAC网络子设备

v1.9路线规划：
1. 支持批量下发命令给客户端，用于网关转发命令给多个子设备
2. IoTServer增加LoraWAN协议插件，支持标准LoraWAN设备接入