namespace HlktechIoT.Services.OAuth;

/// <summary>
/// OAuth应用信息
/// </summary>
public class OAuthApp
{
    /// <summary>
    /// 应用ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 应用密钥
    /// </summary>
    public string ClientSecret { get; set; } = string.Empty;

    /// <summary>
    /// 应用名称
    /// </summary>
    public string AppName { get; set; } = string.Empty;

    /// <summary>
    /// 重定向URI列表（多个用逗号分隔）
    /// </summary>
    public string RedirectUris { get; set; } = string.Empty;

    /// <summary>
    /// 允许的作用域（多个用逗号分隔）
    /// </summary>
    public string AllowedScopes { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 验证重定向URI是否有效
    /// </summary>
    public bool IsValidRedirectUri(string redirectUri)
    {
        if (string.IsNullOrEmpty(RedirectUris) || string.IsNullOrEmpty(redirectUri))
            return false;

        var uris = RedirectUris.Split(',', StringSplitOptions.RemoveEmptyEntries);
        return uris.Any(uri => uri.Trim().Equals(redirectUri, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// 验证作用域是否有效
    /// </summary>
    public bool IsValidScope(string scope)
    {
        if (string.IsNullOrEmpty(AllowedScopes) || string.IsNullOrEmpty(scope))
            return false;

        var allowedScopes = AllowedScopes.Split(',', StringSplitOptions.RemoveEmptyEntries);
        var requestedScopes = scope.Split(' ', StringSplitOptions.RemoveEmptyEntries);

        return requestedScopes.All(s => allowedScopes.Any(allowed => allowed.Trim().Equals(s, StringComparison.OrdinalIgnoreCase)));
    }
}

/// <summary>
/// OAuth授权码信息
/// </summary>
public class OAuthAuthorizationCode
{
    /// <summary>
    /// 授权码
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 用户ID
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// 重定向URI
    /// </summary>
    public string RedirectUri { get; set; } = string.Empty;

    /// <summary>
    /// 作用域
    /// </summary>
    public string Scope { get; set; } = string.Empty;

    /// <summary>
    /// 状态参数
    /// </summary>
    public string State { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime ExpireTime { get; set; }

    /// <summary>
    /// 是否已使用
    /// </summary>
    public bool IsUsed { get; set; }

    /// <summary>
    /// 验证授权码是否有效
    /// </summary>
    public bool IsValid()
    {
        return !IsUsed && ExpireTime > DateTime.UtcNow;
    }
}
