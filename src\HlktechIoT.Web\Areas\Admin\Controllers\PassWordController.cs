﻿using DG.Web.Framework;

using Microsoft.AspNetCore.Mvc;

using NewLife;

using Pek.Models;

using System.ComponentModel;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Admin.Controllers;

/// <summary>修改密码</summary>
[DisplayName("修改密码")]
[Description("当前用户修改自己的密码")]
[AdminArea]
[DHMenu(99, ParentMenuName = "SystemSetting", CurrentMenuUrl = "~/{area}/PassWord", CurrentMenuName = "PassWord", LastUpdate = "20240124")]
public class PassWordController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 99;

    /// <summary>
    /// 修改密码
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("修改密码")]
    [HttpGet]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// 密码提交修改
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("修改密码")]
    [HttpPost]
    public IActionResult UpdatePassword(String oldPassword, String password, String repassword)
    {
        var model = ManageProvider.User!;

        if (password.IsNullOrWhiteSpace())
        {
            return Json(new DResult { success = false, msg = GetResource("新密码不能为空") });
        }

        if (password != repassword)
        {
            return Json(new DResult { success = false, msg = GetResource("两次密码不一致") });
        }

        oldPassword = oldPassword.Length == 32 ? oldPassword : oldPassword.MD5();

        if (ManageProvider.Provider?.PasswordProvider.Verify(oldPassword, model.Password!) == false)
        {
            return Json(new DResult { success = false, msg = GetResource("旧密码错误") });
        }

        model.Password = ManageProvider.Provider?.PasswordProvider.Hash(repassword.Length == 32 ? repassword : repassword.MD5());
        (model as IEntity)!.Update();

        return Json(new DResult { success = true, msg = GetResource("密码修改成功") });
    }
}