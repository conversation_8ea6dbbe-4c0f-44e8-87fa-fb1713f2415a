{
  //以下信息会被自动识别，如无特殊说明，不用的参数可以删除，但修改 key 后将会无法自动识别！

  //CO2NET 设置
  "SenparcSetting": {
    //以下为 CO2NET 的 SenparcSetting 全局配置，请勿修改 key，勿删除任何项

    "IsDebug": true,
    "DefaultCacheNamespace": "DefaultCache",

    //分布式缓存
    "Cache_Redis_Configuration": "#{Cache_Redis_Configuration}#", //Redis配置
    //"Cache_Redis_Configuration": "localhost:6379",//不包含密码
    //"Cache_Redis_Configuration": "localhost:6379,password=senparc,connectTimeout=1000,connectRetry=2,syncTimeout=10000,defaultDatabase=3",//密码及其他配置
    "Cache_Memcached_Configuration": "#{Cache_Memcached_Configuration}#", //Memcached配置
    "SenparcUnionAgentKey": "#{SenparcUnionAgentKey}#" //SenparcUnionAgentKey
  },
  //Senparc.Weixin SDK 设置
  "SenparcWeixinSetting": {
    //以下为 Senparc.Weixin 的 SenparcWeixinSetting 微信配置
    //注意：所有的字符串值都可能被用于字典索引，因此请勿留空字符串（但可以根据需要，删除对应的整条设置）！

    //微信全局
    "IsDebug": true,

    //以下不使用的参数可以删除，key 修改后将会失效

    //公众号
    "Token": "#{Token}#", //说明：字符串内两侧#和{}符号为 Azure DevOps 默认的占位符格式，如果您有明文信息，请删除同占位符，修改整体字符串，不保留#和{}，如：{"Token": "MyFullToken"}
    "EncodingAESKey": "#{EncodingAESKey}#",
    "WeixinAppId": "#{WeixinAppId}#",
    "WeixinAppSecret": "#{WeixinAppSecret}#",
    //小程序
    "WxOpenAppId": "#{WxOpenAppId}#",
    "WxOpenAppSecret": "#{WxOpenAppSecret}#",
    "WxOpenToken": "#{WxOpenToken}#",
    "WxOpenEncodingAESKey": "#{WxOpenEncodingAESKey}#",
    //企业微信
    "WeixinCorpId": "#{WeixinCorpId}#",
    "WeixinCorpAgentId": "#{WeixinCorpAgentId}#",
    "WeixinCorpSecret": "#{WeixinCorpSecret}#",
    "WeixinCorpToken": "#{WeixinCorpToken}#",
    "WeixinCorpEncodingAESKey": "#{WeixinCorpEncodingAESKey}#",

    //微信支付
    //微信支付V2（旧版）
    "WeixinPay_PartnerId": "#{WeixinPay_PartnerId}#",
    "WeixinPay_Key": "#{WeixinPay_Key}#",
    "WeixinPay_AppId": "#{WeixinPay_AppId}#",
    "WeixinPay_AppKey": "#{WeixinPay_AppKey}#",
    "WeixinPay_TenpayNotify": "#{WeixinPay_TenpayNotify}#",
    //微信支付V3（新版）
    "TenPayV3_AppId": "#{TenPayV3_AppId}#",
    "TenPayV3_AppSecret": "#{TenPayV3_AppSecret}#",
    "TenPayV3_SubAppId": "#{TenPayV3_SubAppId}#",
    "TenPayV3_SubAppSecret": "#{TenPayV3_SubAppSecret}#",
    "TenPayV3_MchId": "#{TenPayV3_MchId}#",
    "TenPayV3_SubMchId": "#{TenPayV3_SubMchId}#", //子商户，没有可留空
    "TenPayV3_Key": "#{TenPayV3_Key}#",
    "TenPayV3_CertPath": "#{TenPayV3_CertPath}#", //（新）支付证书物理路径，如：D:\\cert\\apiclient_cert.p12
    "TenPayV3_CertSecret": "#{TenPayV3_CertSecret}#", //（新）支付证书密码（原始密码和 MchId 相同）
    "TenPayV3_TenpayNotify": "#{TenPayV3_TenpayNotify}#", //http://YourDomainName/TenpayV3/PayNotifyUrl
    //如果不设置TenPayV3_WxOpenTenpayNotify，默认在 TenPayV3_TenpayNotify 的值最后加上 "WxOpen"
    "TenPayV3_WxOpenTenpayNotify": "#{TenPayV3_WxOpenTenpayNotify}#", //http://YourDomainName/TenpayV3/PayNotifyUrlWxOpen

    //开放平台
    "Component_Appid": "#{Component_Appid}#",
    "Component_Secret": "#{Component_Secret}#",
    "Component_Token": "#{Component_Token}#",
    "Component_EncodingAESKey": "#{Component_EncodingAESKey}#",

    //扩展及代理参数
    "AgentUrl": "#{AgentUrl}#",
    "AgentToken": "#{AgentToken}#",
    "SenparcWechatAgentKey": "#{SenparcWechatAgentKey}#",

    //以下Items中的内容根据实际情况使用
    //Items 下面可以添加任意多个公众号、小程序、企业微信、微信支付（V2/V3）、开放平台的信息
    "Items": {
      //每一组账号格式参考上一级节点的对应内容，只需要添加需要的参数即可（注意：Key 不能重复），如：
      "第二个公众号": {
        //公众号
        "Token": "#{Token2}#",
        "EncodingAESKey": "#{EncodingAESKey2}#",
        "WeixinAppId": "#{WeixinAppId2}#",
        "WeixinAppSecret": "#{WeixinAppSecret2}#"
      },
      "第三个公众号": {
        //公众号
        "Token": "#{Token3}#",
        "EncodingAESKey": "#{EncodingAESKey3}#",
        "WeixinAppId": "#{WeixinAppId3}#",
        "WeixinAppSecret": "#{WeixinAppSecret3}#"
      },

      "第二个小程序": {
        //小程序
        "WxOpenAppId": "#{WxOpenAppId2}#",
        "WxOpenAppSecret": "#{WxOpenAppSecret2}#",
        "WxOpenToken": "#{WxOpenToken2}#",
        "WxOpenEncodingAESKey": "#{WxOpenEncodingAESKey2}#"
      },

      "第四个公众号+对应小程序+对应微信支付": {
        //公众号
        "Token": "#{Token4}#",
        "EncodingAESKey": "#{EncodingAESKey4}#",
        "WeixinAppId": "#{WeixinAppId4}#",
        "WeixinAppSecret": "#{WeixinAppSecret4}#",

        //小程序
        "WxOpenAppId": "#{WxOpenAppId3}#",
        "WxOpenAppSecret": "#{WxOpenAppSecret3}#",
        "WxOpenToken": "#{WxOpenToken3}#",
        "WxOpenEncodingAESKey": "#{WxOpenEncodingAESKey3}#"

        //微信支付V3（新版），略
      }
      //更多，任意数量、任意模块、任意组合...
    }
  }
}
