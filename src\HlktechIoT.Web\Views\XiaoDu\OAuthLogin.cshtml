@{
    Layout = null;
    ViewData["Title"] = "OAuth 登录";
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewData["Title"]</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .oauth-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 420px;
            overflow: hidden;
            animation: slideInUp 0.6s ease-out;
        }        
        
        @@keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .oauth-header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 40px 30px 30px;
            text-align: center;
            position: relative;
        }

        .oauth-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 2px,
                rgba(255, 255, 255, 0.05) 2px,
                rgba(255, 255, 255, 0.05) 4px
            );
            animation: move 20s linear infinite;
        }        
        
        @@keyframes move {
            0% { transform: translateX(-50%) translateY(-50%) rotate(0deg); }
            100% { transform: translateX(-50%) translateY(-50%) rotate(360deg); }
        }

        .oauth-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            position: relative;
            z-index: 1;
        }

        .oauth-icon i {
            font-size: 28px;
            color: white;
        }

        .oauth-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .oauth-subtitle {
            font-size: 14px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .oauth-body {
            padding: 40px 30px;
        }

        .app-info {
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 4px solid #4f46e5;
        }

        .app-info h6 {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .app-info p {
            font-size: 13px;
            color: #6b7280;
            margin: 0;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
        }

        .form-control {
            width: 100%;
            padding: 14px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #fff;
        }

        .form-control:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .form-control:hover {
            border-color: #d1d5db;
        }

        .form-check {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 24px;
        }

        .form-check-input {
            width: 18px;
            height: 18px;
            border: 2px solid #d1d5db;
            border-radius: 4px;
            cursor: pointer;
        }

        .form-check-input:checked {
            background-color: #4f46e5;
            border-color: #4f46e5;
        }

        .form-check-label {
            font-size: 14px;
            color: #6b7280;
            cursor: pointer;
        }

        .btn {
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .alert {
            padding: 16px;
            border-radius: 12px;
            margin-bottom: 24px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .alert-danger {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        .oauth-footer {
            background: #f8fafc;
            padding: 20px 30px;
            text-align: center;
            font-size: 13px;
            color: #6b7280;
        }

        .security-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            background: #ecfdf5;
            color: #059669;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-top: 10px;
        }        
        
        /* Mobile Responsive */
        @@media (max-width: 480px) {
            body {
                padding: 15px;
            }

            .oauth-container {
                max-width: 100%;
                border-radius: 16px;
            }

            .oauth-header {
                padding: 30px 20px 25px;
            }

            .oauth-body {
                padding: 30px 20px;
            }

            .oauth-footer {
                padding: 15px 20px;
            }

            .oauth-title {
                font-size: 22px;
            }

            .btn {
                padding: 14px;
                font-size: 15px;
            }
        }        
        
        /* Dark mode support */
        @@media (prefers-color-scheme: dark) {
            .oauth-container {
                background: rgba(31, 41, 55, 0.95);
                color: #f9fafb;
            }

            .app-info {
                background: #374151;
                border-left-color: #6366f1;
            }

            .app-info h6 {
                color: #f9fafb;
            }

            .app-info p {
                color: #d1d5db;
            }

            .form-label {
                color: #f3f4f6;
            }

            .form-control {
                background: #4b5563;
                border-color: #6b7280;
                color: #f9fafb;
            }

            .form-control:focus {
                border-color: #6366f1;
            }

            .oauth-footer {
                background: #374151;
                color: #9ca3af;
            }
        }
    </style>
</head>
<body>
    <div class="oauth-container">
        <div class="oauth-header">
            <div class="oauth-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h1 class="oauth-title">安全登录</h1>
            <p class="oauth-subtitle">通过 OAuth 2.0 安全授权</p>
        </div>

        <div class="oauth-body">
            <div class="app-info">
                <h6><i class="fas fa-info-circle"></i> 授权请求</h6>
                <p><strong>@ViewBag.AppName</strong> 正在请求访问您的账户权限</p>
            </div>

            @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
            {
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    @ViewBag.ErrorMessage
                </div>
            }
            
            <form method="post" action="/oauth/login">
                <input type="hidden" name="client_id" value="@ViewBag.ClientId" />
                <input type="hidden" name="redirect_uri" value="@ViewBag.RedirectUri" />
                <input type="hidden" name="scope" value="@ViewBag.Scope" />
                <input type="hidden" name="state" value="@ViewBag.State" />
                <input type="hidden" name="response_type" value="@ViewBag.ResponseType" />
                
                <div class="form-group">
                    <label for="username" class="form-label">
                        <i class="fas fa-user"></i> 用户名
                    </label>
                    <input type="text" class="form-control" id="username" name="username" 
                           placeholder="请输入您的用户名" required autocomplete="username">
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock"></i> 密码
                    </label>
                    <input type="password" class="form-control" id="password" name="password" 
                           placeholder="请输入您的密码" required autocomplete="current-password">
                </div>

                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="rememberMe" name="rememberMe">
                    <label class="form-check-label" for="rememberMe">在此设备上记住我</label>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i> 登录并授权
                </button>
            </form>
        </div>

        <div class="oauth-footer">
            <p>登录后将安全重定向到 <strong>@ViewBag.AppName</strong></p>
            <div class="security-badge">
                <i class="fas fa-lock"></i>
                SSL 加密保护
            </div>
        </div>
    </div>

    <script>
        // 增强用户体验的 JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // 自动聚焦到用户名输入框
            const usernameInput = document.getElementById('username');
            if (usernameInput) {
                setTimeout(() => usernameInput.focus(), 300);
            }

            // 表单验证增强
            const form = document.querySelector('form');
            form.addEventListener('submit', function(e) {
                const btn = form.querySelector('.btn-primary');
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在验证...';
                btn.disabled = true;
            });

            // 输入框交互动画
            document.querySelectorAll('.form-control').forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('focused');
                });
            });
        });
    </script>
</body>
</html>
