﻿using Aop.Api;
using Aop.Api.Domain;
using Aop.Api.Request;
using Aop.Api.Response;
using DH;
using DH.Helpers;
using NewLife.Log;
using SKIT.FlurlHttpClient.Wechat.TenpayV3;
using SKIT.FlurlHttpClient.Wechat.TenpayV3.Models;
using SKIT.FlurlHttpClient.Wechat.TenpayV3.Settings;
using System.Text;

namespace HlktechIoT.Common
{
    /// <summary>
    /// 支付帮助类
    /// </summary>
    public static class PayHelper
    {

        /// <summary>
        /// 初始化支付宝配置
        /// </summary>
        /// <returns></returns>
        public static AlipayConfig SetAlipayConfig()
        {
            string privateKey = Settings.Current.AliPrivateKey;
            string alipayPublicKey = Settings.Current.AliPublicKey;
            AlipayConfig alipayConfig = new AlipayConfig();
            alipayConfig.ServerUrl = Settings.Current.AliServerUrl;
            alipayConfig.AppId = Settings.Current.AliAppId;
            alipayConfig.PrivateKey = privateKey;
            alipayConfig.Format = "json";
            alipayConfig.AlipayPublicKey = alipayPublicKey;
            alipayConfig.Charset = "UTF-8";
            alipayConfig.SignType = "RSA2";
            return alipayConfig;
        }

        /// <summary>
        /// 创建支付宝支付链接
        /// </summary>
        /// <param name="OutTradeNo">商户订单号</param>
        /// <param name="TotalAmount">金额</param>
        /// <returns></returns>
        public static AlipayTradeWapPayResponse AliTradeWapPay(String OutTradeNo,String TotalAmount)
        {
            IAopClient alipayClient = new DefaultAopClient(SetAlipayConfig());
            AlipayTradeWapPayRequest request = new AlipayTradeWapPayRequest();
            AlipayTradeWapPayModel model = new AlipayTradeWapPayModel();
            model.OutTradeNo = OutTradeNo;
            model.TotalAmount = TotalAmount;
            model.Subject = "设备续期";
            model.ProductCode = "QUICK_WAP_WAY";
            model.SellerId = Settings.Current.AliSellerId;
            //var url = $"{DHSetting.Current.CurDomainUrl}/DeviceLicence/PayCallBack?Id={OutTradeNo}";
            var url = $"{Settings.Current.NotifyUrl}/DeviceLicence/PayCallBack";
            model.QuitUrl = url;
            request.SetBizModel(model);
            request.SetReturnUrl(url);
            //AlipayTradeWapPayResponse response = alipayClient.pageExecute(request, null, "POST");
            // 如果需要返回GET请求，请使用
            AlipayTradeWapPayResponse response = alipayClient.pageExecute(request, null, "GET");
            return response;
        }

        /// <summary>
        /// 查询支付宝支付订单
        /// </summary>
        /// <param name="OutTradeNo">商户订单号</param>
        /// <returns></returns>
        public static AlipayTradeQueryResponse AlipayTradeQuery(String OutTradeNo)
        {
            IAopClient alipayClient = new DefaultAopClient(SetAlipayConfig());
            AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
            AlipayTradeQueryModel model = new AlipayTradeQueryModel();
            model.OutTradeNo = OutTradeNo;
            request.SetBizModel(model);
            AlipayTradeQueryResponse response = alipayClient.Execute(request);
            return response;
        }

        /// <summary>
        /// 关闭支付宝支付订单
        /// </summary>
        /// <param name="OutTradeNo">商户订单号</param>
        /// <returns></returns>
        public static AlipayTradeCloseResponse AlipayTradeClose(String OutTradeNo)
        {
            IAopClient alipayClient = new DefaultAopClient(SetAlipayConfig());
            AlipayTradeCloseRequest request = new AlipayTradeCloseRequest();
            AlipayTradeCloseModel model = new AlipayTradeCloseModel();
            model.OutTradeNo = OutTradeNo;
            request.SetBizModel(model);
            AlipayTradeCloseResponse response = alipayClient.Execute(request);
            return response;
        }

        /// <summary>
        /// 创建微信支付客户端
        /// </summary>
        /// <returns></returns>
        public static WechatTenpayClient WxTenpayClient()
        {
            // 创建微信支付客户端
            var client = new WechatTenpayClient(new WechatTenpayClientOptions()
            {
                MerchantId = Settings.Current.WxMchId,
                MerchantV3Secret = Settings.Current.WxAppSecret,
                MerchantCertificateSerialNumber = Settings.Current.WxSerialNumber,
                MerchantCertificatePrivateKey = Settings.Current.WxPrivateKey,
                PlatformAuthScheme = PlatformAuthScheme.Certificate,
                PlatformCertificateManager = new InMemoryCertificateManager()
            });
            return client;
        }

        public async static Task<CreatePayTransactionH5Response> WxTradeWapPay(String OutTradeNumber, Int32 TotalAmount)
        {
            // 创建微信支付客户端
            var client = WxTenpayClient();

            // 构建统一下单请求
            var request = new CreatePayTransactionH5Request()
            {
                AppId = Settings.Current.WxAppId,
                Description = "设备续期",
                OutTradeNumber = OutTradeNumber, // 订单号
                NotifyUrl = Settings.Current.NotifyUrl,
                Amount = new CreatePayTransactionH5Request.Types.Amount()
                {
                    Total = TotalAmount // 单位：分
                },
                Scene = new CreatePayTransactionH5Request.Types.Scene()
                {
                    ClientIp = "**************", // 客户端 IP 地址
                    H5 = new CreatePayTransactionH5Request.Types.Scene.Types.H5()
                    {
                        Type = "Wap", // 场景类型，固定为 "Wap"
                        AppName = Settings.Current.WxAppName, // 应用名称
                        AppUrl = Settings.Current.NotifyUrl // 应用网址
                    }
                }
            };
            
            // 发送请求
            var response = await client.ExecuteCreatePayTransactionH5Async(request);
            return response;
        }

        public async static Task<GetPayTransactionByOutTradeNumberResponse> WxGetPayTransaction(String OutTradeNumber)
        {
            // 创建微信支付客户端
            var client = WxTenpayClient();

            XTrace.WriteLine($"查询微信支付订单的OutTradeNumber：{OutTradeNumber}");

            // 构建查询请求
            var request = new GetPayTransactionByOutTradeNumberRequest()
            {
                OutTradeNumber = OutTradeNumber
            };

            // 发送请求
            var response = await client.ExecuteGetPayTransactionByOutTradeNumberAsync(request);

            // 返回响应结果
            return response;
        }

    }
}
