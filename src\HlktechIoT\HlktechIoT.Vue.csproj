<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
	<LangVersion>latest</LangVersion>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
	<AssemblyName>HlktechIoT</AssemblyName>
    <RootNamespace>HlktechIoT</RootNamespace>
    <UserSecretsId>713d51dd-659c-40ad-9e3c-f27416a9add9</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
	<AssemblyTitle>海凌科IOT云系统</AssemblyTitle>
    <Description>基于YRY.Api.Framework的海凌科IOT云系统</Description>
	<Authors>丁川</Authors>
    <Company>湖北登灏科技有限公司</Company>
    <Copyright>版权所有 © 湖北登灏科技有限公司 2020-2023</Copyright>
	<Version>1.0.2023.1124</Version>
    <FileVersion>1.0.2023.1124</FileVersion>
    <AssemblyVersion>1.0.*</AssemblyVersion>
    <Deterministic>false</Deterministic>
	<OutputPath>..\BinWeb</OutputPath>
	<DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineConstants>TRACE</DefineConstants>
	<!--<PublishReadyToRun>true</PublishReadyToRun>-->
	<!--<PublishReadyToRunComposite>true</PublishReadyToRunComposite>-->

    <!--混合打包，如启用则下面的混合打包规则也要启用-->
    <!--<RazorCompileOnBuild>true</RazorCompileOnBuild>
    <RazorCompileOnPublish>true</RazorCompileOnPublish>
    <PreserveCompilationContext>true</PreserveCompilationContext>
    <PreserveCompilationReferences>true</PreserveCompilationReferences>-->
	
	<!--不打包模板文件-->
    <RazorCompileOnBuild>false</RazorCompileOnBuild>
	<!--如果为 true，则在发布项目时编译并发出 Razor 程序集-->
    <MvcRazorCompileOnPublish>false</MvcRazorCompileOnPublish>
	  
	<!--从. net 6 SDK,[Appname].runtimesettings.dev.json文件不再是在编译时生成的默认。如果你仍然想要这个文件生成,设置GenerateRuntimeConfigDevFile属性为true。-->
    <GenerateRuntimeConfigDevFile>true</GenerateRuntimeConfigDevFile>
	<!--这个参数设置为true的dll从NuGet复制缓存的输出您的项目-->
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
	<!--ASP.NET CORE在IIS下有InProcess和OutOfProcess两种部署模式-->
	<AspNetCoreHostingModel>InProcess</AspNetCoreHostingModel>
	  
	<NoWarn>$(NoWarn);1591</NoWarn>
	<GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>
	
  <PropertyGroup Condition="'$(Configuration)'=='Debug'">
    <DefineConstants>$(DefineConstants);DEBUG</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
	
  <ItemGroup>
    <Content Update="wwwroot\**\*.*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
	
  <ItemGroup>
    <None Update="Run.cmd">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
	
  <!--打包模板文件夹规则，混合编译，部分预编译，部分动态编译，此处如没有设置规则，则动态编译、混合编译无效-->
  <!--<ItemGroup>
    <MvcRazorFilesToCompile Include="Views\**\*.cshtml;EmailTemplates\**\*.cshtml" Exclude="wwwroot\themes\**\*.cshtml;" />
  </ItemGroup>-->
	
  <ItemGroup>
	<!-- 我们复制整个\App_Data目录。 但是我们忽略了JSON文件和数据保护密钥  -->
    <Content Include="App_Data\**" CopyToPublishDirectory="PreserveNewest" Exclude="App_Data\*.json" />
    <Content Remove="App_Data\*.json" />
    <Compile Remove="Entity\Config\**" />
    <Compile Remove="Entity\Log\**" />

    <Compile Remove="Plugins\**;Themes\**" />

    <Content Remove="Entity\Config\**" />

    <Content Remove="Entity\Log\**" />
    <Content Remove="Plugins\**;Themes\**" />
    <EmbeddedResource Remove="Entity\Config\**" />
    <EmbeddedResource Remove="Entity\Log\**" />
    <EmbeddedResource Remove="Plugins\**;Themes\**" />
    <None Remove="Entity\Config\**" />
    <None Remove="Entity\Log\**" />
    <None Remove="Plugins\**;Themes\**" />
    <None Include="Plugins\**" CopyToPublishDirectory="PreserveNewest" Exclude="Plugins\**\runtimes\**;Plugins\**\ref\**;Plugins\**\*.pdb" />

    <Content Include="Themes\**" CopyToPublishDirectory="PreserveNewest" CopyToOutputDirectory="Never" />
  </ItemGroup>
	
  <ItemGroup>
    <None Remove="App_Data\placeholder.txt" />
  </ItemGroup>

  <ItemGroup>
    <!-- 此设置修复了vs2019中websdk中此更新导致的问题
    https://github.com/aspnet/websdk/commit/7e6b193ddcf1eec5c0a88a9748c626775555273e#diff-edf5a48ed0d4aa5a4289cb857bf46a04
    因此，我们恢复了标准配置行为（没有输出目录的副本）以避免发布期间出现“重复dll”错误。我们还可以根据以下条件使用“ExcludeConfigFilesFromBuildOutput”https://github.com/aspnet/AspNetCore/issues/14017 -->
    <Content Update="**\*.config;**\*.json" CopyToOutputDirectory="Never" CopyToPublishDirectory="PreserveNewest" />
  </ItemGroup>

  <ItemGroup>
	<PackageReference Include="DH.NRedis.Extensions" Version="4.12.2025.619-beta1116" />
	<PackageReference Include="DH.NStardust.Extensions" Version="4.12.2025.621-beta0339" />
	<PackageReference Include="DH.Swagger" Version="3.91.2025.620-beta1043" />
	<PackageReference Include="DH.VueCliMiddleware" Version="4.12.2025.619-beta1143" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.0" />
	<PackageReference Include="DH.LettuceEncrypt" Version="4.0.2025.61700080" />
	<PackageReference Include="YRY.Api.Framework" Version="0.7.2025.6170068">
      <CopyToOutputDirectory>lib\net7.0\*.xml</CopyToOutputDirectory>		
	</PackageReference>
  </ItemGroup>
	
  <ItemGroup>
	<PackageReference Include="Microsoft.SourceLink.GitHub" Version="8.0.0">
		<PrivateAssets>all</PrivateAssets>
		<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	</PackageReference>
  </ItemGroup>
	
  <!-- 此目标在“Build”目标之后执行。
    我们使用它来清理文件夹中不必要和过时库中的插件。 -->
  <Target Name="DHTarget" AfterTargets="Build">
    <ItemGroup>
      <!-- 获取插件描述文件以获取插件路径 -->
      <PluginsDescription Include="$(MSBuildProjectDirectory)\Plugins\**\plugin.json;" />
      <!-- 获取所有插件的路径 -->
      <PluginsFolders Include="@(PluginsDescription->'%(relativedir)')" />

      <!-- 获取ClearPluginAssemblies项目的路径 -->
      <ClearPluginAssemblies Include="$(MSBuildProjectDirectory)\..\Build\ClearPluginAssemblies.proj" />

    </ItemGroup>
    <PropertyGroup>
      <PluginsFolders>@(PluginsFolders)</PluginsFolders>
    </PropertyGroup>
    <!-- 当.NETCore构建项目时，它会将所有引用的库复制到输出文件夹。
	对于插件，它会创建太多不必要的文件，只会占用空间。
	目前您不能禁用此行为。这就是为什么我们必须从插件输出目录中手动删除所有不必要的库。 -->
    <MSBuild Projects="@(ClearPluginAssemblies)" Properties="PluginPath=$(PluginsFolders)" Targets="DHClear" />

  </Target>
	
  <PropertyGroup>
    <!--公共语言运行库（CLR）支持两种类型的垃圾收集：	工作站垃圾收集（在所有系统上都可用）和服务器垃圾收集，	其在多处理器系统上可用。	对于单处理器计算机，默认工作站垃圾收集应该是最快的选项。	工作站或服务器均可用于两台处理器计算机。	对于两个以上的处理器，服务器垃圾收集应该是最快的选择。
	
	有关GC的更多详细信息，请参见此处: https://docs.microsoft.com/en-us/dotnet/standard/garbage-collection/fundamentals-->
    <ServerGarbageCollection>false</ServerGarbageCollection>
    <!--在工作站或服务器垃圾收集中，您可以启用并发垃圾收集，这使得线程能够与执行垃圾的专用线程同时运行
	收集的大部分时间。并发垃圾收集使交互式应用程序能够通过最小化集合的暂停。托管线程可以在并发垃圾收集线程正在运行。这导致在正在进行垃圾收集。要在多个进程运行时提高性能，请禁用并发垃圾收集。
	
	此处有更多详细信息: https://docs.microsoft.com/en-us/dotnet/standard/garbage-collection/fundamentals#concurrent-garbage-collection-->
    <ConcurrentGarbageCollection>false</ConcurrentGarbageCollection>
  </PropertyGroup>
	
  <!--设置构建后从NuGet包中复制XML文档-->
  <Target Name="AfterTargetsBuild" AfterTargets="Build">
      <ItemGroup>
             <PackageReferenceFiles Condition="%(PackageReference.CopyToOutputDirectory) != ''" Include="$(NugetPackageRoot)\%(PackageReference.Identity)\%(PackageReference.Version)\%(PackageReference.CopyToOutputDirectory)" />
      </ItemGroup>
      <Copy SourceFiles="@(PackageReferenceFiles)" DestinationFolder="$(OutDir)" />
  </Target>
	
  <!--设置发布后从NuGet包中复制XML文档-->
  <Target Name="AfterTargetsPublish" AfterTargets="Publish">
      <ItemGroup>
             <PackageReferenceFiles Condition="%(PackageReference.CopyToOutputDirectory) != ''" Include="$(NugetPackageRoot)\%(PackageReference.Identity)\%(PackageReference.Version)\%(PackageReference.CopyToOutputDirectory)" />
      </ItemGroup>
      <Copy SourceFiles="@(PackageReferenceFiles)" DestinationFolder="$(PublishDir)" />
  </Target>
	
  <PropertyGroup>
    <SpaRoot>ClientApp\</SpaRoot>
    <DefaultItemExcludes>$(DefaultItemExcludes);$(SpaRoot)node_modules\**</DefaultItemExcludes>
  </PropertyGroup>
	
  <ItemGroup>
    <!-- 不要发布 SPA 源文件，但要在项目文件列表中显示它们 -->
    <Content Remove="$(SpaRoot)**" />
    <None Include="$(SpaRoot)**" Exclude="$(SpaRoot)node_modules\**" />
  </ItemGroup>
	
  <Target Name="DebugEnsureNodeEnv" BeforeTargets="Build">
    <!-- Build Target:  Ensure Node.js is installed -->
    <Exec Command="node --version" ContinueOnError="true">
      <Output TaskParameter="ExitCode" PropertyName="ErrorCode" />
    </Exec>
    <Exec Command="npm --version" ContinueOnError="true">
      <Output TaskParameter="ExitCode" PropertyName="ErrorCode" />
    </Exec>
    <Error Condition="'$(ErrorCode)' != '0'" Text="Node.js and npm are required to build and run this project. To continue, please install Node.js from https://nodejs.org/, and then restart your command prompt or IDE." />
  </Target>
	
  <Target Name="EnsureNodeModulesInstalled" BeforeTargets="Build" Inputs="package.json" Outputs="packages-lock.json" Condition="!Exists('$(SpaRoot)node_modules')">
    <!-- Build Target: Restore NPM packages using npm -->
    <Message Importance="high" Text="Restoring dependencies using 'npm'. This may take several minutes..." />
    <Exec WorkingDirectory="$(SpaRoot)" Command="npm install" />
  </Target>
	
  <Target Name="PublishRunWebpack" AfterTargets="ComputeFilesToPublish">
    <!-- Build Target: Run webpack dist build -->
    <Message Importance="high" Text="Running npm build..." />
    <Exec WorkingDirectory="$(SpaRoot)" Command="npm run build" />

    <!-- Include the newly-built files in the publish output -->
    <ItemGroup>
      <DistFiles Include="$(SpaRoot)dist\**" />
      <ResolvedFileToPublish Include="@(DistFiles->'%(FullPath)')" Exclude="@(ResolvedFileToPublish)">
        <RelativePath>%(DistFiles.Identity)</RelativePath>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        <ExcludeFromSingleFile>True</ExcludeFromSingleFile>
      </ResolvedFileToPublish>
    </ItemGroup>
  </Target>

</Project>
