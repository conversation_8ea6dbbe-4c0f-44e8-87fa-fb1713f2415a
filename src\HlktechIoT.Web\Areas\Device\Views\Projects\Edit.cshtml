﻿@model Project
@{
    Html.AppendTitleParts(T("编辑项目").Text);
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        padding-top: 30px;
        padding-left: 30px;
    }

    .layui-form-item.btn {
        padding-top: 10px;
        padding-left: 115px;
    }

    .label-width span {
        color: #f00;
    }

    .layui-form-label {
        width: 94px;
    }

    .upload {
        cursor: pointer;
        max-width: 120px;
        max-height: 90px;
    }
    .select{
        max-width: 420px !important;
        border: 1px solid #e6e6e6;
    }
</style>
<div class="containers">
    <form class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("名称")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="Name" placeholder="@T("请输入产品名称")" autocomplete="off" class="layui-input" lay-filter="Versions" value="@Model.Name">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("ProjectKey")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="ProjectKey" placeholder="@T("请输入ProjectKey")" autocomplete="off" class="layui-input" lay-filter="Versions" disabled value="@Model.ProjectKey">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("备注")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="remark" placeholder="@T("请输入备注")" autocomplete="off" class="layui-input" lay-filter="Versions" value="@Model.Remark">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("AppKey")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="AppKey" placeholder="@T("请输入AppKey")" autocomplete="off" class="layui-input" lay-filter="Versions" disabled value="@Model.AppKey">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("AppSecret")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="AppSecret" placeholder="@T("请输入AppSecret")" autocomplete="off" class="layui-input" lay-filter="Versions" disabled value="@Model.AppSecret">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("PushAppKey")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="PushAppKey" placeholder="@T("请输入PushAppKey")" autocomplete="off" class="layui-input" lay-filter="Versions" disabled value="@Model.PushAppKey">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("PushAppSecret")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="PushAppSecret" placeholder="@T("请输入PushAppAppSecret")" autocomplete="off" class="layui-input" lay-filter="Versions" disabled value="@Model.PushAppSecret">
            </div>
        </div>

        <div class="layui-form-item btn">
            <input hidden name="Id" value="@Model.Id" />
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" value="@T("保存")" />
        </div>
    </form>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        form.on('submit(Submit)', function (data) {
            if (data.field.Name.length == 0) {
                abp.notify.warn("@T("名称不能为空")");
                return;
            }

            var waitIndex = parent.layer.load(2);

            abp.ajax({
                url: "@Url.Action("Edit")",
                contentType : "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                dg.reload('tables');
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

    });
</Script>