<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
    <AssemblyTitle>RPC服务插件</AssemblyTitle>
    <Description>IoT服务平台</Description>
    <Company>深圳市海凌科电子有限公司</Company>
	<Authors>丁川</Authors>
	<Copyright>版权所有(C) 深圳市海凌科电子有限公司 2009-2024</Copyright>
	<VersionPrefix>0.1</VersionPrefix>
    <VersionSuffix>$([System.DateTime]::Now.ToString(`yyyy.MMdd`))</VersionSuffix>
    <Version>$(VersionPrefix).$(VersionSuffix)</Version>
    <FileVersion>$(Version)</FileVersion>
    <AssemblyVersion>$(VersionPrefix).*</AssemblyVersion>
    <Deterministic>false</Deterministic>
    <ImplicitUsings>enable</ImplicitUsings>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>
	
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>$(NoWarn);1701;1702;1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\HlktechIoT.IoTService\HlktechIoT.IoTService.csproj" />
  </ItemGroup>

</Project>
