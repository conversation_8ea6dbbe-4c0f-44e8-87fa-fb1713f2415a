﻿@model Device
@{
    Html.AppendTitleParts(T("设备调试").Text);

    var dgPage = (Pek.Webs.HttpContext.Current.Request.RouteValues["controller"] + "_" + Pek.Webs.HttpContext.Current.Request.RouteValues["action"]).ToLower();

    var id = Model.Id;

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/Storage.js");
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/initSignalr.js");
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .selectLabel {
        min-width: 50px !important;
        min-height: 30px !important;
        line-height: 30px !important;
    }

    .selectBox {
        width: 500px !important;
        height: 40px !important;
        display: flex !important;
        background: transparent !important;
        /* border:2px solid black; */
    }

    .dg-form {
        /* position:absolute; */
        /* top: 30px; */
        z-index: 1000;
    }

    .Remark {
        overflow: hidden; /*超出部分隐藏*/
        white-space: nowrap; /*禁止换行*/
        text-overflow: ellipsis; /*省略号*/
        cursor: pointer;
    }

    .mask {
        position: fixed;
    }
    .copy{
        position: absolute;
        bottom: 10px;
        right: 10px;
    }
    .layui-table-cell {
        width: auto;
    }
    
    /* 操作列自适应样式 */
    .layui-table-fixed-r .layui-table-body {
        overflow-x: hidden !important;
    }

    .operation-column {
        white-space: nowrap;
        padding: 0 5px; /* 添加左右边距 */
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center; /* 垂直居中 */
        flex-wrap: nowrap;
        height: 100%; /* 确保容器占满单元格高度 */
        width: 100%; /* 确保容器占满单元格宽度 */
        box-sizing: border-box; /* 确保内边距不增加总宽度 */
    }

        .operation-column .pear-btn {
            margin-right: 6px; /* 保持原有按钮间距 */
            padding: 0 8px; /* 保持按钮内边距 */
            height: 30px; /* 保持按钮高度 */
            line-height: 30px; /* 行高与高度一致 */
            min-width: 44px; /* 按钮最小宽度 */
            text-align: center;
            flex-grow: 1; /* 按钮自动扩展占用空间 */
            @* flex-basis: 0; /* 所有按钮基础宽度相同 */ *@
        }

            .operation-column .pear-btn:last-child {
                margin-right: 0;
            }

    /* 确保表格单元格内容居中 */
    .layui-table-cell {
        text-align: center;
        padding: 0 2px; /* 保持单元格内边距 */
    }

    /* 固定列样式优化 */
    .layui-table-fixed {
        height: auto !important;
    }

    /* 确保表格右侧固定列正确显示 */
    .layui-table-fixed-r {
        right: 0 !important;
    }

        /* 优化表格右侧固定列的单元格 */
        .layui-table-fixed-r .layui-table-cell {
            padding: 0;
        }

    /* .layui-table-cell {
                        white-space: normal;
                    } */
</style>

<style>
    /* 把显示完整按钮 隐藏掉 */
    /* .layui-table-grid-down{
        display: none !important;
    } */
    .noneHover::before{
        position: absolute;
        right: 0;
        content: '';
        width: 100%;
        height: 100%;
        background-color: transparent;
        z-index: 1000;
        display: none;
    }
    .noneHover:hover::before{
        display: block;
    }
</style>

<div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
    <input hidden id="cacheTags" />
    <ul class="layui-tab-title" id="TabControl">
        <li lay-id="11" id="layuiTab_1" class="layui-this isLoaded">@T("设备属性")</li>
        <li lay-id="22" id="layuiTab_2">@T("历史日志")</li>
        <li lay-id="33" id="layuiTab_3">@T("历史数据")</li>
    </ul>
    <div class="layui-tab-content">
        <div class="layui-tab-item layui-show">
            @await Html.PartialAsync("_deviceProperties")
        </div>
        <div class="layui-tab-item">
            @await Html.PartialAsync("_historicalLogs")
        </div>
        <div class="layui-tab-item">
            @await Html.PartialAsync("_historicalData")
        </div>
    </div>
</div>

<script asp-location="Footer">
        var id = '@id';

        const storage1 = new Storage(3);
        var layid = storage1.get(id + "debugging:refresh");

        $("#cacheTags").val(layid);

        $(window).on("unload", function (e) {

        });

        $(window).on("beforeunload", function (e) {
            storage1.set(id + "debugging:refresh", $("#cacheTags").val());
        });

        layui.use(['table', 'abp', 'dg', 'element', 'form', 'dgcommon'], function () {
            var $ = layui.jquery;
            var abp = layui.abp;
            var form = layui.form;
            var table = layui.table;
            var dg = layui.dg;
            var os = layui.dgcommon;
            var element = layui.element;
            var laydate = layui.laydate;

            var iframeName = window.name;
            var index = iframeName.replace("iframe", "");

            element.tabChange('docDemoTabBrief', layid); //假设当前地址为：http://a.com#docDemoTabBrief=222，那么选项卡会自动切换到“发送消息”这一项
            element.on('tab(docDemoTabBrief)', function (data) {
                if (data.index == 1) { //解决fixed显示bug
                    //active.reload1()
                }
                $("#cacheTags").val(this.getAttribute('lay-id'));
            });

            // 按钮配置集中定义
        
        window.aaa = function (rowData) {
            var operationButtons = [];
            // 系统记录只显示编辑按钮
            if (rowData.Readonly) {
                return
            } 
            else {
                operationButtons = operationButtons.concat([
                    { text: '设置', event: 'transmission', class: 'pear-btn-primary' },
                    { text: '服务', event: 'psetting', class: 'pear-btn-warming' }
                ]);
            }
            
            window.operationButtons = operationButtons
        }
        

        // 将按钮配置保存到全局变量，供模板使用
        

        // 动态计算操作列宽度的函数
        function calculateOperationColumnWidth() {
            try {
                var buttonMargin = 6; // 按钮间距
                var extraPadding = 24; // 额外边距
                var baseButtonPadding = 20; // 按钮内边距
                if (operationButtons.length === 0) {
                    return 100;
                }

                var totalWidth = 0;

                operationButtons.forEach(function(button) {
                    // 调整文字宽度估算：中文字符约16px，英文字符约9px
                    var text = button.text;
                    var textWidth = 0;
                    for (var i = 0; i < text.length; i++) {
                        var char = text.charAt(i);
                        if (/[\u4e00-\u9fa5]/.test(char)) {
                            textWidth += 16; // 中文字符
                        } else {
                            textWidth += 9;  // 英文字符
                        }
                    }

                    var buttonWidth = textWidth + baseButtonPadding;
                    totalWidth += buttonWidth;
                });

                // 加上按钮间距和额外边距
                totalWidth += (operationButtons.length - 1) * buttonMargin + extraPadding;

                // 设置最小宽度
                if (totalWidth < 120) {
                    totalWidth = 120;
                }

                console.log('计算的操作列宽度:', totalWidth, '按钮数量:', operationButtons.length, '按钮配置:', operationButtons);
                return totalWidth;
            } catch (error) {
                @* console.error('计算操作列宽度时出错:', error); *@
                return 200;
            }
        }

        // 应用操作列宽度的函数
        function applyOperationColumnWidth() {
            try {
                var calculatedWidth = calculateOperationColumnWidth();

                var fixedRightHeader = $('.layui-table-fixed-r .layui-table-header table thead tr th:last-child');
                var fixedRightBody = $('.layui-table-fixed-r .layui-table-body table tbody tr td:last-child');

                if (fixedRightHeader.length > 0) {
                    fixedRightHeader.css('width', calculatedWidth + 'px');
                    fixedRightHeader.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                if (fixedRightBody.length > 0) {
                    fixedRightBody.css('width', calculatedWidth + 'px');
                    fixedRightBody.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                var mainTableHeader = $('#tablist').next('.layui-table-view').find('.layui-table-header table thead tr th:last-child');
                var mainTableBody = $('#tablist').next('.layui-table-view').find('.layui-table-body table tbody tr td:last-child');

                if (mainTableHeader.length > 0) {
                    mainTableHeader.css('width', calculatedWidth + 'px');
                    mainTableHeader.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                if (mainTableBody.length > 0) {
                    mainTableBody.css('width', calculatedWidth + 'px');
                    mainTableBody.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                console.log('已应用操作列宽度:', calculatedWidth);
            } catch (error) {
                console.error('应用操作列宽度时出错:', error);
            }
        }

        // 监听窗口大小变化
        $(window).on('resize', function() {
            try {
                setTimeout(function() {
                    applyOperationColumnWidth();
                    console.log('窗口大小变化，已重新调整操作列宽度');
                }, 100);
            } catch (error) {
                console.error('窗口resize时出错:', error);
            }
        });

            table.render({
                elem: '#tablist'
                , url: '@Url.Action("ProductFunctionList", new { Id = Model.Id })'
                , page: false //开启分页
                , toolbar: '#user-toolbar1'
                , defaultToolbar: [{
                    title: '@T("刷新")',
                    layEvent: 'refresh',
                    icon: 'layui-icon-refresh',
                }, 'filter', 'print']
                , cellMinWidth: 60
                , cols: [[
                    // { field: 'DeviceName', title: '@T("设备")', width: 130 },
                    { field: 'Name', title: '@T("名称")',minWidth: 176 }
                    , { field: 'NickName', title: '@T("昵称")', width: 176 }
                    , { field: 'DataType', title: '@T("类型")', width: 80, toolbar: '#DataType' }
                    , { field: 'Readonly', title: '@T("只读")', width: 60, toolbar: '#ReadOnly' }
                    // , { field: 'Value', title: '@T("数值")',width:240 }
                    , { title: '@T("内容")', minWidth: 100,templet:(d)=>{
                        if (d.Value != '' && d.Value != null) {
                            return `
                            <div class="noneHover" lay-event="readValue" style="cursor:pointer;color:rgb(50,50,50)"> ${d.Value}</div>
                            `; // 禁止溢出显示
                        }
                        else {
                            return '';
                        }
                    } }
                    , { field: 'FunctionName', title: '@T("功能定义")', width: 110 }
                    , { field: 'DefaultValue', title: '@T("默认值")', minWidth: 280 }
                    , { field: 'Unit', title: '@T("单位")', width: 60 }
                    , { field: 'UnitName', title: '@T("单位名称")', width: 90 }
                    , { field: 'Length', title: '@T("长度")', width: 60 }
                    , { field: 'Address', title: '@T("点位地址")', minWidth: 160 }
                    , { field: 'Rule', title: '@T("解析规则")', minWidth: 160 }
                    , { field: 'LastPost', title: '@T("最后上报")', width: 160, templet: function (d) {
                        if (d.LastReportTime && !/^0001/.test(d.LastReportTime)) {
                            return d.LastReportTime;
                        }
                        return "";
                    } }
    @* , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: 220 } 换个模式 *@
                    , { fixed: 'right', title: ' @T("操作")', toolbar: '#operateTool', width: calculateOperationColumnWidth() }
                ]]
                , limit: 12
                , page: 1
                , limits: [10, 12, 20, 30, 50, 100]
                , height: 'full-160'
                , id: 'ProductFunctionTables'
                , done: () => {
                    try {
                        // 延迟执行，确保DOM已经渲染完成
                        setTimeout(function() {
                            applyOperationColumnWidth();
                        }, 200);
                    } catch (error) {
                        console.error('表格done回调中出错:', error);
                    }
                }
            });

            window.active = {
                reload: function () {
                    table.reload('ProductFunctionTables', {
                        where: {
                            Id:'@Model.Id',
                            key: $("#key").val(),
                        }
                    });
                },
                reload1: function () {
                    table.reload('HistoricalLogsTables', {
                        where: {
                            Id:'@Model.Id',
                            key: $("#key1").val(),
                        }
                    });
                },
                reload3: function () {
                    table.reload('historicalData_TableId',
                    {
                        where: {
                            deviceId:'@Model.Id',
                            key: $("#HistoricalDatakey").val(),
                            start:$("#start").val(),
                            end:$("#end").val(),
                            names:$("#HistoricalDatakey").val()
                            },
                            page: {
                                curr: 1
                            }
                    });
                }
            };

            table.on('toolbar(tool)', function (obj) {
                // console.log('第一次监听',obj);
                var data = obj.data;
                if (obj.event === 'select') {
                    window.select();
                    } else if (obj.event === 'transmissions') {
                window.transmissions(data);
                } else if (obj.event === 'refresh') {
                    active.reload();
                }
            });

            table.on('tool(tool)', function (obj) {
                // console.log('第一次监听2',obj);
                var data = obj.data;
                if (obj.event === 'psetting') {
                    window.psetting(data);
                } else if (obj.event === 'transmission') {
                    window.transmission(data);
                }else if (obj.event === 'readValue') {
                    window.readValue(obj.data)
                }
            });

            // input搜索监听事件 - ProductFunctionTables
            $("#key").on("input", function (e) {
                active.reload();
            });


            window.psetting = function (data) {
                layer.open({
                    type: 2,
                    title: '@T("服务设置")',
                    content: "@Url.Action("PropertiesSetting")" + abp.utils.formatString("?id={0}", data.Id),
                    area: ["460px", "336px"],
                    shade: 0.1,
                    btn: ['@T("确定")', '@T("取消")'],
                    yes: function (index, layero) {
                        window['layui-layer-iframe' + index].submitForm();
                    }
                });
            }

            window.readValue =  (data) => {
                // 在此处输入 layer 的任意代码
                layer.open({
                    type: 1, // page 层类型
                    area: ['320px', '200px'],
                    title: '@T("查看数值")',
                    shade: 0.1, // 遮罩透明度
                    shadeClose: true, // 点击遮罩区域，关闭弹层
                    maxmin: true, // 允许全屏最小化
                    anim: 0, // 0-6 的动画形式，-1 不开启
                    content: `<div style="max-width:400px;margin:10px;word-wrap: break-word;line-height:20px;"> ${data.Value || ''} </div>`
                });
            }

            window.transmission = function (data) {
                layer.open({
                    type: 2,
                    title: '@T("属性设置")',
                    content: "@Url.Action("Transmission")" + abp.utils.formatString("?Id={0}", data.Id),
                    area: ["460px", "336px"],
                    shade: 0.1,
                    btn: ['@T("确定")', '@T("取消")'],
                    yes: function (index, layero) {
                        window['layui-layer-iframe' + index].submitForm();
                    }
                });
            }

            window.transmissions = function (data) {
            layer.open({
                offset:'0px',
                type: 2,
                title: "@T("多属性设置")",
                content: "@Url.Action("Transmissions")" + abp.utils.formatString("?Id={0}", @id),
                area: ["970px", "780px"],
                shade: 0.1,
                btn: ['@T("确定")', '@T("取消")'],
                yes: function (index, layero) {
                    window['layui-layer-iframe' + index].submitForm();
                }
            });
        }

            table.render({
                elem: '#tablist1'
                , url: '@Url.Action("HistoricalLogs", new { Id = Model.Id })'
                , page: false //开启分页
                , toolbar: '#user-toolbar'
                , defaultToolbar: [{
                    title: '@T("刷新")',
                    layEvent: 'refresh',
                    icon: 'layui-icon-refresh',
                }, 'filter', 'print']
                ,expandedWidth:0
                , cellMinWidth: 60
                , cols: [[
                    // { field: 'Name', title: '@T("名称")', minWidth: 130 },
                    { field: 'AreaName', title: '@T("城市")', minWidth: 130 }
                    , { field: 'Action', title: '@T("操作")', minWidth: 120 }
                    , { field: 'Success', title: '@T("成功")', minWidth: 60, toolbar: '#Success' }
                    , { title: '@T("内容")', minWidth: 100,templet:(d)=>{
                        if (d.Remark != '' && d.Remark != null) {
                            return `
                            <div class="noneHover" lay-event="readDetail_tool1" style="cursor:pointer;color:rgb(50,50,50)"> ${d.Remark}</div>
                            `; // 禁止溢出显示
                        }
                        else {
                            return '';
                        }
                    } }
                    , { field: 'Version', title: '@T("版本")', width: 170 }
                    , { field: 'Creator', title: '@T("创建者")', minWidth: 200 }
                    , { field: 'CreateTime', title: '@T("创建时间")', minWidth: 160 }
                    , { field: 'CreateIP', title: '@T("创建地址")', minWidth: 130 }
                ]]
                , limit: 12
                , page: 1
                , limits: [10, 12, 20, 30, 50, 100]
                , height: 'full-160'
                , id: 'HistoricalLogsTables'
            });

            table.on('toolbar(tool1)', function (obj) {
                if (obj.event === 'select') {
                    window.select();
                } else if (obj.event === 'refresh') {
                    active.reload1();
                }
            });
             table.on('tool(tool1)', function (obj) {
                console.log(obj);
                if (obj.event === 'readDetail_tool1') {
                    // console.log('查看详情：',obj);
                    window.readDetail_tool1(obj.data);
                }
            });

            window.readDetail_tool1 =  (data) => {
                // 在此处输入 layer 的任意代码
                layer.open({
                    type: 1, // page 层类型
                    area: ['420px', '300px'],
                    title: '@T("查看详情")',
                    shade: 0.1, // 遮罩透明度
                    shadeClose: true, // 点击遮罩区域，关闭弹层
                    maxmin: true, // 允许全屏最小化
                    anim: 0, // 0-6 的动画形式，-1 不开启
                    content: `
                        <div style="max-width:400px;margin:10px;word-wrap: break-word;line-height:20px;" id="copyText"> ${data.Remark} </div>
                    `,
                    btn:['@T("一键复制")'],
                    yes: function (index, layero, that) {
                        var text = $("#copyText").text();
                        if (navigator.clipboard) {
                            navigator.clipboard.writeText(text);
                        } else {
                            const oInput = document.createElement('input');
                            oInput.value = text;
                            document.body.appendChild(oInput);
                            oInput.select(); // 选择对象
                            document.execCommand("Copy"); // 执行浏览器复制命令
                            document.body.removeChild(oInput);
                        }
                        layer.msg('复制成功');   
                        // layer.close(index);
                    },
        
                });
                //    <button class="pear-btn pear-btn-primary pear-btn-md copy" lay-active="copy">@T("一键复制")</button>
            }
            // var util = layui.util;
            // //处理属性 为 lay-active 的所有元素事件
            // util.event('lay-active', {
            //     copy: function(){
            //         // 兼容性复制
                   
            //     }
            // });

            // input搜索监听事件 - HistoricalLogsTables
            $("#key1").on("input", function (e) {
                active.reload1();
            });

            window.saveCallback = function (data) {
                layer.close(data.index);
                abp.notify.success(data.msg);
                active.reload();
            }

    // -------------------------------------------------------------------------------------以下是历史数据页面的函数
            table.render({
                elem: '#historicalDataTable'
                , url: '@Url.Action("GetHistoryDataList")'
                , page: true //开启分页
                , toolbar: '#user-toolbar'
                  , defaultToolbar: [{
                    title: '@T("刷新")',
                    layEvent: 'refresh',
                    icon: 'layui-icon-refresh',
                }, 'filter', 'print']
                , cellMinWidth: 80
                , cols: [[
                    // { field: 'Id', title: '@T("ID")', width: 180  }
                    // , { field: 'DeviceId', title: '@T("设备编号")', width: 88  }
                    { field: 'Name', title: '@T("名称")', minWidth: 140 }
                    , { field: 'Kind', title: '@T("类型")', width: 100 }
                    // , { field: 'TraceId', title: '@T("跟踪ID")', width:250  }
                    , { title: '@T("内容")', minWidth: 100,templet:(d)=>{
                            if (d.Value != '' && d.Value != null) {
                                return `
                                <div class="noneHover" lay-event="readDetail_tool2" style="cursor:pointer;color:rgb(50,50,50)"> ${d.Value}</div>
                                `; // 禁止溢出显示
                            }
                            else {
                                return '';
                            }
                        } }
                    , { field: 'Timestamp', title: '@T("设备数据时间")', width: 164, align: 'left' }
                    // , { field: 'Timestamp', title: '@T("时间戳")', width: 110 }
                    // , { field: 'Value', title: '@T("追踪标识")', minWidth: 90, toolbar: '#Value3',align:'center' }
                    , { field: 'Creator', title: '@T("创建者")', minWidth: 200  }
                    , { field: 'CreateTime', title: '@T("创建时间")', width: 164  }
                    , { field: 'CreateIP', title: '@T("创建IP地址")', width: 140  }

                ]]
                , limit: 13
                , limits: [10, 13, 20, 30, 50, 100]
                , height: 'full-160'
                , id: 'historicalData_TableId'
                , where:{
                    deviceId:'@Model.Id',
                    // start:$("#start").val(),
                    // end:$("#end").val()

                }
            });


            $("#HistoricalDatakey").on("input", function (e) {
                // console.log('监听input',e);
                // active.reload();
            });

            $("#SearchHistoricalData").click(function (e) {
                // console.log($("#start").val());
                // console.log('点击搜索：',e);
                active.reload3();
            });

            table.on('toolbar', function (obj) {
                console.log(obj);
                if (obj.event === 'select') {
                    window.select();
                } else if (obj.event === 'refresh') {
                      active.reload3();
                }
            });
            table.on('tool(tool2)', function (obj) {
                if (obj.event === 'readDetail_tool2') {
                    // console.log('查看详情：',obj);
                    window.readDetail_tool2(obj.data);
                }
            });

            window.readDetail_tool2 =  (data) => {
                // 在此处输入 layer 的任意代码
                layer.open({
                    type: 1, // page 层类型
                    area: ['420px', '300px'],
                    title: '@T("查看详情")',
                    shade: 0.1, // 遮罩透明度
                    shadeClose: true, // 点击遮罩区域，关闭弹层
                    maxmin: true, // 允许全屏最小化
                    anim: 0, // 0-6 的动画形式，-1 不开启
                    content: `<div style="max-width:400px;margin:10px;word-wrap: break-word;line-height:20px;"> ${data.Value} </div>`
                });
            }
            // 获取当前日期
            function getCurrentDate() {
                var today = new Date();
                var year = today.getFullYear();
                var month = String(today.getMonth() + 1).padStart(2, '0');
                var day = String(today.getDate()).padStart(2, '0');
                var currentDate = year + '-' + month + '-' + day;
                return currentDate;
            }
            // 设置默认时间
            $("#start").val(getCurrentDate());
            $("#end").val(getCurrentDate());
              //时间插件
            var startDate = laydate.render({
                elem: '#start',
                btns: ['clear', "confirm"],//只显示清空和确定按钮
                type: 'date',       // 设置日期选择类型为年月-日
                format: 'yyyy-MM-dd',   // 设置日期的格式，这里是年-月-日
                done: function (value, date) {
                    console.log('选择后:',value+'-'+date.date);

                    $("#start").val(value);
                    checkDateValidity();
                },
                choose: function (date) {
                    // 用户选择日期的回调函数
                    // 在这里可以处理用户选择日期后的逻辑
                    laydate.close(); // 关闭日期选择器弹窗
                }
            });

            var endDate = laydate.render({
                elem: '#end',
                btns: ["clear", "confirm"],
                type: 'date',       // 设置日期选择类型为年月-日
                format: 'yyyy-MM-dd',   // 设置日期的格式，这里是年-月-日
                done: function (value, date) {
                    console.log('选择后:',value+'-'+date.date);

                    $("#end").val(value);
                    checkDateValidity();
                },
                choose: function (date) {
                    // 用户选择日期的回调函数
                    // 在这里可以处理用户选择日期后的逻辑
                    laydate.close(); // 关闭日期选择器弹窗
                }
            });

            function checkDateValidity() {
                var startValue = $("#start").val();
                var endValue = $("#end").val();

                if (startValue && endValue) {
                    var startDate = new Date(startValue + "-01");  // 这里是格式化-作-可对比的日期---格式
                    var endDate = new Date(endValue + "-01");

                    if (startDate > endDate) {
                        os.warning('开始时间不能晚于结束时间，请重新选择。');
                        $("#start").val(""); // 清空开始时间输入框
                        $("#end").val("");   // 清空结束时间输入框
                    }
                }
            }
            // 点历史数据页面的时候要刷新一下不然有bug
            $("#layuiTab_3").click(()=>{
                //active.reload3()
            })

        });
</script>
<script type="text/html" id="operateTool">
    {{window.aaa(d)}}
    <div class="operation-column">
        {{#  layui.each(window.operationButtons, function(index, button){ }}
            <a class="pear-btn {{button.class}} pear-btn-xs" lay-event="{{button.event}}">{{button.text}}</a>
        {{#  }); }}
    </div>
</script>
<script type="text/html" id="user-toolbar1">
    <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="transmissions">
        <i class="layui-icon">

        </i>
        @T("批量设置")
    </button>
</script>