﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - HlktechIoT</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/HlktechIoT.styles.css" asp-append-version="true" />
    <script asp-location="Head">
        var pleaseSelect = '@T("请选择")';
        var layuiPrint = '@T("打印")';
        var layuiExport = '@T("导出")';
        var layuiFilterColumn = '@T("筛选列")';
        var layuiArticlePage = '@T("条/页")';
        var layuiTotal = '@T("共")';
        var layuiBtn = '@T("确定")';
        var layuiGoPage = '@T("到第")';
        var layuiPage = '@T("页")';
        var layuiPrev = '@T("上一页")';
        var layuiNext = '@T("下一页")';
        var layuiNoData = '@T("无数据")';
        var layuiAsc = '@T("升序")';
        var layuiDesc = '@T("降序")';
        var layuiCloseCurrent = '@T("关 闭 当 前")';
        var layuiCloseOther = '@T("关 闭 其 他")';
        var layuiCloseAll = '@T("关 闭 全 部")';
        var layuiMenuStyle = '@T("菜单风格")';
        var layuiTopStyle = '@T("顶部风格")';
        var layuiThemeColor = '@T("主题配色")';
        var layuiMoreSettings = '@T("更多设置")';
        var layuiOpen = '@T("开")';
        var layuiClose = '@T("关")';
        var layuiMenu = '@T("菜单")';
        var layuiView = '@T("视图")';
        var layuiBanner = '@T("通栏")';
        var layuiThroughColor = '@T("通色")';
        var layuiFooter = '@T("页脚")';
        var layuiSelectAll = '@T("全选")';
        var layuiClear = '@T("清空")';
        var layuiReverseSelection = '@T("反选")';
        var layuiPeeling = '@T("换肤")';
        var layuiNoDataYet = '@T("暂无数据")';
        var layuiSearch = '@T("搜索")';
        var layuiPrevious = '@T("上一页")';
        var layuiNext = '@T("下一页")';
        var layuiNotAllowClose = '@T("前页面不允许关闭")';
        var layuiOpenAtMost = '@T("最多打开")';
        var layuiTabs = '@T("个标签页")';
    </script>
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-light bg-white border-bottom box-shadow mb-3">
            <div class="container-fluid">
                <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index">HlktechIoT</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        <li class="nav-item">
                            <a class="nav-link text-dark" asp-area="" asp-controller="Home" asp-action="Index">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-dark" asp-area="" asp-controller="Home" asp-action="Privacy">Privacy</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <div class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer text-muted">
        <div class="container">
            &copy; 2023 - HlktechIoT - <a asp-area="" asp-controller="Home" asp-action="Privacy">Privacy</a>
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
