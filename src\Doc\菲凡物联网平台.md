# 菲凡物联网平台
物联网平台为各种设备提供快速接入能力，支持Https/WebSocket/MQTT等多种标准协议以及自定义协议，提供设备管理、安全通信、规则引擎和消息下发等功能。  
向下支持连接海量设备，采集设备数据上云；向上提供开放API，应用端可通过调用开放API将指令消息下发至设备端，实现远程控制。  
菲凡物联网平台分为轻量级`ZeroIoT`与完整版`FIoT`，两者接口保持高度一致，即前者客户端可对接后者平台服务端，完全共用设备驱动。  

## 免费版
轻量级物联网平台`ZeroIoT`，支持数据采集上报及远程控制，可作为自定义物联网平台脚手架，为特定设备定制专属物联网平台。  

## 青春版
轻量级物联网平台`ZeroIoT`的完整实现，增加MQTT接入、事件报警和数据队列等功能。  

## 标准版
标准版包括`FIoT`平台全部源代码以及文档资料，一年内更新，仅可用于一个项目。  
标准版是<font color=red>开发物联网项目</font>的最佳授权方式。  

## 旗舰版
旗舰版包括`FIoT`平台全部源代码以及文档资料，三年内更新，没有项目个数限制。  
旗舰版是<font color=red>开发物联网产品</font>的授权方式，我们将针对客户需求提供整套解决方案，包括应用架构、集群部署、监控平台、大数据分析和单点登录等外围配套，提供必要技术支持，帮助客户研发形成自己的物联网平台产品。  

## 各版本区别
标准版和旗舰版的源码与资料保持一致，仅服务不同。  
|          |      类目      |           免费版           |           青春版           |           标准版           |           旗舰版           |           备注           |
| :------: | :------------: | :------------------------: | :------------------------: | :------------------------: | :------------------------: | :----------------------: |
| 基础功能 |    产品管理    | <font color=green>✔</font> | <font color=green>✔</font> | <font color=green>✔</font> | <font color=green>✔</font> |  具有相同特征的设备集合  |
|          |    设备管理    | <font color=green>✔</font> | <font color=green>✔</font> | <font color=green>✔</font> | <font color=green>✔</font> |       设备接入鉴权       |
|          |   子设备管理   |  <font color=red>✘</font>  |  <font color=red>✘</font>  | <font color=green>✔</font> | <font color=green>✔</font> |  子设备通过网关设备接入  |
|          |   物模型管理   |  <font color=red>✘</font>  |  <font color=red>✘</font>  | <font color=green>✔</font> | <font color=green>✔</font> |  支持TSL与公共平台订阅   |
|          |    HTTP接入    | <font color=green>✔</font> | <font color=green>✔</font> | <font color=green>✔</font> | <font color=green>✔</font> |    提供接口文档及例程    |
|          |    MQTT接入    |  <font color=red>✘</font>  | <font color=blue>○</font>  | <font color=green>✔</font> | <font color=green>✔</font> |    提供接口文档及例程    |
|          |  私有硬件接入  |  <font color=red>✘</font>  |  <font color=red>✘</font>  |  <font color=red>✘</font>  | <font color=green>✔</font> |     定制实现私有协议     |
|          |    数据采集    | <font color=green>✔</font> | <font color=green>✔</font> | <font color=green>✔</font> | <font color=green>✔</font> |       标准驱动模型       |
|          |    远程控制    | <font color=green>✔</font> | <font color=green>✔</font> | <font color=green>✔</font> | <font color=green>✔</font> |       标准驱动模型       |
|          |    事件报警    |  <font color=red>✘</font>  | <font color=blue>○</font>  | <font color=green>✔</font> | <font color=green>✔</font> | 数据变更事件及自定义事件 |
| 平台功能 |  时序数据存储  | <font color=green>✔</font> | <font color=green>✔</font> | <font color=green>✔</font> | <font color=green>✔</font> |  支持关系数据库按天分表  |
|          |    数据队列    |  <font color=red>✘</font>  | <font color=blue>○</font>  | <font color=green>✔</font> | <font color=green>✔</font> |  经消息队列驱动下游业务  |
|          |    数据推送    |  <font color=red>✘</font>  | <font color=blue>○</font>  | <font color=green>✔</font> | <font color=green>✔</font> |  支持HTTP/MQTT/DB/Redis  |
|          |    数据接口    | <font color=blue>○</font>  | <font color=blue>○</font>  | <font color=green>✔</font> | <font color=green>✔</font> |  业务应用经接口查询数据  |
|          |    工业协议    | <font color=blue>○</font>  | <font color=blue>○</font>  | <font color=green>✔</font> | <font color=green>✔</font> |  Modbus/OPC/BACnet/PLC   |
|          |    规则引擎    | <font color=blue>○</font>  | <font color=blue>○</font>  | <font color=green>✔</font> | <font color=green>✔</font> | 按规则执行动作或生成事件 |
|          |    SAAS平台    |  <font color=red>✘</font>  |  <font color=red>✘</font>  |  <font color=red>✘</font>  | <font color=green>✔</font> |        多租户平台        |
|          |    数据大屏    |  <font color=red>✘</font>  |  <font color=red>✘</font>  |  <font color=red>✘</font>  | <font color=green>✔</font> |     展示、分析、监控     |
|          |     移动端     |  <font color=red>✘</font>  |  <font color=red>✘</font>  |  <font color=red>✘</font>  | <font color=green>✔</font> |  手机查看数据及控制设备  |
| 运行环境 |    操作系统    | <font color=green>✔</font> | <font color=green>✔</font> | <font color=green>✔</font> | <font color=green>✔</font> |  Windows/Centos/Ubuntu   |
|          |     数据库     | <font color=green>✔</font> | <font color=green>✔</font> | <font color=green>✔</font> | <font color=green>✔</font> |  MySql/SQLite/SqlServer  |
|          |    终端设备    | <font color=green>✔</font> | <font color=green>✔</font> | <font color=green>✔</font> | <font color=green>✔</font> |    x86/Arm/LoongArch     |
| 服务支持 | 方案设计及培训 |  <font color=red>✘</font>  |  <font color=red>✘</font>  |  <font color=red>✘</font>  | <font color=green>✔</font> |        线上或现场        |
|          |  专家顾问咨询  |  <font color=red>✘</font>  |  <font color=red>✘</font>  |  <font color=red>✘</font>  | <font color=green>✔</font> |       每小时2000元       |
|          |    故障分析    |  <font color=red>✘</font>  |  <font color=red>✘</font>  |  <font color=red>✘</font>  | <font color=green>✔</font> |                          |
|          |    监控平台    |  <font color=red>✘</font>  | <font color=blue>○</font>  | <font color=blue>○</font>  | <font color=green>✔</font> |      星尘分布式平台      |
|          | 大数据方案设计 |  <font color=red>✘</font>  |  <font color=red>✘</font>  |  <font color=red>✘</font>  | <font color=green>✔</font> |     蚂蚁调度计算平台     |
|          |    单点登录    |  <font color=red>✘</font>  | <font color=blue>○</font>  | <font color=blue>○</font>  | <font color=green>✔</font> |   魔方OAuth2.0用户中心   |
| 授权定价 |      价格      |             ￥0             |            ￥600            |           ￥6000            |           ￥60000           |           含税           |
|          |    系统规模    |           100点            |          1000点           |           10万点           |          100万点           |
|          |    源码交付    | <font color=green>✔</font> | <font color=green>✔</font> | <font color=green>✔</font> | <font color=green>✔</font> |
|          |    源码更新    |          自由下载          |            单次            |            1年             |            3年             |
|          |    授权目标    |                            |                            |            项目            |            产品            |
|          |     授权点     |                            |                            |             1              |            无限            |

<font color=green>✔</font> 支持；<font color=red>✘</font> 不支持； <font color=blue>○</font> 部分支持；  