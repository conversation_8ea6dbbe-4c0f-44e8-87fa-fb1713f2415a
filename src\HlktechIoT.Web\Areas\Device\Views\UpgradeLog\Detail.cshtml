﻿@{
    var id = Model.lId;
}
<style asp-location="true">
    .layui-form-label{
        white-space:nowrap;
        /* border:2px solid ; */
    }
    .layui-card-his{
        padding:20px 14px;
        background-color:white;
    }
    .layui-form-item-his{
        padding:3px 0px;
    }


</style>
<script asp-location="Head">
    var layuiNumber = '@T("个")';
    var pleaseSelect = '@T("请选择")';
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPage = '@T("页")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
    var layuiCloseCurrent = '@T("关 闭 当 前")';
    var layuiCloseOther = '@T("关 闭 其 他")';
    var layuiCloseAll = '@T("关 闭 全 部")';
    var layuiMenuStyle = '@T("菜单风格")';
    var layuiTopStyle = '@T("顶部风格")';
    var layuiThemeColor = '@T("主题配色")';
    var layuiMoreSettings = '@T("更多设置")';
    var layuiOpen = '@T("开")';
    var layuiClose = '@T("关")';
    var layuiMenu = '@T("菜单")';
    var layuiView = '@T("视图")';
    var layuiBanner = '@T("通栏")';
    var layuiThroughColor = '@T("通色")';
    var layuiFooter = '@T("页脚")';
    var layuiSelectAll = '@T("全选")';
    var layuiClear = '@T("清空")';
    var layuiReverseSelection = '@T("反选")';
    var layuiPeeling = '@T("换肤")';
    var layuiNoDataYet = '@T("暂无数据")';
    var layuiSearch = '@T("搜索")';
    var layuiPrevious = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNotAllowClose = '@T("前页面不允许关闭")';
    var layuiOpenAtMost = '@T("最多打开")';
    var layuiTabs = '@T("个标签页")';
</script>
<div class="layui-card-his">
    <div class="layui-card-body-his">
        <table class="layui-hide" id="table2" lay-filter="tool2"></table>
    </div>
</div>

<script asp-location="Footer">
layui.use(['table', 'abp', 'dg', 'element', 'form', 'dgcommon'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;
        var element = layui.element;
        var laydate = layui.laydate;


        table.render({
            elem: '#table2'
            , url: '@Url.Action("GetUpgradeALogs")'
            , page: true //开启分页
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 60
            , cols: [[
                { field: 'IotId', title: '@T("设备标识")', minWidth: 200 },
                { field: 'Content', title: '@T("日志内容")', minWidth: 550 },
                { field: 'CreateTime', title: '@T("创建时间")', minWidth: 200 },
            ]]
            , limit: 10
            , limits: [10, 12, 20, 30, 50, 100]
            , height: 'full'
            , id: 'table2'
            , where :{
                lId : '@id',
            }
        });
        // 获取当前日期

        window.active = {
            reload: function () {
                table.reload('table2',
                    {
                        where :{
                            lId : '@id',
                        }
                    });
            }
        }
        form.on('submit(SearchHistoricalData)', function (data) {
            window.active.reload();
            return false;
        });
});
</script>