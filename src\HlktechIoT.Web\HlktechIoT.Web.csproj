﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
	<LangVersion>latest</LangVersion>
    <TargetFramework>net9.0</TargetFramework>
	<RootNamespace>HlktechIoT</RootNamespace>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>3ca1bda5-d069-49b9-b90b-c19f8857a48b</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
	<DockerfileContext>..\..</DockerfileContext>
	<AssemblyTitle>海凌科物联网云项目</AssemblyTitle>
	<Description>基于DH.Web.FrameWork制作的海凌科物联网云项目</Description>
	<Authors>丁川</Authors>
    <Company>深圳市海凌科电子有限公司</Company>
    <Copyright>版权所有(C) 深圳市海凌科电子有限公司 2009-2025</Copyright>
	<Version>1.0.2023.1117</Version>
	<FileVersion>1.0.2023.1117</FileVersion>
	<AssemblyVersion>1.0.*</AssemblyVersion>
	<Deterministic>false</Deterministic>
	<DebugType>pdbonly</DebugType>
	<Optimize>true</Optimize>
	<DefineConstants>TRACE</DefineConstants>
	<GenerateRuntimeConfigDevFile>true</GenerateRuntimeConfigDevFile>
	<SatelliteResourceLanguages>en</SatelliteResourceLanguages>
	<!--允许你指定要在生成和发布过程中为哪些语言保留附属资源程序集-->

	<!--将此参数设置为true以获取从NuGet缓存复制到项目输出的dll。-->
	<CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>

	<NoWarn>$(NoWarn);1591</NoWarn>
	<GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>
	
  <PropertyGroup Condition="'$(Configuration)'=='Debug'">
	<DefineConstants>$(DefineConstants);DEBUG</DefineConstants>
	<DebugType>full</DebugType>
	<DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <ItemGroup>
	<!-- 我们复制整个\App_Data目录。 但是我们忽略了JSON文件和数据保护密钥  -->
	<Content Include="App_Data\**" CopyToPublishDirectory="PreserveNewest" Exclude="App_Data\*.json" />
	<Content Remove="App_Data\*.json" />
	<Content Remove="Plugins\**" />
	<Content Update="App_Data\DataProtectionKeys\*.xml" CopyToPublishDirectory="Never" />

    <Compile Remove="Entity\Config\**" />
    <Compile Remove="Entity\Log\**" />
    <Content Remove="Entity\Config\**" />
    <Content Remove="Entity\Log\**" />
    <EmbeddedResource Remove="Entity\Config\**" />
    <EmbeddedResource Remove="Entity\Log\**" />
    <None Remove="Entity\Config\**" />
    <None Remove="Entity\Log\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AlipaySDKNet.Core" Version="5.0.1" />
    <PackageReference Include="DH.JPush" Version="4.0.2025.61700080" />
    <!--<PackageReference Include="DH.LettuceEncrypt" Version="3.6.2024.8160165" />-->
    <PackageReference Include="DH.Magicodes.IE.Excel" Version="4.0.2025.61700080" />
    <PackageReference Include="DH.NAgent" Version="4.12.2025.617-beta1020" />
    <PackageReference Include="DH.NMQTT" Version="4.12.2025.620-beta1203" />
    <PackageReference Include="DH.Npoi" Version="4.0.2025.61700080" />
	<PackageReference Include="DH.NRedis.Extensions" Version="4.12.2025.619-beta1116" />
	<PackageReference Include="DH.Permissions" Version="4.0.2025.606-beta1258" />
    <PackageReference Include="DH.SafeOrbit" Version="4.0.2025.61700080" />
    <PackageReference Include="DH.SignalR" Version="4.12.2025.515-beta0121" />
    <PackageReference Include="DH.SLazyCaptcha" Version="4.0.2025.412-beta0631" />
    <PackageReference Include="DH.Swagger" Version="3.91.2025.620-beta1043" />
    <PackageReference Include="Flurl.Http" Version="4.0.2" />
	<PackageReference Include="DH.AspNetCore.ServerSentEvents" Version="4.0.2025.61700080" />
	<PackageReference Include="Hlk.CPIot" Version="1.7.2025.6210229" />
	<PackageReference Include="HlktechIoT.Data" Version="1.7.2025.6210229" />
	<PackageReference Include="HlktechIoT.Services" Version="1.7.2025.6210229" />
	<PackageReference Include="HlktechIoT.UICube" Version="1.7.2025.6210229" />
	
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.0" />
	<PackageReference Include="HlktechIoT.Web.Framework" Version="1.7.2025.6210229" />
	<PackageReference Include="Pek.AspNetCore" Version="4.12.2025.619-beta1320" />
	<PackageReference Include="Pek.Common" Version="4.12.2025.617-beta0444" />
	<PackageReference Include="SKIT.FlurlHttpClient.Wechat.TenpayV3" Version="3.12.0" />
  </ItemGroup>
	
  <ItemGroup>
	<Content Update="Settings\*.*">
		<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</Content>
	<Content Update="web.config">
	  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</Content>
  </ItemGroup>
	
  <ItemGroup>
	<PackageReference Update="Microsoft.SourceLink.GitHub" Version="8.0.0" />
  </ItemGroup>
	
  <!--自动复制所有引用包的XML文档-->
	<Target Name="CopyPackageXmlDocs" AfterTargets="Build">
	  <ItemGroup>
		<!-- 获取所有引用的程序集 -->
		<ReferencedAssemblies Include="@(ReferenceCopyLocalPaths)" Condition="'%(ReferenceCopyLocalPaths.Extension)' == '.dll'" />
		<!-- 构建对应的XML文档路径 -->
		<XmlDocFiles Include="@(ReferencedAssemblies->'%(RootDir)%(Directory)%(Filename).xml')" Condition="Exists('%(RootDir)%(Directory)%(Filename).xml')" />
	  </ItemGroup>
	  <Copy SourceFiles="@(XmlDocFiles)" DestinationFolder="$(OutDir)" SkipUnchangedFiles="true" />
	  <Message Text="复制XML文档: @(XmlDocFiles)" Importance="high" Condition="'@(XmlDocFiles)' != ''" />
	</Target>

	<!--发布时也复制XML文档-->
	<Target Name="CopyPackageXmlDocsOnPublish" AfterTargets="Publish">
	  <ItemGroup>
		<ReferencedAssemblies Include="@(ReferenceCopyLocalPaths)" Condition="'%(ReferenceCopyLocalPaths.Extension)' == '.dll'" />
		<XmlDocFiles Include="@(ReferencedAssemblies->'%(RootDir)%(Directory)%(Filename).xml')" Condition="Exists('%(RootDir)%(Directory)%(Filename).xml')" />
	  </ItemGroup>
	  <Copy SourceFiles="@(XmlDocFiles)" DestinationFolder="$(PublishDir)" SkipUnchangedFiles="true" />
	</Target>
	
  <!--DATAS 是一项很棒的新功能，它将 Workstation GC 和 Server GC 的优势结合在一起：您开始时内存更少，当请求激增时，GC 可以动态扩展其托管堆的数量以提高吞吐量。当请求数在以后的某个时间点减少时，也可以减少托管堆的数量以释放内存 https://mp.weixin.qq.com/s/WJEkHmV3bPYStqZoDJhvmg-->
  <PropertyGroup>
     <ServerGarbageCollection>true</ServerGarbageCollection>
     <GarbageCollectionAdapatationMode>1</GarbageCollectionAdapatationMode>
  </PropertyGroup>

</Project>
