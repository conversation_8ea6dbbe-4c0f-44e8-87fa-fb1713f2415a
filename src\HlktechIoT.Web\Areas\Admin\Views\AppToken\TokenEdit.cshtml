﻿@model HlktechIoT.Data.Apis.AppToken

@{
    Html.AppendTitleParts("编辑令牌");
}
<form class="layui-form" lay-filter="tokenForm">
    <input type="hidden" name="Id" value="@Model.Id">
    <div class="layui-form-item">
        <label class="layui-form-label">@T("名称")</label>
        <div class="layui-input-block">
            <input type="text" name="Name" required lay-verify="required" placeholder="@T("请输入名称")" autocomplete="off" class="layui-input" value="@Model.Name">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">@T("令牌")</label>
        <div class="layui-input-block">
            <input type="text" name="Token" required lay-verify="required" placeholder="@T("请输入令牌")" autocomplete="off" class="layui-input" value="@Model.Token">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">@T("有效期")</label>
        <div class="layui-input-block">
            <input type="text" name="Expire" required lay-verify="required" placeholder="@T("请选择有效期")" autocomplete="off" class="layui-input" id="Expire" value="@Model.Expire.ToString("yyyy-MM-dd HH:mm:ss")">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">@T("启用")</label>
        <div class="layui-input-block">
            <input type="checkbox" name="Enable" lay-skin="switch" lay-text="@T("是|否")" @(Model.Enable ? "checked" : "")>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="pear-btn pear-btn-primary" lay-submit lay-filter="submit">@T("提交")</button>
            <button type="reset" class="pear-btn pear-btn-secondary">@T("重置")</button>
        </div>
    </div>
</form>

<script asp-location="Footer">
    layui.use(['form', 'laydate', 'abp'], function () {
        var form = layui.form;
        var laydate = layui.laydate;
        var $ = layui.jquery;
        var abp = layui.abp;

        // console.log(window.parent.length); //第一步：先拿到所有父元素
        var parentList = window.parent
        var parentPage = null;

        // 第二步：拿到对应层
        for (let i = 0; i < parentList.length; i++) {
            if (parentList[i].name === 'TokenDetail') { //这里的name自己去对应层DIY
                parentPage = parentList[i]
                break;
            }
        }
        // 第三步：操作对应层
        var parent_window = parentPage.window  //获取父层的window层
        var parent_layer = parentPage.layer //获取父层的layer
        var parent_notify = parentPage.layui.abp.notify //获取父层的layui.notify --消息通知
        var parent_layui  = parentPage.layui  //获取父层的layui
        var currentPageCloseIndex = parent_window.editPageIndex //当前层的关闭index下标

        laydate.render({
            elem: '#Expire',
            type: 'datetime'
        });

        // 初始化表单
        form.val("tokenForm", {
            "Name": "@Model.Name",
            "Token": "@Model.Token",
            "Expire": "@Model.Expire.ToString("yyyy-MM-dd HH:mm:ss")",
            "Enable": @Model.Enable.ToString().ToLower()
        });

        form.on('submit(submit)', function (data) {
            $.post('@Url.Action("TokenEdit")', data.field, function (res) {
                if (res.success) {
                    // parent.layer.msg(res.msg);
                    // parent.layer.close(window.name);
                    parent_notify.success(res.msg);
                    parentPage.active.reload();
                    // 关闭当前编辑页面
                    parent.layer.close(currentPageCloseIndex);
                } else {
                    parent.layer.msg(res.msg);
                }
            });
            return false;
        });
    });
</script>
