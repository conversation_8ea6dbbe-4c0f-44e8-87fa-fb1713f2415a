﻿@{
    Layout = null;
}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>续费支付</title>
    
    <link rel="stylesheet" href="~/libs/pear/css/pear.css" />
    <script src="~/js/jquery.min.js"></script>
    <script src="~/libs/layui/layui.js"></script>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            overflow-x: hidden;
            width: 100%;
            height: 100%;
            box-sizing: border-box;
        }

        * {
            box-sizing: border-box;
        }
        .renew-pay {
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
            max-width: 100%; 
        }
        .nav {
            width: 100%;
            height: 50px;
            line-height: 50px;
            background-color: #357fff;
            font-size: 16px;
            color: white;
            display: flex;
            position: relative;
            overflow: hidden;
        }
        .nav-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: 1;
        }
        .nav-title {
            flex: 1;
            text-align: center;
            z-index: 2;
        }
        .nav-link {
            position: absolute;
            right: 15px;
            color: white;
            text-decoration: none;
            font-size: 12px;
            z-index: 2;
        }
        .content {
            flex: 1;
            background-color: #f3f2f2;
            overflow-y: auto;
        }
        .renew-pay-label {
            padding: 24px 20px 10px 20px;
            font-size: 13px;
            color: #565656;
            font-weight: 500;
        }
        .equipment {
            display: flex;
            justify-content: space-between;
        }

        .equipment-add {
            display: flex;
            color: #357BFF;
            cursor: pointer;
        }
        .icon {
            width: 18px;
            height: 18px;
            margin-right: 5px;
        }
        .renew-pay-input-container {
            position: relative;
        }
        .renew-pay-input {
            border: none;
            height: 50px;
            width: 100%;
            padding: 0 20px;
            font-size: 16px;
            background: #fff;
            outline: none;
            box-shadow: none;
        }
        .renew-pay-input:focus {
            border: none;
            outline: none;
            box-shadow: none;
        }
        .searchResults {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            max-height: 200px;
            overflow-y: auto;
            background: white;
            border: 1px solid #ddd;
            z-index: 10;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .remove-input {
            position: absolute;
            right: 10px;
            top: 15px;
            cursor: pointer;
            color: #999;
            font-size: 16px;
        }
        .year-options {
            display: grid;
            grid-template-columns: repeat(auto-fill, 105px);
            grid-gap: 10px;
            padding: 10px 20px;
            background: #fff;
        }

        .year-option {
            width: 105px;
            height: 46px;
            border-radius: 6px;
            border: 1px solid #D8D8D8;
            line-height: 46px;
            text-align: center;
            color: #8b8b8b;
            cursor: pointer;
        }

        .active {
            border-color: #357BFF;
            color: #357BFF;
            position: relative;
        }

        .year-option.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 20px 20px 0 0;
            border-color: #357BFF transparent transparent transparent;
            z-index: 1;
        }

        .year-option.active::after {
            content: '✓';
            position: absolute;
            top: 1px;
            left: 1px;
            font-size: 12px;
            color: #fff;
            z-index: 2;
            line-height: 1;
            transform: rotate(10deg);
        }
        .pay-mode {
            background: #fff;
            padding: 0 20px;
        }

        .pay-option {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 60px;
        }

        .pay-option:last-child {
            border-bottom: none;
        }

        .pay-option .check {
            margin-right: 15px;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            border: 1px solid #8B8B8B;
        }

        .pay-option .activeCheck {
            border: none;
            background-image:  url('@Url.Content("~/images/renewPay/xuanzhong.png")');;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .pay-option label {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .pay-info {
            display: flex;
            align-items: center;
            margin-left: auto;
        }

        .pay-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
        }

        .pay-info span {
            font-weight: 500;
            color: #2B2B2B;
        }

        .pay-btn {
            width: 270px;
            height: 40px;
            background: #8B8B8B;
            border-radius: 20px;
            display: block;
            margin: 50px auto;
            border: none;
            font-size: 14px;
            color: #fff;
            cursor: pointer;
        }

        .allowClick {
            background: linear-gradient(0deg, #357BFF, #37B5FF);
            color: white;
            border: none;
        }
    </style>
    <script>
        var pleaseSelect = '@T("请选择")';
        var layuiPrint = '@T("打印")';
        var layuiExport = '@T("导出")';
        var layuiFilterColumn = '@T("筛选列")';
        var layuiArticlePage = '@T("条/页")';
        var layuiTotal = '@T("共")';
        var layuiBtn = '@T("确定")';
        var layuiGoPage = '@T("到第")';
        var layuiPage = '@T("页")';
        var layuiPrev = '@T("上一页")';
        var layuiNext = '@T("下一页")';
        var layuiNoData = '@T("无数据")';
        var layuiAsc = '@T("升序")';
        var layuiDesc = '@T("降序")';
        var layuiCloseCurrent = '@T("关 闭 当 前")';
        var layuiCloseOther = '@T("关 闭 其 他")';
        var layuiCloseAll = '@T("关 闭 全 部")';
        var layuiMenuStyle = '@T("菜单风格")';
        var layuiTopStyle = '@T("顶部风格")';
        var layuiThemeColor = '@T("主题配色")';
        var layuiMoreSettings = '@T("更多设置")';
        var layuiOpen = '@T("开")';
        var layuiClose = '@T("关")';
        var layuiMenu = '@T("菜单")';
        var layuiView = '@T("视图")';
        var layuiBanner = '@T("通栏")';
        var layuiThroughColor = '@T("通色")';
        var layuiFooter = '@T("页脚")';
        var layuiSelectAll = '@T("全选")';
        var layuiClear = '@T("清空")';
        var layuiReverseSelection = '@T("反选")';
        var layuiPeeling = '@T("换肤")';
        var layuiNoDataYet = '@T("暂无数据")';
        var layuiSearch = '@T("搜索")';
        var layuiPrevious = '@T("上一页")';
        var layuiNext = '@T("下一页")';
        var layuiNotAllowClose = '@T("前页面不允许关闭")';
        var layuiOpenAtMost = '@T("最多打开")';
        var layuiTabs = '@T("个标签页")';
    </script>
</head>
<body>
    <div class="renew-pay">
        <div class="nav">
            <img class="nav-background" src="@Url.Content("~/images/renewPay/dingbuditu.png")" alt="" />
            <div class="nav-title">@T("续费支付")</div>
            <a class="nav-link" href="@Url.Action("PayRecord")">@T("支付记录")></a>
        </div>
        <div class="content">
            <div class="renew-pay-label equipment">
                <div>@T("设备号")</div>
                <div class="equipment-add"  id="addDeviceBtn">
                    <img class="icon" src="~/images/renewPay/xinzeng.png" alt="">
                    <div>@T("新增")</div>
                </div>
            </div>
            <div id="deviceInputsContainer">
                <div class="renew-pay-input-container">
                    <input id="deviceInput" name="password" placeholder="@T("填写设备号")" autocomplete="off" class="renew-pay-input">
                    <div class="searchResults"></div>
                </div>
            </div>
            <div class="renew-pay-label">
                <div>@T("支付年限")</div>
            </div>
            <div class="year-options"></div>
            <div class="renew-pay-label">
                <div>@T("支付方式")</div>
            </div>
            <div class="pay-mode">
                <div class="pay-option">
                    <label for="alipay">
                        <div class="pay-info">
                            <img src="~/images/renewPay/zhifubao.png" alt="" class="pay-icon">
                            <span>@T("支付宝支付")</span>
                        </div>
                    </label>
                    <div class="check"></div>
                </div>
                <div class="pay-option">
                    <label for="wechat">
                        <div class="pay-info">
                            <img src="~/images/renewPay/weixin.png" alt="" class="pay-icon">
                            <span>@T("微信支付")</span>
                        </div>
                    </label>
                    <div class="check"></div>
                </div>
            </div>
            <button class="pay-btn">@T("立即支付")</button>
        </div>
    </div>
</body>
<script>

    layui.use(['layer'], function(){
        const layer = layui.layer;
        sessionStorage.setItem('search', window.location.search)
        onLoad();
        function onLoad() {
            $.ajax({
                url: '@Url.Action("QueryYearAmount")',
                type: 'GET',
                success: function(res) {
                    if (res.data && res.data.length > 0) {
                        $('.year-options').empty();
                        res.data.forEach(function(item, index) {
                            const yearOption = $('<div class="year-option" data-year="' + item.year + '">' + 
                                            item.year + ' @T("年")' + '</div>');
                            $('.year-options').append(yearOption);
                            // 默认选中第一个选项
                            if (index === 0) {
                                yearOption.addClass('active');
                            }
                        });
                        // 重新绑定点击事件
                        bindYearOptionEvents();
                    } else {
                        console.error('获取支付年限数据失败');
                    }
                },
                error: function() {
                    resultsContainer.html('<div class="search-item" style="padding: 10px; text-align: center;">搜索出错，请重试</div>').show();
                }
            });
        }
        // 默认选中第一个选项
        $('.year-option:first').addClass('active');
        
        // 绑定年限选项点击事件
        function bindYearOptionEvents() {
            $('.year-option').click(function() {
                $('.year-option').removeClass('active');
                $(this).addClass('active');
                // 获取选中的年限值和金额
                const selectedYear = $(this).data('year');
            });
        }


        // 支付方式点击事件处理
        $('.pay-option').click(function() {
            $('.pay-option .check').removeClass('activeCheck');
            $(this).find('.check').addClass('activeCheck');
             $('.pay-btn').addClass('allowClick')
            // 获取选中的支付方式
            const selectedPayment = $(this).find('label').attr('for');
        });
    
        // 新增设备输入框
        $('#addDeviceBtn').click(function() {
            const inputCount = $('#deviceInputsContainer .renew-pay-input-container').length;
            const newInputHtml = `
                <div class="renew-pay-input-container" style="position: relative;">
                    <input id="deviceInput${inputCount}" name="deviceInputs[]" placeholder="@T("填写设备号")" autocomplete="off" class="renew-pay-input">
                    <div class="searchResults"></div>
                    <span class="remove-input">×</span>
                </div>
            `;
            $('.renew-pay-input-container').css({marginBottom: '10px'})
            $('#deviceInputsContainer').append(newInputHtml);
            
            // 为新添加的输入框绑定搜索事件
            bindSearchEvents('input', $('#deviceInput' + inputCount));
            bindSearchEvents('focus', $('#deviceInput' + inputCount));
            
            // 绑定删除按钮事件
            $('.remove-input').click(function(e) {
                e.stopPropagation();
                $(this).parent().remove();
            });
        });
        
        // 设备号搜索功能
        function bindSearchEvents(eventType, inputElement) {
            let searchTimeout;
            inputElement.on(eventType, function() {
                const query = $(this).val().trim();
                const resultsContainer = $(this).siblings('.searchResults');
                clearTimeout(searchTimeout);
                
                searchTimeout = setTimeout(function() {
                    const selectedDevices = [];
                    $('#deviceInputsContainer .renew-pay-input').each(function() {
                        const val = $(this).val().trim();
                        if (val) {
                            selectedDevices.push(val);
                        }
                    });
                    // 调用后端API搜索设备号
                    $.ajax({
                        url: '@Url.Action("QueryUserDevice")',
                        type: 'GET',
                        data: { deviceName: query },
                        success: function(response) {
                            const filteredData = response.data.filter(item => 
                                !selectedDevices.includes(item.value)
                            );
                            if (filteredData.length > 0) {
                                displaySearchResults(resultsContainer, filteredData, inputElement);
                            } else {
                                resultsContainer.html('<div class="search-item" style="padding: 10px; text-align: center;">没有找到匹配的设备</div>').show();
                            }
                        },
                        error: function() {
                            resultsContainer.html('<div class="search-item" style="padding: 10px; text-align: center;">搜索出错，请重试</div>').show();
                        }
                    });
                }, 300); // 300ms延迟，避免频繁请求
            });
        }
        // 显示搜索结果
        function displaySearchResults(resultsContainer, results, inputElement) {
            let html = '';
            results.forEach(function(item) {
                html += '<div class="search-item" style="padding: 10px; border-bottom: 1px solid #eee; cursor: pointer;" data-value="' + item.value + '">' + item.name + '</div>';
            });
            
            resultsContainer.html(html).show();
            
            // 点击选择设备
            resultsContainer.find('.search-item').click(function() {
                const selectedValue = $(this).data('value');
                inputElement.val(selectedValue);
                resultsContainer.hide();
            });
        }
        
        // 为初始输入框绑定搜索事件
        bindSearchEvents('input', $('#deviceInput'));
        bindSearchEvents('focus', $('#deviceInput'))
        // 点击外部区域隐藏搜索结果
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.renew-pay-input-container').length) {
                $('.searchResults').hide();
            }
        });

        // 支付按钮点击事件处理
        $('.pay-btn').click(function() {
            // 获取设备号
            const deviceNumbers = [];
            $('#deviceInputsContainer .renew-pay-input').each(function() {
                const deviceNo = $(this).val().trim();
                if (deviceNo) {
                    deviceNumbers.push(deviceNo);
                }
            });
            
            if (deviceNumbers.length === 0) {
                layer.msg('请输入设备号')
                return;
            }
            
            // 获取选中的年限
            const selectedYear = $('.year-option.active').data('year');
            
            if (!selectedYear) {
                layer.msg('请选择支付年限')
                return;
            }
            
            // 获取支付方式 1：支付宝 2：微信
            let payWay = 0;
            if ($('.pay-option:eq(0) .check').hasClass('activeCheck')) {
                payWay = 1;
            } else if ($('.pay-option:eq(1) .check').hasClass('activeCheck')) {
                payWay = 2;
            }
            
            if (payWay === 0) {
                layer.msg('请选择支付方式')
                return;
            }
            
            // 支付参数
            const payParams = {
                deviceNames: deviceNumbers.join(','),
                // 1: 支付宝 2：微信
                payWay: payWay,
                year: selectedYear,
                // 不存在id 默认为0
                id: '0'
            };
            
            // 5. 调用支付接口
            $.ajax({
                url: '@Url.Action("CreatePayUrl", "DeviceLicence")',
                type: 'POST',
                data: payParams,
                success: (res) => {
                    if (res.success) {
                        // 存储数据
                        localStorage.setItem('paymentData', JSON.stringify({
                            searchQuery: sessionStorage.getItem('search'),
                        }));
                        if (payWay === 1) { // 支付宝
                            window.location.href = res.data;
                        } else if (payWay === 2) { // 微信
                            window.location.href = res.data;
                     
                        }
                    } else {
                        layer.msg(res.msg)
                    }
                },
                error: function(err) {
                    console.error('网络错误，请稍后重试')
                }
            });
        });
    
    
    });    
</script>
</html>