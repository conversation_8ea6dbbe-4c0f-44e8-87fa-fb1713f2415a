﻿using DG;
using DG.Web.Framework;

using DH.Core.Infrastructure;
using DH.Entity;

using HlktechIoT.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Caching;
using NewLife.Data;
using NewLife.Serialization;

using Pek;
using Pek.Configs;
using Pek.Models;

using System.ComponentModel;

using XCode.Membership;

using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Device.Controllers;

/// <summary>合作公司</summary>
[DisplayName("合作公司")]
[Description("合作公司相关数据管理")]
[DeviceArea]
[DHMenu(90, ParentMenuName = "MassManager", CurrentMenuUrl = "~/{area}/ThirdParty", CurrentMenuName = "ThirdParty", LastUpdate = "20241009")]
public class ThirdPartyController : BaseAdminControllerX {

    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 90;

    /// <summary>
    /// 合作公司列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("合作公司列表")]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// 根据条件搜索
    /// </summary>
    /// <param name="accessId">商户号</param>
    /// <param name="uId">所属用户</param>
    /// <param name="name">公司名称</param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("合作公司查询")]
    public IActionResult GetOpenPlatform(String name, String accessId, DateTime start, DateTime end, String key, Int32 uId=-1, Int32 page = 1, Int32 limit = 10)
    {
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
        };
        IEnumerable<OpenPlatform> list = OpenPlatform.Search(name,accessId, uId, start, end, key, pages);
        var data = list.Select(x => new { x.Id, x.Name, x.AccessId, x.AccessKey, x.OfficialUrl, x.CreateTime, x.Enabled,DisplayName=GetDisplayName(x.UId) });
        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data = data });
    }
    /// <summary>
    /// 搜索所属销售ID查询
    /// </summary>
    /// <param name="id">商户号</param>
    /// <returns></returns>
    public string GetDisplayName(Int32 id) { 
        var model=UserE.FindByID(id);
        var name = "";
        if (model!=null)
        {
            name = model.DisplayName;
        }
        return name;
    }

    /// <summary>
    /// 搜索项目
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [HttpPost]
    [DisplayName("搜索销售用户")]
    public IActionResult SearchInUid(Int32 page, Int32 limit = 10)
    {
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
        };
        var res = new DResult();
        var roleId= Role.GetOrAdd("销售").ID;
        res.data = UserE.PageByRoleId(roleId, pages).Select(e =>
        {
            return new Xmselect<Int32>
            {
                name = e.DisplayName,
                value = e.ID,
            };
        });
        res.success = true;
        return Json(res);
    }

    /// <summary>
    /// 添加合作公司
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("添加合作公司")]
    public IActionResult Add()
    {
        return View();
    }

    /// <summary>
    /// 添加合作公司信息
    /// </summary>
    /// <param name="Name">公司名称</param>
    /// <param name="AccessId">商品号</param>
    /// <param name="AccessKey">商户密钥</param>
    /// <param name="OfficialUrl">第三方服务器地址</param>
    /// <param name="UName">联系人</param>
    /// <param name="Phone">联系电话</param>
    /// <param name="UId">所属销售</param>
    /// <param name="Enabled">是否启用</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    [DisplayName("添加合作公司信息")]
    public IActionResult AddThirdParty(string Name,string AccessId, string AccessKey, string OfficialUrl,string UName,string Phone,Int32 UId,string Enabled)
    {
        var result = new DResult();
        if (Name.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("公司名称不能为空");
            return Json(result);
        }
        if (AccessId.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("商户号不能为空");
            return Json(result);
        }
        if (AccessKey.Length < 20)
        {
            result.msg = GetResource("商户密钥太短");
            return Json(result);
        }
        if (!OfficialUrl.IsNullOrWhiteSpace())
        {
            if (!OfficialUrl.StartsWith("http://") && !OfficialUrl.StartsWith("https://"))
            {
                result.msg = "服务器地址不正确";
                return Json(result);
            }
        }
        var modelName=OpenPlatform.FindByName(Name);
        if (modelName != null) {
            result.msg = GetResource("该公司名称已创建");
            return Json(result);
        }
        var modelAccessId = OpenPlatform.FindByAccessId(AccessId);
        if (modelAccessId != null)
        {
            result.msg = GetResource("该商户号已创建");
            return Json(result);
        }

        OpenPlatform model = new OpenPlatform();
        model.Name = Name;
        model.AccessKey = AccessKey;
        model.AccessId = AccessId;
        model.OfficialUrl = OfficialUrl;
        model.Enabled = Enabled.SafeString() == "on";
        model.UName = UName;
        model.Phone = Phone;
        model.UId = UId;
        model.Insert();

        var keyOpenPlatform = $"{RedisSetting.Current.CacheKeyPrefix}:openplatform:{model.CreateUserID}";

        var rds = EngineContext.Current.Resolve<FullRedis>();
        rds.Remove(keyOpenPlatform);

        result.msg = GetResource("添加成功");
        result.success = true;
        return Json(result);
    }

    /// <summary>
    /// 编辑合作公司信息
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("编辑合作公司信息")]
    public IActionResult Edit(int Id)
    {
        var model = OpenPlatform.FindById(Id);
        if (model == null)
        {
            return Content("数据有误");
        }

        var roleId = Role.GetOrAdd("销售").ID;
        var ModelList = UserE.FindAllByRoleID(roleId).Select(x => new { name = x.DisplayName, value = x.ID, selected = x.ID == model.UId });

        ViewBag.PList = ModelList.ToJson();
        return View(model);
    }

    /// <summary>
    /// 修改启用状态
    /// </summary>
    /// <param name="id"></param>
    /// <param name="status"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("修改启用状态")]
    public IActionResult modifystate(int id, bool status)
    {
        var res = new DResult();
        var model = OpenPlatform.FindById(id);

        if (model == null)
        {
            res.msg = "对接数据不存在";
            return Json(res);
        }

        bool UId = false;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.Role.IsSystem || !modelRole.IsAdmin)
        {
            UId = true;
        }
        if (UId)
        {
            if (model.UpdateUser == "管理员" && model.Enabled == false)
            {
                res.msg = "管理员已禁用";
                return Json(res);
            }
        }

        model.Enabled = status;
        model.Update();

        var keyOpenPlatform = $"{RedisSetting.Current.CacheKeyPrefix}:openplatform:{model.CreateUserID}";

        var rds = EngineContext.Current.Resolve<FullRedis>();
        rds.Remove(keyOpenPlatform);

        UserE.WriteLog(LocaleStringResource.GetResource("修改启用状态"), true, String.Format(LocaleStringResource.GetResource("用户[{0}]修改启用[{1}]的状态为[{2}]"), ManageProvider.User?.Name, model.AccessId, status));

        res.success = true;
        res.msg = "修改成功";
        return Json(res);
    }

    /// <summary>
    /// 修改合作公司信息
    /// </summary>
    /// <param name="id">id</param>
    /// <param name="Name">公司名称</param>
    /// <param name="AccessId">商品号</param>
    /// <param name="AccessKey">商户密钥</param>
    /// <param name="OfficialUrl">第三方服务器地址</param>
    /// <param name="Enabled">是否启用</param>
    /// <param name="UName">联系人</param>
    /// <param name="Phone">联系电话</param>
    /// <param name="UId">所属销售</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("编辑合作公司数据")]
    public IActionResult EditThirdParty(int id,string Name, string AccessId, string AccessKey, string OfficialUrl, String Enabled,string UName,string Phone,Int32 UId)
    {
        var result = new DResult();
        if (Name.IsNullOrWhiteSpace()) {
            result.msg = GetResource("公司名称不能为空");
            return Json(result);
        }
        if (AccessId.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("商户号不能为空");
            return Json(result);
        }
        if (AccessKey.Length < 20)
        {
            result.msg = GetResource("商户密钥太短");
            return Json(result);
        }
        if (!OfficialUrl.IsNullOrWhiteSpace())
        {
            if (!OfficialUrl.StartsWith("http://") && !OfficialUrl.StartsWith("https://"))
            {
                result.msg = "服务器地址不正确！";
                return Json(result);
            }
        }
        var model = OpenPlatform.FindById(id);
        if (model == null)
        {
            result.msg = GetResource("参数出错");
            return Json(result);
        }

        var modelName = OpenPlatform.FindByName(Name);
        if (modelName != null&& modelName.Id!=id)
        {
            result.msg = GetResource("该公司名称已创建");
            return Json(result);
        }

        bool UIds = false;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.Role.IsSystem || !modelRole.IsAdmin)
        {
            UIds = true;
        }
        if (UIds)
        {
            if (model.UpdateUser == "管理员" && model.Enabled == false)
            {
                result.msg = GetResource("管理员已禁用");
                return Json(result);
            }
        }
        model.Name = Name;
        model.AccessKey = AccessKey;
        model.AccessId = AccessId;
        model.OfficialUrl = OfficialUrl;
        model.Enabled = Enabled == "on";
        model.UName = UName;
        model.Phone = Phone;
        model.UId = UId;
        model.Update();

        var keyOpenPlatform = $"{RedisSetting.Current.CacheKeyPrefix}:openplatform:{model.CreateUserID}";

        var rds = EngineContext.Current.Resolve<FullRedis>();
        rds.Remove(keyOpenPlatform);

        result.msg = GetResource("修改成功");
        result.success = true;
        return Json(result);
    }

    /// <summary>
    /// 删除合作公司信息
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [HttpPost]
    [DisplayName("删除合作公司信息")]
    public IActionResult Delete(int Id)
    {
        var res = new DResult();

        var model = OpenPlatform.FindById(Id);
        if (model != null)
        {
            var keyOpenPlatform = $"{RedisSetting.Current.CacheKeyPrefix}:openplatform:{model.CreateUserID}";

            var rds = EngineContext.Current.Resolve<FullRedis>();
            rds.Remove(keyOpenPlatform);

            model.Delete();
        }

        res.success = true;

        return Json(res);
    }
}
