﻿@{
    Html.AppendTitleParts(T("添加应用令牌").Text);
}
<div class="container">
    <form class="layui-form" action="">
        <div class="layui-form-item">
            <label class="layui-form-label"><span class="red">*</span>@T("名称")</label>
            <div class="layui-input-inline">
                <input type="text" name="name" id="name" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"><span class="red">*</span>@T("显示名")</label>
            <div class="layui-input-inline">
                <input type="text" name="displayName" id="displayName" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">@T("有效期")</label>
            <div class="layui-input-inline">
                <input type="number" name="tokenExpire" id="tokenExpire" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">@T("是否启用")</label>
            <div class="layui-input-inline">
                <select name="enable" id="enable">
                    <option value="true">@T("是")</option>
                    <option value="false">@T("否")</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">@T("白名单")</label>
            <div class="layui-input-inline">
                <input type="text" name="white" id="white" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">@T("黑名单")</label>
            <div class="layui-input-inline">
                <input type="text" name="black" id="black" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">@T("回调地址")</label>
            <div class="layui-input-inline">
                <input type="text" name="urls" id="urls" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">@T("备注")</label>
            <div class="layui-input-block">
                <textarea name="remark" id="remark" class="layui-textarea"></textarea>
            </div>
        </div>

        <div class="layui-form-item">
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" value="@T("保存")" />
        </div>
    </form>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'dg', 'form', 'dgcommon'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var dg = layui.dg;
        var os = layui.dgcommon;

        form.on('submit(Submit)', function (data) {
            if (data.field.name.length == 0) {
                abp.notify.warn("@T("名称不能为空")");
                return;
            } else if (data.field.displayName.length == 0) {
                abp.notify.warn("@T("显示名不能为空")");
                return;
            } else if (data.field.tokenExpire < 0) {
                abp.notify.warn("@T("有效期不能小于0")");
                return;
            }

            var waitIndex = parent.layer.load(2);

            abp.ajax({
                url: "@Url.Action("Add")",
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                dg.reload('tables');
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });
    });
</script>
