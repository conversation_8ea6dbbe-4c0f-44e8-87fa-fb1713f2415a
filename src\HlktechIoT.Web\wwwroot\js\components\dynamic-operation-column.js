/**
 * 动态操作列组件 - 通用解决方案
 * 用于LayUI表格中动态计算和渲染操作列宽度
 * <AUTHOR> Name
 * @version 1.0.0
 */
(function(window) {
    'use strict';
    
    /**
     * 动态操作列管理器
     */
    function DynamicOperationColumn() {
        this.operationButtons = [];
        this.operationColumnWidth = 120; // 默认宽度
        this.tableId = '';
        this.debugMode = false;
    }
    
    /**
     * 初始化动态操作列
     * @param {Object} config 配置参数
     * @param {Array} config.buttons 按钮配置数组
     * @param {string} config.tableId 表格ID
     * @param {boolean} config.debug 是否开启调试模式
     */
    DynamicOperationColumn.prototype.init = function(config) {
        this.operationButtons = config.buttons || [];
        this.tableId = config.tableId || '';
        this.debugMode = config.debug || false;
        
        // 计算初始宽度
        this.operationColumnWidth = this.calculateOperationColumnWidth();
        
        if (this.debugMode) {
            console.log('动态操作列初始化完成:', {
                tableId: this.tableId,
                buttonCount: this.operationButtons.length,
                calculatedWidth: this.operationColumnWidth
            });
        }
        
        // 将按钮配置保存到全局变量，供模板使用
        window.operationButtons = this.operationButtons;
        
        return this.operationColumnWidth;
    };
    
    /**
     * 动态计算操作列宽度
     * @returns {number} 计算出的列宽度
     */
    DynamicOperationColumn.prototype.calculateOperationColumnWidth = function() {
        try {
            var buttonMargin = 4; // 按钮间距
            var extraPadding = 8; // 额外边距
            var baseButtonPadding = 20; // 按钮内边距
            
            if (this.operationButtons.length === 0) {
                return 120; // 最小宽度
            }
            
            var totalWidth = 0;
            
            // 计算所有有权限显示的按钮宽度
            this.operationButtons.forEach(function(button) {
                // 优化文字宽度估算：中文字符约17px，英文字符约10px
                var text = button.text;
                var textWidth = 0;
                for (var i = 0; i < text.length; i++) {
                    var char = text.charAt(i);
                    if (/[\u4e00-\u9fa5]/.test(char)) {
                        textWidth += 17; // 中文字符宽度
                    } else {
                        textWidth += 10;  // 英文字符宽度
                    }
                }
                
                // 确保按钮最小宽度
                var buttonWidth = Math.max(textWidth + baseButtonPadding, 50);
                totalWidth += buttonWidth;
            });
            
            // 加上按钮间距和额外边距
            totalWidth += (this.operationButtons.length - 1) * buttonMargin + extraPadding;
            
            // 设置最小宽度
            if (totalWidth < 120) {
                totalWidth = 120;
            }
            
            if (this.debugMode) {
                console.log('计算的操作列宽度:', totalWidth, '按钮数量:', this.operationButtons.length);
            }
            
            return totalWidth;
        } catch (error) {
            console.error('计算操作列宽度时出错:', error);
            return 280; // 返回默认宽度作为备用
        }
    };
    
    /**
     * 基于DOM测量计算精确宽度
     * @returns {number|null} 测量出的宽度，失败时返回null
     */
    DynamicOperationColumn.prototype.calculateOperationColumnWidthFromDOM = function() {
        try {
            if (!this.tableId) {
                if (this.debugMode) {
                    console.warn('未设置tableId，无法进行DOM测量');
                }
                return null;
            }
            
            var tableView = $('#' + this.tableId).next('.layui-table-view');
            if (tableView.length === 0) {
                if (this.debugMode) {
                    console.warn('未找到表格视图容器，无法进行DOM测量');
                }
                return null;
            }
            
            // 查找操作列中的按钮
            var operationCells = tableView.find('.operation-column');
            if (operationCells.length === 0) {
                if (this.debugMode) {
                    console.warn('未找到操作列，无法进行DOM测量');
                }
                return null;
            }
            
            var maxWidth = 0;
            operationCells.each(function() {
                var buttons = $(this).find('.pear-btn');
                var cellWidth = 0;
                
                buttons.each(function() {
                    cellWidth += $(this).outerWidth(true); // 包含margin
                });
                
                cellWidth += 16; // 额外边距
                maxWidth = Math.max(maxWidth, cellWidth);
            });
            
            if (maxWidth > 0) {
                if (this.debugMode) {
                    console.log('DOM测量得到的宽度:', maxWidth);
                }
                return maxWidth;
            }
            
            return null;
        } catch (error) {
            console.error('DOM测量时出错:', error);
            return null;
        }
    };
    
    /**
     * 应用操作列宽度
     * @param {boolean} useMeasurement 是否使用DOM测量
     */
    DynamicOperationColumn.prototype.applyOperationColumnWidth = function(useMeasurement) {
        try {
            var finalWidth = this.operationColumnWidth;
            
            // 如果需要，使用DOM测量获取更精确的宽度
            if (useMeasurement) {
                var measuredWidth = this.calculateOperationColumnWidthFromDOM();
                if (measuredWidth) {
                    finalWidth = measuredWidth;
                    this.operationColumnWidth = finalWidth; // 更新保存的宽度
                }
            }
            
            if (this.debugMode) {
                console.log('开始应用操作列宽度:', finalWidth);
            }
            
            if (!this.tableId) {
                console.error('tableId未设置，无法应用宽度');
                return;
            }
            
            var tableView = $('#' + this.tableId).next('.layui-table-view');
            
            if (tableView.length === 0) {
                if (this.debugMode) {
                    console.log('未找到表格视图容器');
                }
                return;
            }
            
            // 只处理固定右侧列容器
            var fixedRightContainer = tableView.find('.layui-table-fixed-r');
            if (fixedRightContainer.length === 0) {
                if (this.debugMode) {
                    console.log('未找到固定右侧列容器');
                }
                return;
            }
            
            // 设置固定右侧容器的整体宽度
            fixedRightContainer.css({
                'width': finalWidth + 'px',
                'min-width': finalWidth + 'px'
            });
            
            // 设置固定右侧列中的表头单元格
            var fixedHeaderCells = fixedRightContainer.find('.layui-table-header th');
            fixedHeaderCells.css({
                'width': finalWidth + 'px',
                'min-width': finalWidth + 'px'
            });
            
            // 设置固定右侧列中的数据单元格
            var fixedBodyCells = fixedRightContainer.find('.layui-table-body td');
            fixedBodyCells.css({
                'width': finalWidth + 'px',
                'min-width': finalWidth + 'px'
            });
            
            // 设置单元格内容容器
            fixedRightContainer.find('.layui-table-cell').css({
                'width': (finalWidth - 2) + 'px', // 减去边框宽度
                'min-width': (finalWidth - 2) + 'px',
                'padding': '0 8px',
                'text-align': 'center'
            });
            
            if (this.debugMode) {
                console.log('已应用固定列宽度:', finalWidth);
                console.log('- 固定容器数量:', fixedRightContainer.length);
                console.log('- 表头单元格数量:', fixedHeaderCells.length);
                console.log('- 数据单元格数量:', fixedBodyCells.length);
            }
            
            // 隐藏主表格中的操作列（避免重复显示）
            var mainTableCells = tableView.find('.layui-table-main .layui-table-body td:last-child');
            mainTableCells.css('display', 'none');
            
            var mainHeaderCells = tableView.find('.layui-table-main .layui-table-header th:last-child');
            mainHeaderCells.css('display', 'none');
            
            if (this.debugMode) {
                console.log('已隐藏主表格中的重复操作列');
            }
            
        } catch (error) {
            console.error('应用操作列宽度时出错:', error);
        }
    };
    
    /**
     * 生成操作按钮HTML
     * @param {Object} data 行数据
     * @returns {string} 按钮HTML字符串
     */
    DynamicOperationColumn.prototype.generateButtonsHtml = function(data) {
        var html = '<div class="operation-column">';
        
        this.operationButtons.forEach(function(button) {
            if (button.alwaysShow || (button.condition && button.condition(data))) {
                var isDisabled = button.condition && !button.condition(data);
                var disabledClass = isDisabled ? ' disabled-button' : '';
                var eventAttr = isDisabled ? 'data-event="disabled"' : 'lay-event="' + button.event + '"';
                
                html += '<a class="' + button.class + disabledClass + '" ' + eventAttr + '>' + button.text + '</a>';
            }
        });
        
        html += '</div>';
        return html;
    };
    
    /**
     * 获取计算出的操作列宽度
     * @returns {number} 操作列宽度
     */
    DynamicOperationColumn.prototype.getOperationColumnWidth = function() {
        return this.operationColumnWidth;
    };
    
    /**
     * 更新按钮配置
     * @param {Array} buttons 新的按钮配置
     */
    DynamicOperationColumn.prototype.updateButtons = function(buttons) {
        this.operationButtons = buttons || [];
        this.operationColumnWidth = this.calculateOperationColumnWidth();
        window.operationButtons = this.operationButtons;
        
        if (this.debugMode) {
            console.log('按钮配置已更新，新宽度:', this.operationColumnWidth);
        }
    };
    
    /**
     * 延迟应用宽度（用于表格渲染完成后）
     * @param {number} delay 延迟时间（毫秒）
     * @param {boolean} useMeasurement 是否使用DOM测量
     */
    DynamicOperationColumn.prototype.delayApplyWidth = function(delay, useMeasurement) {
        var self = this;
        setTimeout(function() {
            if (useMeasurement) {
                var measuredWidth = self.calculateOperationColumnWidthFromDOM();
                if (measuredWidth && measuredWidth !== self.operationColumnWidth) {
                    if (self.debugMode) {
                        console.log('基于DOM测量调整宽度从', self.operationColumnWidth, '到', measuredWidth);
                    }
                    self.operationColumnWidth = measuredWidth;
                }
            }
            self.applyOperationColumnWidth(false);
            if (self.debugMode) {
                console.log('延迟应用操作列宽度完成');
            }
        }, delay || 300);
    };
    
    // 导出到全局
    window.DynamicOperationColumn = DynamicOperationColumn;
    
    // 创建全局实例（可选）
    window.dynamicOperationColumn = new DynamicOperationColumn();
    
})(window);