﻿using HlktechIoT.Core.Models;
using HlktechIoT.Data;
using HlktechIoT.IoTService.Services;
using HlktechIoT.Services;

using NewLife;
using NewLife.Caching;
using NewLife.IoT.Drivers;
using NewLife.IoT.Models;
using NewLife.IoT.ThingModels;
using NewLife.Log;
using NewLife.Remoting;
using NewLife.Security;
using NewLife.Serialization;
using NewLife.Web;

using System.Reflection;

using XCode;

namespace HlktechIoT.IoTServer.Services;

/// <summary>设备服务</summary>
public class MyDeviceService
{
    /// <summary>节点引用，令牌无效时使用</summary>
    public Device Current { get; set; }

    private readonly ICache _cache;
    private readonly IPasswordProvider _passwordProvider;
    private readonly DataService _dataService;
    private readonly IPushService _pushService;
    private readonly IoTSetting _setting;
    private readonly ITracer _tracer;

    /// <summary>
    /// 实例化设备服务
    /// </summary>
    /// <param name="passwordProvider"></param>
    /// <param name="dataService"></param>
    /// <param name="cacheProvider"></param>
    /// <param name="setting"></param>
    /// <param name="tracer"></param>
    /// <param name="pushService"></param>
    public MyDeviceService(IPasswordProvider passwordProvider, DataService dataService, IPushService pushService, ICacheProvider cacheProvider, IoTSetting setting, ITracer tracer)
    {
        _passwordProvider = passwordProvider;
        _dataService = dataService;
        _pushService = pushService;
        _cache = cacheProvider.InnerCache;
        _setting = setting;
        _tracer = tracer;
    }

    #region 登录
    /// <summary>
    /// 设备登录验证，内部支持动态注册
    /// </summary>
    /// <param name="inf">登录信息</param>
    /// <param name="source">登录来源</param>
    /// <param name="ip">远程IP</param>
    /// <returns></returns>
    /// <exception cref="ApiException"></exception>
    public LoginResponse Login(LoginInfo inf, String source, String ip)
    {
        var code = inf.Code;
        var secret = inf.Secret;

        var dv = Device.FindByCode(code);
        Current = dv;

        var autoReg = false;
        if (dv == null)
        {
            if (inf.ProductKey.IsNullOrEmpty()) throw new ApiException(98, "找不到设备，且产品证书为空，无法登录");

            dv = AutoRegister(null, inf, ip);
            autoReg = true;
        }
        else
        {
            if (!dv.Enable) throw new ApiException(99, "禁止登录");

            // 校验唯一编码，防止客户端拷贝配置
            var uuid = inf.UUID;
            if (!uuid.IsNullOrEmpty() && !dv.Uuid.IsNullOrEmpty() && uuid != dv.Uuid)
                WriteHistory(dv, source + "登录校验", false, $"新旧唯一标识不一致！（新）{uuid}!={dv.Uuid}（旧）", ip);

            // 登录密码未设置或者未提交，则执行动态注册
            if (!dv.Secret.IsNullOrEmpty()
                && (secret.IsNullOrEmpty() || !_passwordProvider.Verify(dv.Secret, secret)))
            {
                if (inf.ProductKey.IsNullOrEmpty()) throw new ApiException(98, "设备验证失败，且产品证书为空，无法登录");

                XTrace.WriteLine($"判断动态注册条件：{dv.Secret}:{secret}:{_passwordProvider.Verify(dv.Secret, secret)}");

                dv = AutoRegister(dv, inf, ip);
                autoReg = true;
            }
        }

        //if (dv != null && !dv.Enable) throw new ApiException(99, "禁止登录");

        Current = dv ?? throw new ApiException(12, "节点鉴权失败");

        dv.Login(inf, ip);

        // 设置令牌
        var tm = IssueToken(dv.Code, _setting);

        // 在线记录
        var olt = GetOnline(dv, ip) ?? CreateOnline(dv, ip);
        olt.AccessMode = source;
        olt.Save(inf, null, tm.AccessToken);

        SetChildOnline(dv, source, ip);

        // 登录历史
        WriteHistory(dv, source + "设备鉴权", true, $"[{dv.Name}/{dv.Code}]鉴权成功 " + inf.ToJson(false, false, false), ip);

        var rs = new LoginResponse
        {
            Name = dv.Name,
            Token = tm.AccessToken,
            Time = DateTime.UtcNow.ToLong(),
        };

        // 动态注册的设备不可用时，不要发令牌，只发证书
        if (!dv.Enable) rs.Token = null;

        // 动态注册，下发节点证书
        if (autoReg) rs.Secret = dv.Secret;

        rs.Code = dv.Code;

        return rs;
    }

    void SetChildOnline(Device dv, String source, String ip)
    {
        if (_setting.UseChildDeviceOnline && dv.ChildOnline)
        {
            using var span = _tracer?.NewSpan(nameof(SetChildOnline), new { dv.Name, dv.Code, ip });

            // 所有子设备上线
            foreach (var child in Device.FindAllByParent(dv.Id))
            {
                if (child.Enable)
                {
                    var olt2 = GetOnline(child, ip) ?? CreateOnline(child, ip);
                    olt2.AccessMode = source;
                    olt2.Save(null, null, null);

                    span?.AppendTag($"{child.Name} online={olt2.Id}");
                }
                else
                {
                    // 被禁用的设备，自动下线
                    child.Online = false;
                    child.Update();
                }
            }
        }
    }

    /// <summary>设置设备在线，同时检查在线表</summary>
    /// <param name="dv"></param>
    /// <param name="ip"></param>
    /// <param name="reason"></param>
    public void SetDeviceOnline(Device dv, String ip, String reason)
    {
        // 如果已上线，则不需要埋点
        var tracer = _tracer;
        //if (dv.Online) tracer = null;
        using var span = tracer?.NewSpan(nameof(SetDeviceOnline), new { dv.Name, dv.Code, ip, reason });

        var olt = GetOnline(dv, ip) ?? CreateOnline(dv, ip);
        olt.Remark = reason;

        dv.SetOnline(ip, reason);

        // 避免频繁更新心跳数
        if (olt.UpdateTime.AddSeconds(60) < DateTime.Now)
            olt.Save(null, null, null);
    }

    /// <summary>自动注册</summary>
    /// <param name="device"></param>
    /// <param name="inf"></param>
    /// <param name="ip"></param>
    /// <returns></returns>
    /// <exception cref="ApiException"></exception>
    public Device AutoRegister(Device device, LoginInfo inf, String ip)
    {
        // 全局开关，是否允许自动注册新产品
        if (!_setting.AutoRegister) throw new ApiException(12, $"禁止自动注册:[{inf.Code}]");

        // 验证产品，即使产品不给自动注册，也会插入一个禁用的设备
        var product = Product.FindByCode(inf.ProductKey);
        if (product == null || !product.Enable)
            throw new ApiException(13, $"无效产品[{inf.ProductKey}]！");
        if (!product.Secret.IsNullOrEmpty() && !_passwordProvider.Verify(product.Secret, inf.ProductSecret))
            throw new ApiException(13, $"非法产品[{product}]！");

        // 检查白名单
        if (!product.IsMatchWhiteIP(ip)) throw new ApiException(13, "非法来源，禁止注册");

        var code = inf.Code;
        if (code.IsNullOrEmpty()) code = Rand.NextString(8);

        device ??= new Device
        {
            Code = code,
            CreateIP = ip,
            CreateTime = DateTime.Now,
            Secret = Rand.NextString(8),
        };

        // 如果未打开动态注册，则把节点修改为禁用
        device.Enable = product.DynamicRegister;

        if (device.Name.IsNullOrEmpty()) device.Name = inf.Name;

        device.ProductId = product.Id;
        //device.Secret = Rand.NextString(16);
        device.UpdateIP = ip;
        device.UpdateTime = DateTime.Now;

        device.Save();

        // 自动发布物模型以及拷贝驱动参数
        device.Fix(true, true);

        // 更新产品设备总量避免界面无法及时获取设备数量信息
        device.Product.Fix();

        WriteHistory(device, "动态注册", true, inf.ToJson(false, false, false), ip);

        return device;
    }

    /// <summary>注销</summary>
    /// <param name="device">设备</param>
    /// <param name="reason">注销原因</param>
    /// <param name="source">登录来源</param>
    /// <param name="ip">远程IP</param>
    /// <returns></returns>
    public Device Logout(Device device, String reason, String source, String ip)
    {
        var olt = GetOnline(device, ip);
        if (olt != null)
        {
            var msg = $"{reason} [{device}]]登录于{olt.CreateTime.ToFullString()}，最后活跃于{olt.UpdateTime.ToFullString()}";
            WriteHistory(device, source + "设备下线", true, msg, ip);
            olt.Delete();

            RemoveOnline(device.Id, ip);

            // 计算在线时长
            if (olt.CreateTime.Year > 2000)
                device.OnlineTime += (Int32)(DateTime.Now - olt.CreateTime).TotalSeconds;

            //DeviceOnlineService.CheckOffline(device, "注销");
        }

        device.Logout();

        return device;
    }
    #endregion

    #region 心跳
    /// <summary>
    /// 心跳
    /// </summary>
    /// <param name="device"></param>
    /// <param name="inf"></param>
    /// <param name="token"></param>
    /// <param name="ip"></param>
    /// <param name="source"></param>
    /// <returns></returns>
    public DeviceOnline Ping(Device device, PingInfo inf, String token, String source, String ip)
    {
        if (inf != null && !inf.IP.IsNullOrEmpty()) device.IP = inf.IP;

        // 自动上线
        if (device != null && !device.Online) device.SetOnline(ip, "心跳");

        // 更新最后活跃时间
        if (device.LastActive.AddSeconds(IoTSetting.Current.SessionTimeout) < DateTime.Now) device.LastActive = DateTime.Now;

        device.UpdateIP = ip;
        device.FixArea();

        // 设备版本号

        if (inf?.Version.IsNullOrWhiteSpace() == false)
        {
            device.Version = inf?.Version;
        }

        device.Module = inf?.Module;

        device.SaveAsync();

        var olt = GetOnline(device, ip) ?? CreateOnline(device, ip);
        olt.Name = device.Name;
        olt.GroupPath = device.GroupPath;
        olt.ProductId = device.ProductId;
        olt.ParentId = device.ParentId;
        olt.ProvinceId = device.ProvinceId;
        olt.CityId = device.CityId;
        olt.AccessMode = source;
        olt.Save(null, inf, token);

        return olt;
    }

    private static IList<DeviceServiceLog> _commands;
    private static DateTime _nextTime;

    /// <summary>
    /// 申请下行命令
    /// </summary>
    /// <param name="deviceId"></param>
    /// <returns></returns>
    public ServiceModel[] AcquireCommands(Int32 deviceId)
    {
        // 缓存最近1000个未执行命令，用于快速过滤，避免大量节点在线时频繁查询命令表
        if (_nextTime < DateTime.Now)
        {
            _commands = DeviceServiceLog.AcquireCommands(-1, 1000);
            _nextTime = DateTime.Now.AddMinutes(1);
        }

        // 是否有本节点
        if (!_commands.Any(e => e.DeviceId == deviceId)) return null;

        var logs = DeviceServiceLog.AcquireCommands(deviceId, 100);
        if (logs == null) return null;

        var rs = new List<ServiceModel>();
        foreach (var item in logs)
        {
            if (item.Times > 10 || item.Expire.Year > 2000 && item.Expire < DateTime.Now)
                item.Status = ServiceStatus.取消;
            else
            {
                if (item.Status == ServiceStatus.处理中 && item.UpdateTime.AddMinutes(10) < DateTime.Now) continue;

                item.Times++;
                item.Status = ServiceStatus.处理中;
                rs.Add(item.ToServiceModel());
            }
            item.UpdateTime = DateTime.Now;

            //var svc = DeviceService.FindByDeviceIdAndName(item.DeviceId, item.Name);
            //if (svc == null) svc = new DeviceService { DeviceId = item.DeviceId, Name = item.Name, Enable = true };
            //svc.Status = item.Status;
            //svc.Save();
        }
        logs.Update(false);

        return rs.ToArray();
    }

    public DeviceOnline GetOrAddOnline(Device device, String ip)
    {
        var localIp = device?.IP;
        if (localIp.IsNullOrEmpty()) localIp = ip;

        return GetOnline(device, localIp) ?? CreateOnline(device, ip);
    }

    /// <summary></summary>
    /// <param name="device"></param>
    /// <param name="ip"></param>
    /// <returns></returns>
    protected virtual DeviceOnline GetOnline(Device device, String ip)
    {
        var sid = $"{device.Id}@{ip}";
        var olt = _cache.Get<DeviceOnline>($"DeviceOnline:{sid}");
        if (olt != null)
        {
            _cache.SetExpire($"DeviceOnline:{sid}", TimeSpan.FromSeconds(600));
            return olt;
        }

        return DeviceOnline.FindBySessionId(sid);
    }

    /// <summary>检查在线</summary>
    /// <param name="device"></param>
    /// <param name="ip"></param>
    /// <returns></returns>
    protected virtual DeviceOnline CreateOnline(Device device, String ip)
    {
        var sid = $"{device.Id}@{ip}";
        var olt = DeviceOnline.GetOrAdd(sid);
        olt.ProductId = device.ProductId;
        olt.ParentId = device.ParentId;
        olt.DeviceId = device.Id;
        olt.Name = device.Name;
        olt.IP = device.IP;
        olt.CreateIP = ip;
        olt.ProvinceId = device.ProvinceId;
        olt.CityId = device.CityId;

        olt.Creator = Environment.MachineName;

        _cache.Set($"DeviceOnline:{sid}", olt, 600);

        return olt;
    }

    /// <summary>删除在线</summary>
    /// <param name="deviceId"></param>
    /// <param name="ip"></param>
    /// <returns></returns>
    public Int32 RemoveOnline(Int32 deviceId, String ip)
    {
        var sid = $"{deviceId}@{ip}";

        return _cache.Remove($"DeviceOnline:{sid}");
    }
    #endregion

    #region 升级
    /// <summary>升级检查</summary>
    /// <returns></returns>
    public ProductVersion Upgrade(Device device, String ip)
    {
        // 找到所有产品版本
        var list = ProductVersion.GetValids();

        // 应用过滤规则，使用最新的一个版本
        var pv = list.Where(e => e.Match(device)).OrderByDescending(e => e.Version).FirstOrDefault();
        if (pv == null) return null;
        //if (pv == null) throw new ApiException(509, "没有升级规则");

        WriteHistory(device, "自动更新", true, $"=> [{pv.Id}] {pv.Version} {pv.Source} {pv.Executor}", ip);

        return pv;
    }
    #endregion

    #region 设备通道
    /// <summary>获取设备列表，主设备加多个子设备</summary>
    /// <param name="device"></param>
    /// <returns></returns>
    public DeviceModel[] GetDevices(Device device)
    {
        var list = Device.FindAllByParent(device.Id);
        list.Insert(0, device);

        return list.Where(e => e.Enable).Select(e => e.ToDeviceModel()).ToArray();
    }

    /// <summary>设备上线。驱动打开后调用，子设备发现，或者上报主设备/子设备的默认参数模版</summary>
    /// <remarks>
    /// 有些设备驱动具备扫描发现子设备能力，通过该方法上报设备。
    /// 主设备或子设备，也可通过该方法上报驱动的默认参数模版。
    /// 根据需要，驱动内可能多次调用该方法。
    /// </remarks>
    /// <param name="device">主设备</param>
    /// <param name="devices">设备信息集合。可传递参数模版</param>
    /// <returns>返回上报信息对应的反馈，如果新增子设备，则返回子设备信息</returns>
    public DeviceModel[] SetOnline(Device device, DeviceModel[] devices)
    {
        if (devices == null || devices.Length == 0) return null;

        var rs = new DeviceModel[devices.Length];
        for (var i = 0; i < devices.Length; i++)
        {
            var item = devices[i];
            var dv = Device.FindByCode(item.Code);
            if (dv == null && !item.Code.IsNullOrEmpty())
            {
                // 自动创建设备
                var product = Product.FindByCode(item.ProductCode) ?? device.Product;
                if (product != null)
                {
                    dv = new Device
                    {
                        Code = item.Code,
                        Name = item.Name,
                        ProductId = product.Id,
                        ParentId = device.Id,
                        Enable = product.DynamicRegister,

                        Parameter = item.Parameter,
                    };
                }
            }
            if (dv != null)
            {
                // 修复参数模版
                if (!item.Parameter.IsNullOrEmpty()) dv.Parameter = item.Parameter;

                dv.Online = true;

                dv.Save();

                rs[i] = dv.ToDeviceModel();
            }
        }

        return rs;
    }

    /// <summary>设备下线。驱动内子设备变化后调用</summary>
    /// <remarks>
    /// 根据需要，驱动内可能多次调用该方法。
    /// </remarks>
    /// <param name="device">主设备</param>
    /// <param name="devices">设备编码集合。用于子设备离线</param>
    /// <returns>返回上报信息对应的反馈，如果新增子设备，则返回子设备信息</returns>
    public DeviceModel[] SetOffline(Device device, String[] devices)
    {
        if (devices == null || devices.Length == 0) return null;

        var rs = new DeviceModel[devices.Length];
        for (var i = 0; i < devices.Length; i++)
        {
            var dv = Device.FindByCode(devices[i]);
            if (dv != null)
            {
                dv.Online = false;
                dv.Update();

                rs[i] = dv.ToDeviceModel();
            }
        }

        return rs;
    }

    /// <summary>提交驱动信息。客户端把自己的驱动信息提交到平台</summary>
    /// <param name="device">主设备</param>
    /// <param name="drivers"></param>
    /// <returns></returns>
    public IList<String> PostDriver(Device device, DriverInfo[] drivers)
    {
        if (drivers == null || drivers.Length == 0) return new List<String>();

        var rs = new List<String>();
        var list = Driver.FindAll();
        foreach (var item in drivers)
        {
            var drv = list.FirstOrDefault(e => e.Name.EqualIgnoreCase(item.Name));
            drv ??= new Driver { Name = item.Name, Enable = true };

            // 新版本驱动信息强行覆盖旧版本
            if (drv.Version.IsNullOrEmpty() || !item.Version.IsNullOrEmpty() && new Version(item.Version) > new Version(drv.Version))
            {
                drv.DisplayName = item.DisplayName;

                if (drv.Type.IsNullOrEmpty()) drv.Type = ".NET";
                drv.ClassName = item.ClassName;
                drv.Version = item.Version;
                drv.IoTVersion = item.IoTVersion;

                drv.DefaultParameter = item.DefaultParameter;

                // 有更新
                rs.Add(drv.Name);
            }

            drv.Save();

            list.Add(drv);
        }

        // 修正设备的驱动配置
        FixDeviceParameter(device, drivers);
        foreach (var item in Device.FindAllByParent(device.Id))
        {
            FixDeviceParameter(item, drivers);
        }

        return rs;
    }

    /// <summary>修正设备参数</summary>
    /// <param name="device"></param>
    /// <param name="drivers"></param>
    private void FixDeviceParameter(Device device, DriverInfo[] drivers)
    {
        var protocol = device?.Product?.ProtocolType;
        if (protocol.IsNullOrEmpty()) return;

        var drv = drivers.FirstOrDefault(e => e.Name.EqualIgnoreCase(protocol));
        if (drv != null && !drv.DefaultParameter.IsNullOrEmpty())
        {
            if (device.Parameter.IsNullOrEmpty())
            {
                device.Parameter = drv.DefaultParameter;
            }
            else
            {
                try
                {
                    var ps = XmlParser.Decode(drv.DefaultParameter.TrimStart((Char)0xFEFF));
                    device.FixParameter(ps);
                }
                catch { }
            }
            device.Update();
        }
    }
    #endregion

    #region 辅助
    /// <summary>
    /// 颁发令牌
    /// </summary>
    /// <param name="name"></param>
    /// <param name="set"></param>
    /// <returns></returns>
    public TokenModel IssueToken(String name, IoTSetting set)
    {
        // 颁发令牌
        var ss = set.TokenSecret.Split(':');
        var jwt = new JwtBuilder
        {
            Issuer = Assembly.GetEntryAssembly().GetName().Name,
            Subject = name,
            Id = Rand.NextString(8),
            Expire = DateTime.Now.AddSeconds(set.TokenExpire),

            Algorithm = ss[0],
            Secret = ss[1],
        };

        return new TokenModel
        {
            AccessToken = jwt.Encode(null),
            TokenType = jwt.Type ?? "JWT",
            ExpireIn = set.TokenExpire,
            RefreshToken = jwt.Encode(null),
        };
    }

    /// <summary>
    /// 解码令牌，并验证有效性
    /// </summary>
    /// <param name="token"></param>
    /// <param name="tokenSecret"></param>
    /// <returns></returns>
    /// <exception cref="ApiException"></exception>
    public Device DecodeToken(String token, String tokenSecret)
    {
        //if (token.IsNullOrEmpty()) throw new ArgumentNullException(nameof(token));
        if (token.IsNullOrEmpty()) throw new ApiException(402, "节点未登录");

        // 解码令牌
        var ss = tokenSecret.Split(':');
        var jwt = new JwtBuilder
        {
            Algorithm = ss[0],
            Secret = ss[1],
        };

        var rs = jwt.TryDecode(token, out var message);
        var node = Device.FindByCode(jwt.Subject);
        Current = node;
        if (!rs) throw new ApiException(403, $"非法访问 {message}");

        return node;
    }

    /// <summary>
    /// 验证并颁发令牌
    /// </summary>
    /// <param name="deviceCode"></param>
    /// <param name="token"></param>
    /// <returns></returns>
    public TokenModel ValidAndIssueToken(String deviceCode, String token)
    {
        if (token.IsNullOrEmpty()) return null;

        // 令牌有效期检查，10分钟内过期者，重新颁发令牌
        var ss = _setting.TokenSecret.Split(':');
        var jwt = new JwtBuilder
        {
            Algorithm = ss[0],
            Secret = ss[1],
        };
        var rs = jwt.TryDecode(token, out var message);
        if (!rs || jwt == null) return null;

        if (DateTime.Now.AddMinutes(10) > jwt.Expire) return IssueToken(deviceCode, _setting);

        return null;
    }

    static readonly String[] _keys = new[] { "设备", "注册", "登录", "注销", "下线", "上线" };
    /// <summary>
    /// 写设备历史
    /// </summary>
    /// <param name="device"></param>
    /// <param name="action"></param>
    /// <param name="success"></param>
    /// <param name="remark"></param>
    /// <param name="ip"></param>
    public void WriteHistory(Device device, String action, Boolean success, String remark, String ip)
    {
        var traceId = DefaultSpan.Current?.TraceId;
        var hi = DeviceHistory.Create(device ?? Current, action, success, remark, Environment.MachineName, ip, traceId);

        if (_setting.DataQueue) _dataService.AddHistory(hi);

        // 数据推送
        var ev = new EventModel
        {
            Time = DateTime.UtcNow.ToLong(),
            Type = success ? "info" : "error",
            Name = action,
            Remark = remark,
        };
        if (_keys.Any(action.Contains)) ev.Data = device;

        if (device != null)
            _pushService.PushEvent(device, new EventModels { DeviceCode = device.Code, Items = new[] { ev } });
    }
    #endregion
}