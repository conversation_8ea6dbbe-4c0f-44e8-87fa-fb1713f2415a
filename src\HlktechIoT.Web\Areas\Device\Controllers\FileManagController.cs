﻿using DG.Web.Framework;

using DH;
using DH.Entity;

using HlktechIoT.Data;
using HlktechIoT.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Serialization;

using Pek;
using Pek.Helpers;
using Pek.Iot;
using Pek.Models;
using Pek.Webs;

using System.ComponentModel;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Device.Controllers;

/// <summary>固件列表</summary>
[DisplayName("模组固件管理")]
[Description("固件上传文件管理")]
[DeviceArea]
[DHMenu(100, ParentMenuName = "FileManag", ParentMenuDisplayName = "固件管理", ParentMenuUrl = "", ParentMenuOrder = 40, ParentIcon = "layui-icon-component", CurrentMenuUrl = "~/{area}/FileManag", CurrentMenuName = "FileManagList", LastUpdate = "20240508")]
public class FileManagController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 100;

    /// <summary>
    /// 固件管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("固件管理")]
    [HttpGet]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// 根据条件搜索
    /// </summary>
    /// <param name="key">文件名、版本、备注等</param>
    /// <param name="PType">固件类型</param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("固件查询")]
    public IActionResult GetList(String key, Int32 PType = -1, Int32 page = 1, Int32 limit = 10)
    {
        if (!key.IsNullOrWhiteSpace()) key = key.Trim();
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = FirmwareInfo._.Id,
            Desc = true
        };

        var exp = new WhereExpression();
        if (PType >= 0)
        {
            exp &= FirmwareInfo._.FType == PType;
        }
        if (!key.IsNullOrEmpty())
        {
            exp &= FirmwareInfo._.OriginFileName.Contains(key) | FirmwareInfo._.FileUrl.Contains(key) | FirmwareInfo._.SVersions.Contains(key) | FirmwareInfo._.Remark.Contains(key) | FirmwareInfo._.ProductKey.Contains(key);
        }

        var list = FirmwareInfo.FindAll(exp, pages);

        var data = list.Select(x => new { x.Id, x.OriginFileName, x.FileSize, x.FileUrl, x.SVersions, x.ProductKey, x.CreateTime, x.UpdateTime, x.CreateUser, x.UpdateUser, ModuleName = x.ProductModule?.Code, x.Remark, x.DataSize, x.FType });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 搜索产品
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [HttpPost]
    [DisplayName("搜索产品")]
    public IActionResult SearchProduct(String ProductKey, String keyword, Int32 page)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
        };

        var modelProduct = Product.FindByCode(ProductKey);
        if (modelProduct != null)
        {
            res.data = new List<Xmselect<Int32>>
            {
                new() {
                    name = modelProduct.Name + $"({modelProduct.Code})",
                    value = modelProduct.Id,
                    selected = true
                }
            };
        }

        //var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        //if (!modelRole.IsAdmin)
        //{
        //res.data = Product.FindAllByProjectKey(ProductKey).Select(e =>
        //{
        //    var selected = true;

        //    return new Xmselect<Int32>
        //    {
        //        name = e.Name + $"({e.Code})",
        //        value = e.Id,
        //        selected = selected
        //    };
        //});
        //}
        //else
        //{
        //    res.data = Product.Search(keyword, 0, pages).Select(e =>
        //    {
        //        var selected = true;

        //        return new Xmselect<Int32>
        //        {
        //            name = e.Name + $"({e.Code})",
        //            value = e.Id,
        //            selected = selected
        //        };
        //    });
        //}

        res.extdata = pages.PageCount;
        res.success = true;
        return Json(res);
    }

    /// <summary>
    /// 搜索产品型号
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [HttpPost]
    [DisplayName("搜索产品型号")]
    public IActionResult SearchProductModule(String keyword, Int32 page)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = ProductModule._.Id,
            Desc = true,
        };

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            res.data = ProductModule.Search(keyword, ManageProvider.User?.ID ?? -1, pages).Select(e =>
            {
                var selected = false;

                return new Xmselect<Int32>
                {
                    name = e.Code + $"({e.Remark})",
                    value = e.Id,
                    selected = selected
                };
            });
        }
        else
        {
            res.data = ProductModule.Search(keyword, 0, pages).Select(e =>
            {
                var selected = false;

                return new Xmselect<Int32>
                {
                    name = e.Code + $"({e.Remark})",
                    value = e.Id,
                    selected = selected
                };
            });
        }

        res.extdata = pages.PageCount;
        res.success = true;
        return Json(res);
    }

    /// <summary>
    /// 上传固件
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("上传固件")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult UploadFiles(IFormFile file)
    {
        var res = new DResult();
        if (file == null)
        {
            XTrace.WriteLine("获取到的文件为空");

            res.msg = GetResource("固件上传有误");
            return Json(res);
        }

        var version = String.Empty;
        var pk = String.Empty;
        var size = 0L;

        var OrignfileName = file.FileName;

        var header = file.OpenReadStream().ReadBytes(file.Length).ToHex();
        var arrayHex = header.Split("0D0A", StringSplitOptions.RemoveEmptyEntries);

        if (arrayHex.Length < 4)
        {
            res.msg = GetResource("固件格式有误");
            return Json(res);
        }

        version = AsciiHelper.HexStringToAsciiString(arrayHex[0]);

        if (version.Split('-').Length != 3)
        {
            res.msg = GetResource("固件版本号格式有误");
            return Json(res);
        }

        pk = arrayHex[1].ToHex().ToStr();
        size = AsciiHelper.HexStringToAsciiString(arrayHex[2]).ToLong();

        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(OrignfileName)}";
        var filepath = DHSetting.Current.UploadPath.CombinePath($"FirmwareInfo/{filename}");

        var saveFileName = filepath.GetFullPath();
        var f = saveFileName.AsFile();
        if (f.Exists)
        {
            f.Delete();
        }
        saveFileName.EnsureDirectory();
        file.SaveAs(saveFileName, 200, (Int32)file.Length - 200);

        res.msg = GetResource("文件上传成功");
        res.success = true;
        res.data = new { OriginFileName = OrignfileName, FileSize = file.Length, FileUrl = filepath.Replace("\\", "/"), FileHash = file.OpenReadStream().ReadBytes(-1).MD5(), Version = version, ProductKey = pk, DataSize = size };
        return Json(res);
    }

    /// <summary>
    /// 新增固件
    /// </summary>
    /// <returns></returns>
    [DisplayName("新增固件")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult Add()
    {
        return View();
    }

    /// <summary>
    /// 新增固件
    /// </summary>
    /// <returns></returns>
    [DisplayName("新增固件")]
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    public IActionResult Add(String OriginFileName, Int32 FileSize, String FileUrl, String Versions, Int32 select, String Remark, String Executor, String Force, short FType, String ForcedUpgrade, int DataSize, string ProductKey)
    {
        var res = new DResult();

        var model = new FirmwareInfo();
        model.OriginFileName = OriginFileName;
        model.FileSize = FileSize;
        model.FileUrl = FileUrl;
        model.SVersions = Versions;
        model.ProductType = select;
        model.Remark = Remark;
        model.Executor = Executor;
        model.Force = Force.SafeString() == "on";
        model.ForcedUpgrade = ForcedUpgrade.SafeString() == "on";
        model.FType = FType;

        model.Version1 = Versions.Split('-')[0];
        model.Version2 = Versions.Split('-')[1].Replace("V", "").ToDGDouble();
        model.Version3 = Versions.Split('-')[2].Replace(".", "").ToDGLong();
        model.DataSize = DataSize;
        model.ProductKey = ProductKey;

        var modelProduct = Product.FindByCode(ProductKey);
        if (modelProduct != null)
        {
            model.ProductType = modelProduct.ProductModule?.Id ?? 0;
        }

        model.Insert();

        res.success = true;
        res.msg = GetResource("新增成功");
        return Json(res);
    }

    /// <summary>
    /// 编辑固件
    /// </summary>
    /// <returns></returns>
    [DisplayName("编辑固件")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult Edit(Int32 Id)
    {
        var model = FirmwareInfo.FindById(Id);

        if (model == null)
        {
            return Content(GetResource("固件不存在"));
        }

        var modelProductModule = ProductModule.FindById(model.ProductType);
        ViewBag.ModuleList = new List<NameValueL<Int32?>>() { new() { name = modelProductModule?.Code + $"({modelProductModule?.Remark})", value = modelProductModule?.Id } }.ToJson();

        return View(model);
    }

    /// <summary>
    /// 编辑固件
    /// </summary>
    /// <returns></returns>
    [DisplayName("编辑固件")]
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    public IActionResult Edit(Int32 Id, String OriginFileName, Int32 FileSize, String FileUrl, String Versions, Int32 select, String Remark, String Executor, String Force, short FType, String ForcedUpgrade, int DataSize, string ProductKey)
    {
        var res = new DResult();

        var model = FirmwareInfo.FindById(Id);

        if (model == null)
        {
            return Json(new { success = false, msg = GetResource("固件不存在") });
        }

        model.OriginFileName = OriginFileName;
        model.FileSize = FileSize;
        model.FileUrl = FileUrl;
        model.SVersions = Versions;
        model.ProductType = select;
        model.Remark = Remark;
        model.Executor = Executor;
        model.Force = Force.SafeString() == "on";
        model.ForcedUpgrade = ForcedUpgrade.SafeString() == "on";
        model.FType = FType;

        model.Version1 = Versions.Split('-')[0];
        model.Version2 = Versions.Split('-')[1].Replace("V", "").ToDGDouble();
        model.Version3 = Versions.Split('-')[2].Replace(".", "").ToDGLong();
        model.DataSize = DataSize;
        model.ProductKey = ProductKey;

        var modelProduct = Product.FindByCode(ProductKey);
        if (modelProduct != null)
        {
            model.ProductType = modelProduct.ProductModule?.Id ?? 0;
        }

        model.Update();

        res.success = true;
        res.msg = GetResource("编辑成功");
        return Json(res);
    }

    /// <summary>
    /// 删除固件
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除固件")]
    [HttpPost]
    public IActionResult Delete(Int32 Id)
    {
        var res = new DResult();

        var model = FirmwareInfo.FindById(Id);
        if (model != null)
        {
            var file = model.FileUrl.GetFullPath().AsFile();
            if (file.Exists)
            {
                file.Delete();
            }
            model.Delete();
        }

        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 批量升级管理
    /// </summary>
    /// <returns></returns>
    [DisplayName("批量升级管理")]
    [EntityAuthorize((PermissionFlags)32)]
    public IActionResult MultipleUpdate(Int32 Id)
    {
        var model = FirmwareInfo.FindById(Id);

        if (model == null)
        {
            return Content(GetResource("固件不存在"));
        }

        var modelProductModule = ProductModule.FindById(model.ProductType);
        if (modelProductModule == null)
        {
            return Content(GetResource("产品型号不存在"));
        }

        return View(model);
    }

    /// <summary>
    /// 批量升级列表
    /// </summary>
    /// <returns></returns>
    [DisplayName("批量升级列表")]
    [EntityAuthorize((PermissionFlags)32)]
    public IActionResult MultipleList(string id, int page, int limit, string key)
    {
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = BatchUpgradeFirmwareEx._.Id,
            Desc = true
        };

        var list = BatchUpgradeFirmwareEx.SearchByIdAndUpgradeFirmwareId(id, pages, key);

        var data = list.Select(x => new { x.Id, x.BType, x.UType, x.Status, x.CreateTime });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 批次详情
    /// </summary>
    /// <returns></returns>
    [DisplayName("批次详情")]
    [EntityAuthorize((PermissionFlags)32)]
    public IActionResult BatchDetails(Int32 Id)
    {

        var model = BatchUpgradeFirmware.FindById(Id);

        if (model == null)
        {
            return Content(GetResource("批次不存在"));
        }

        //var batchFirmwareUpdateItems = BatchFirmwareUpdateItems.FindAllByBatchUpgradeFirmwareId(Id);

        return View(model);
    }

    /// <summary>
    /// 批次设备列表
    /// </summary>
    /// <returns></returns>
    [DisplayName("批次设备列表")]
    [EntityAuthorize((PermissionFlags)32)]
    public IActionResult DeviceList(string id, int page, int limit, string key, int state = -1)
    {
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true
        };

        var list = BatchFirmwareUpdateItemsEx.SearchByBatchUpgradeFirmwareId(id, pages, key, state);

        var data = list.Select(x => new
        {
            x.DeviceName,
            x.LastVersions,
            x.UpdateTime,
            x.Status,
            ProductModule = Data.Device.FindByCode(x.DeviceName)?.Module ?? "",
            x.Remarks
        });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 升级
    /// </summary>
    /// <returns></returns>
    [DisplayName("升级")]
    [EntityAuthorize((PermissionFlags)32)]
    public IActionResult Update(Int32 Id)
    {
        var model = FirmwareInfo.FindById(Id);

        if (model == null)
        {
            return Content(GetResource("固件不存在"));
        }

        var modelProductModule = ProductModule.FindById(model.ProductType);
        return View(model);
    }

    /// <summary>
    /// 升级
    /// </summary>
    /// <returns></returns>
    [DisplayName("升级")]
    [EntityAuthorize((PermissionFlags)32)]
    [HttpPost]
    public IActionResult Update(int firmwareId, string versionToBeUpgraded, string appPush, string product, Int16 upgradeStrategy, Int16 upgradeScope, string deviceScope, string province, string city, Int16 grayRange, Int16 upgradeTime, DateTime startTime, DateTime endTime, int pushRate, Int16 retryInterval, int retryLimit, int timeout, string isOverride, string onlyNewlyReported)
    {
        var res = new DResult();

        if (firmwareId <= 0)
        {
            res.msg = GetResource("固件不存在");
            return Json(res);
        }
        var firmwareInfo = FirmwareInfo.FindById(firmwareId);
        if (firmwareInfo == null)
        {
            res.msg = GetResource("固件不存在");
            return Json(res);
        }

        var BatchUpgradeFirmware = new BatchUpgradeFirmware();
        BatchUpgradeFirmware.BType = 2;//批量升级
        BatchUpgradeFirmware.FirmwareId = firmwareId;
        BatchUpgradeFirmware.UpgradeVersions = versionToBeUpgraded;
        BatchUpgradeFirmware.AppConfirmUpgrade = appPush == "on";
        BatchUpgradeFirmware.Products = product;
        BatchUpgradeFirmware.UType = upgradeStrategy;
        if (upgradeStrategy == 2)
        {
            BatchUpgradeFirmware.OnlyNewVersion = onlyNewlyReported == "on";
        }
        BatchUpgradeFirmware.Condition = upgradeScope;
        if (upgradeScope == 0)
        {
            var pages = new PageParameter
            {
                PageIndex = 1,
                PageSize = 0,
            };
            var deviceIds = DeviceEx.Search(product, "", 0, pages, versionToBeUpgraded).Select(e => e.Id).ToList();
            foreach (var deviceId in deviceIds)
            {
                var device = DeviceEx.FindById(deviceId);
                var item = new BatchFirmwareUpdateItems
                {
                    BatchUpgradeFirmwareId = BatchUpgradeFirmware.Id,
                    DeviceName = device.Name,
                    LastVersions = device.Version,
                    Status = 0
                };
                item.Insert();
            }
        }
        else if (upgradeScope == 1)//定向设备固定写到items表，剩余情况未实现
        {
            var deviceIds = deviceScope.Split(',').Select(e => e.ToInt()).ToList();
            foreach (var deviceId in deviceIds)
            {
                var device = DeviceEx.FindById(deviceId);
                var item = new BatchFirmwareUpdateItems
                {
                    BatchUpgradeFirmwareId = BatchUpgradeFirmware.Id,
                    DeviceName = device.Name,
                    LastVersions = device.Version,
                    Status = 0
                };
                item.Insert();
            }
        }
        else if (upgradeScope == 2)
        {
            // 省份和城市
            var provinces = province.Split(',').Select(int.Parse).ToList();
            var cities = city.Split(',').Select(int.Parse).ToList();
            var areas = provinces.Zip(cities, (p, c) => new { Province = p, City = c })
                                 .GroupBy(x => new { x.Province, x.City })
                                 .Select(g => new[] { g.Key.Province, g.Key.City })
                                 .ToList();
            BatchUpgradeFirmware.Area = areas.ToJson();
        }
        else if (upgradeScope == 3)
        {
            BatchUpgradeFirmware.GrayscaleRange = grayRange;
        }
        BatchUpgradeFirmware.TType = upgradeTime;
        if (upgradeTime == 2)
        {
            BatchUpgradeFirmware.StartTime = startTime;
            BatchUpgradeFirmware.EndTime = endTime;
        }
        BatchUpgradeFirmware.PushRate = pushRate;
        BatchUpgradeFirmware.RetryInterval = retryInterval;
        if (retryInterval != 0)
        {
            BatchUpgradeFirmware.Versions = retryLimit switch
            {
                1 => "1",
                2 => "2",
                3 => "3",
                _ => BatchUpgradeFirmware.Versions
            };
        }
        BatchUpgradeFirmware.TimeoutPeriod = timeout;
        BatchUpgradeFirmware.IsCovered = isOverride == "on";
        BatchUpgradeFirmware.Insert();

        res.success = true;
        res.msg = GetResource("成功下发升级指令，等待升级！");
        return Json(res);
    }

    /// <summary>
    /// 待升级版本号
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [HttpPost]
    [DisplayName("待升级版本号")]
    public IActionResult SearchAllDeviceVersion(int productModuleId, String key)
    {
        var res = new DResult();

        var productIds = Product.FindAllByProductModuleId(productModuleId).Select(e => e.Id);

        var Versions = Data.Device.FindAllByProductId(productIds)
            .Select(e => e.Version.IsNullOrWhiteSpace() ? "unknown" : e.Version)
            .Distinct()
            .OrderBy(v => v);

        if (key.IsNullOrWhiteSpace())
        {
            res.data = Versions.Select(e =>
            {
                var selected = false;

                return new Xmselect<string>
                {
                    name = e,
                    value = e,
                    selected = selected
                };
            });
        }
        else
        {
            res.data = Versions.Where(e => e.IndexOf(key, StringComparison.OrdinalIgnoreCase) >= 0).Select(e =>
            {
                var selected = false;

                return new Xmselect<string>
                {
                    name = e,
                    value = e,
                    selected = selected
                };
            });
        }

        res.success = true;
        return Json(res);
    }

    /// <summary>
    /// 定向升级
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("定向升级")]
    [HttpGet]
    public IActionResult DeviceRange()
    {
        return View();
    }

    /// <summary>
    /// 搜索产品下所有设备
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [HttpPost]
    [DisplayName("搜索设备")]
    public IActionResult SearchAllDevices(string version, string productIds, int productModuleId, string key, Int32 page)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
        };

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);

        if (!productIds.IsNotNullOrWhiteSpace())
        {
            var product = Product.FindAllByProductModuleId(productModuleId);
            productIds = string.Join(",", product.Select(e => e.Id));
        }

        if (!modelRole.IsAdmin)
        {
            res.data = DeviceEx.Search(productIds, key, ManageProvider.User?.ID ?? -1, pages, version).Select(e =>
            {
                var selected = false;

                return new Xmselect<Int32>
                {
                    name = e.Name + $"({e.Code})",
                    value = e.Id,
                    selected = selected,
                    mutex = e.Version,
                };
            });
        }
        else
        {
            res.data = DeviceEx.Search(productIds, key, 0, pages, version).Select(e =>
            {
                var selected = false;

                return new Xmselect<Int32>
                {
                    name = e.Name + $"({e.Code})",
                    value = e.Id,
                    selected = selected,
                    mutex = e.Version,
                };
            });
        }

        res.extdata = pages.TotalCount;
        res.success = true;
        return Json(res);
    }

    /// <summary>
    /// 搜索所有一级行政区
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [HttpPost]
    [DisplayName("搜索所有一级行政区")]
    public IActionResult SearchAllFirstLevel(String key)
    {
        var res = new DResult();
        if (key.IsNullOrWhiteSpace())
        {
            res.data = Regions.FindAllByParentCode(0).Select(e =>
            {
                var selected = false;

                return new Xmselect<Int32>
                {
                    name = e.Name,
                    value = e.Id,
                    selected = selected
                };
            });
        }
        else
        {
            res.data = Regions.FindAllByName(key).Select(e =>
            {
                var selected = false;

                return new Xmselect<Int32>
                {
                    name = e.Name,
                    value = e.Id,
                    selected = selected
                };
            });
        }
        res.success = true;
        return Json(res);
    }

    /// <summary>
    /// 搜索所有二级行政区
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [HttpPost]
    [DisplayName("搜索所有二级行政区")]
    public IActionResult SearchAllSecondLevel(int parentId, string key)
    {
        var res = new DResult();

        if (parentId == 0)//不选省份时，不显示城市
        {
            res.data = new List<Xmselect<Int32>>();
            res.success = true;
            return Json(res);
        }
        else if (parentId < 0)
        {
            res.data = new List<Xmselect<Int32>>();
            res.success = false;
            res.msg = GetResource("父级ID有误");
            return Json(res);
        }

        var parentCode = Regions.FindById(parentId).AreaCode;

        if (key.IsNullOrWhiteSpace())
        {
            res.data = Regions.FindAllByParentCode(parentCode).Select(e =>
            {
                var selected = false;

                return new Xmselect<Int32>
                {
                    name = e.Name,
                    value = e.Id,
                    selected = selected
                };
            });
        }
        else
        {
            res.data = Regions.FindAllByName(key, parentCode).Select(e =>
            {
                var selected = false;

                return new Xmselect<Int32>
                {
                    name = e.Name,
                    value = e.Id,
                    selected = selected
                };
            });
        }
        //}

        //res.extdata = pages.PageCount;
        res.success = true;
        return Json(res);
    }
}
