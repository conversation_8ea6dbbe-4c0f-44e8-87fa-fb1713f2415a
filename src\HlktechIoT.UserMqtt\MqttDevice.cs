﻿using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.MQTT;
using NewLife.MQTT.Messaging;
using NewLife.Security;
using NewLife.Serialization;
using NewLife.Threading;

namespace HlktechIoT.UserMqtt;

/// <summary>MQTT协议用户</summary>
public class MqttDevice {
    #region 属性
    /// <summary>服务器地址</summary>
    public String? Server { get; set; }

    /// <summary>是否进行SSL连接</summary>
    public Boolean UseSSL { get; set; } = false;

    /// <summary>用户惟一码</summary>
    public String? IdentityId { get; set; }

    /// <summary>用户名</summary>
    public String? Name { get; set; }

    /// <summary>用户密码</summary>
    public String? Password { get; set; }

    /// <summary>用户Id</summary>
    public Int32 UId { get; set; }

    /// <summary>密码散列提供者。避免密码明文提交</summary>
    public IPasswordProvider SaltPasswordProvider { get; set; } = new SaltPasswordProvider { Algorithm = "md5+sha512", SaltTime = 60 };

    /// <summary>链路追踪器</summary>
    public ITracer? Tracer { get; set; }

    private MqttClient? _mqttClient;
    private TimerX? _timerPost;
    private TimerX? _timerPing;
    private Int32 _delay;
    #endregion

    /// <summary>
    /// 登录
    /// </summary>
    /// <param name="inf"></param>
    /// <returns></returns>
    public async Task LoginAsync()
    {
        if (Password.IsNullOrWhiteSpace()) throw new Exception("密码不能为空");

        var mi = MachineInfo.GetCurrent();
        var ip = NetHelper.MyIP() + "";
        var client = new MqttClient
        {
            Server = Server,
            ClientId = $"{UId}|{IdentityId}|{ip}|3",
            UserName = $"{Name}|{DateTime.UtcNow.ToLong()}|0",
            //Password = Password,
            Password = Password.MD5(),
            KeepAlive = 30,

            Tracer = Tracer,
            Log = XTrace.Log,

            //UseSSL = UseSSL
        };

#if DEBUG
        client.Log.Level = LogLevel.Debug;
#endif

        client.Received += OnReceived;

        var res = await client.ConnectAsync().ConfigureAwait(false);

        // 订阅感兴趣的主题
        await client.SubscribeAsync(
        [
            $"sys/klIFU64700C/xnAG4ASL00Q/thing/property/post",
            $"sys/user/{IdentityId}/event/set"
        ]).ConfigureAwait(false);

        _mqttClient = client;
    }

    private void OnReceived(Object? sender, EventArgs<PublishMessage> e)
    {
        var topic = e.Arg.Topic;
        var message = e.Arg.Payload?.ToStr();

        _mqttClient?.WriteLog($"收到: topic={topic} message={message}");
    }
}
