<!DOCTYPE html>
      <html>
      <head>
      <title>魔方</title>
      <meta charset="utf-8" />
      <style>@charset "UTF-8";
html,
body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote {
  margin: 0;
  padding: 0;
  font-weight: normal;
  -webkit-font-smoothing: antialiased;
}

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 6px;
}

/* 外层轨道 */
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset006pxrgba(255, 0, 0, 0.3);
  background: rgba(0, 0, 0, 0.1);
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
}

::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(0, 0, 0, 0.2);
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimSun, sans-serif;
  font-size: 13px;
  line-height: 25px;
  color: #393838;
  position: relative;
}

table {
  margin: 10px 0 15px 0;
  border-collapse: collapse;
}

td,
th {
  border: 1px solid #ddd;
  padding: 3px 10px;
}

th {
  padding: 5px 10px;
}

a, a:link, a:visited {
  color: #34495e;
  text-decoration: none;
}

a:hover, a:focus {
  color: #59d69d;
  text-decoration: none;
}

a img {
  border: none;
}

p {
  padding-left: 10px;
  margin-bottom: 9px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #404040;
  line-height: 36px;
}

h1 {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 16px;
  font-size: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ddd;
  line-height: 50px;
}

h2 {
  font-size: 28px;
  padding-top: 10px;
  padding-bottom: 10px;
}

h3 {
  clear: both;
  font-weight: 400;
  margin-top: 20px;
  margin-bottom: 20px;
  border-left: 3px solid #59d69d;
  padding-left: 8px;
  font-size: 18px;
}

h4 {
  font-size: 16px;
}

h5 {
  font-size: 14px;
}

h6 {
  font-size: 13px;
}

hr {
  margin: 0 0 19px;
  border: 0;
  border-bottom: 1px solid #ccc;
}

blockquote {
  padding: 13px 13px 21px 15px;
  margin-bottom: 18px;
  font-family: georgia, serif;
  font-style: italic;
}

blockquote:before {
  font-size: 40px;
  margin-left: -10px;
  font-family: georgia, serif;
  color: #eee;
}

blockquote p {
  font-size: 14px;
  font-weight: 300;
  line-height: 18px;
  margin-bottom: 0;
  font-style: italic;
}

code,
pre {
  font-family: Monaco, Andale Mono, Courier New, monospace;
}

code {
  background-color: #fee9cc;
  color: rgba(0, 0, 0, 0.75);
  padding: 1px 3px;
  font-size: 12px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}

pre {
  display: block;
  padding: 14px;
  margin: 0 0 18px;
  line-height: 16px;
  font-size: 11px;
  border: 1px solid #d9d9d9;
  white-space: pre-wrap;
  word-wrap: break-word;
  background: #f6f6f6;
}

pre code {
  background-color: #f6f6f6;
  color: #737373;
  font-size: 11px;
  padding: 0;
}

sup {
  font-size: 0.83em;
  vertical-align: super;
  line-height: 0;
}

* {
  -webkit-print-color-adjust: exact;
}

@media print {
  body,
  code,
  pre code,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: black;
  }

  table,
  pre {
    page-break-inside: avoid;
  }
}
html,
body {
  height: 100%;
}

.table-of-contents {
  position: fixed;
  top: 61px;
  left: 0;
  bottom: 0;
  overflow-x: hidden;
  overflow-y: auto;
  width: 260px;
}

.table-of-contents > ul > li > a {
  font-size: 20px;
  margin-bottom: 16px;
  margin-top: 16px;
}

.table-of-contents ul {
  overflow: auto;
  margin: 0px;
  height: 100%;
  padding: 0px 0px;
  box-sizing: border-box;
  list-style-type: none;
}

.table-of-contents ul li {
  padding-left: 20px;
}

.table-of-contents a {
  padding: 2px 0px;
  display: block;
  text-decoration: none;
}

.content-right {
  max-width: 700px;
  margin-left: 290px;
  padding-left: 70px;
  flex-grow: 1;
}
.content-right h2:target {
  padding-top: 80px;
}

body > p {
  margin-left: 30px;
}

body > table {
  margin-left: 30px;
}

body > pre {
  margin-left: 30px;
}

.curProject {
  position: fixed;
  top: 20px;
  font-size: 25px;
  color: black;
  margin-left: -240px;
  width: 240px;
  padding: 5px;
  line-height: 25px;
  box-sizing: border-box;
}

.g-doc {
  margin-top: 56px;
  padding-top: 24px;
  display: flex;
}

.curproject-name {
  font-size: 42px;
}

.m-header {
  background: #32363a;
  height: 56px;
  line-height: 56px;
  padding-left: 60px;
  display: flex;
  align-items: center;
  position: fixed;
  z-index: 9;
  top: 0;
  left: 0;
  right: 0;
}
.m-header .title {
  font-size: 22px;
  color: #fff;
  font-weight: normal;
  -webkit-font-smoothing: antialiased;
  margin: 0;
  margin-left: 16px;
  padding: 0;
  line-height: 56px;
  border: none;
}
.m-header .nav {
  color: #fff;
  font-size: 16px;
  position: absolute;
  right: 32px;
  top: 0;
}
.m-header .nav a {
  color: #fff;
  margin-left: 16px;
  padding: 8px;
  transition: color .2s;
}
.m-header .nav a:hover {
  color: #59d69d;
}

.m-footer {
  border-top: 1px solid #ddd;
  padding-top: 16px;
  padding-bottom: 16px;
}

/*# sourceMappingURL=defaultTheme.css.map */
</style>
      </head>
      <body>
        <div class="m-header">
          <a href="#" style="display: inherit;"><svg class="svg" width="32px" height="32px" viewBox="0 0 64 64" version="1.1"><title>Icon</title><desc>Created with Sketch.</desc><defs><linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1"><stop stop-color="#FFFFFF" offset="0%"></stop><stop stop-color="#F2F2F2" offset="100%"></stop></linearGradient><circle id="path-2" cx="31.9988602" cy="31.9988602" r="2.92886048"></circle><filter x="-85.4%" y="-68.3%" width="270.7%" height="270.7%" filterUnits="objectBoundingBox" id="filter-3"><feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset><feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur><feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.159703351 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix></filter></defs><g id="首页" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="大屏幕"><g id="Icon"><circle id="Oval-1" fill="url(#linearGradient-1)" cx="32" cy="32" r="32"></circle><path d="M36.7078009,31.8054514 L36.7078009,51.7110548 C36.7078009,54.2844537 34.6258634,56.3695395 32.0579205,56.3695395 C29.4899777,56.3695395 27.4099998,54.0704461 27.4099998,51.7941246 L27.4099998,31.8061972 C27.4099998,29.528395 29.4909575,27.218453 32.0589004,27.230043 C34.6268432,27.241633 36.7078009,29.528395 36.7078009,31.8054514 Z" id="blue" fill="#2359F1" fill-rule="nonzero"></path><path d="M45.2586091,17.1026914 C45.2586091,17.1026914 45.5657231,34.0524383 45.2345291,37.01141 C44.9033351,39.9703817 43.1767091,41.6667796 40.6088126,41.6667796 C38.040916,41.6667796 35.9609757,39.3676862 35.9609757,37.0913646 L35.9609757,17.1034372 C35.9609757,14.825635 38.0418959,12.515693 40.6097924,12.527283 C43.177689,12.538873 45.2586091,14.825635 45.2586091,17.1026914 Z" id="green" fill="#57CF27" fill-rule="nonzero" transform="translate(40.674608, 27.097010) rotate(60.000000) translate(-40.674608, -27.097010) "></path><path d="M28.0410158,17.0465598 L28.0410158,36.9521632 C28.0410158,39.525562 25.9591158,41.6106479 23.3912193,41.6106479 C20.8233227,41.6106479 18.7433824,39.3115545 18.7433824,37.035233 L18.7433824,17.0473055 C18.7433824,14.7695034 20.8243026,12.4595614 23.3921991,12.4711513 C25.9600956,12.4827413 28.0410158,14.7695034 28.0410158,17.0465598 Z" id="red" fill="#FF561B" fill-rule="nonzero" transform="translate(23.392199, 27.040878) rotate(-60.000000) translate(-23.392199, -27.040878) "></path><g id="inner-round"><use fill="black" fill-opacity="1" filter="url(#filter-3)" xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#path-2"></use><use fill="#F7F7F7" fill-rule="evenodd" xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#path-2"></use></g></g></g></g></svg></a>
          <a href="#"><h1 class="title">YAPI 接口文档</h1></a>
          <div class="nav">
            <a href="https://hellosean1025.github.io/yapi/">YApi</a>
          </div>
        </div>
        <div class="g-doc">
          <div class="table-of-contents"><ul><li><a href="#sso">Sso</a><ul><li><a href="#1uff0cu9a8cu8bc1u7528u6237u8eabu4efd0a3ca20id3d1uff0cu9a8cu8bc1u7528u6237u8eabu4efd713e203ca3e">1，验证用户身份
<a id=1，验证用户身份71> </a></a></li><li><a href="#2uff0cu7528u6237u767bu5f55u6210u529fu540eu8fd4u56deu8fd9u91cc0a3ca20id3d2uff0cu7528u6237u767bu5f55u6210u529fu540eu8fd4u56deu8fd9u91cc713e203ca3e">2，用户登录成功后返回这里
<a id=2，用户登录成功后返回这里71> </a></a></li><li><a href="#3uff0cu6839u636ecodeu83b7u53d6u4ee4u724c0a3ca20id3d3uff0cu6839u636ecodeu83b7u53d6u4ee4u724c713e203ca3e">3，根据code获取令牌
<a id=3，根据code获取令牌71> </a></a></li><li><a href="#3uff0cu6839u636ecodeu83b7u53d6u4ee4u724c0a3ca20id3d3uff0cu6839u636ecodeu83b7u53d6u4ee4u724c713e203ca3e">3，根据code获取令牌
<a id=3，根据code获取令牌71> </a></a></li><li><a href="#3uff0cu6839u636epasswordclient-credentialsu83b7u53d6u4ee4u724c0a3ca20id3d3uff0cu6839u636epasswordclient-credentialsu83b7u53d6u4ee4u724c713e203ca3e">3，根据password/client_credentials获取令牌
<a id=3，根据password/client_credentials获取令牌71> </a></a></li><li><a href="#3uff0cu6839u636epasswordclient-credentialsu83b7u53d6u4ee4u724c0a3ca20id3d3uff0cu6839u636epasswordclient-credentialsu83b7u53d6u4ee4u724c713e203ca3e">3，根据password/client_credentials获取令牌
<a id=3，根据password/client_credentials获取令牌71> </a></a></li><li><a href="#4uff0cu6839u636etokenu83b7u53d6u7528u6237u4fe1u606f0a3ca20id3d4uff0cu6839u636etokenu83b7u53d6u7528u6237u4fe1u606f713e203ca3e">4，根据token获取用户信息
<a id=4，根据token获取用户信息71> </a></a></li><li><a href="#5uff0cu5237u65b0u4ee4u724c0a3ca20id3d5uff0cu5237u65b0u4ee4u724c713e203ca3e">5，刷新令牌
<a id=5，刷新令牌71> </a></a></li><li><a href="#5uff0cu5237u65b0u4ee4u724c0a3ca20id3d5uff0cu5237u65b0u4ee4u724c713e203ca3e">5，刷新令牌
<a id=5，刷新令牌71> </a></a></li><li><a href="#u6ce8u9500u767bu5f550a3ca20id3du6ce8u9500u767bu5f55713e203ca3e">注销登录
<a id=注销登录71> </a></a></li><li><a href="#u7528u6237u9a8cu8bc1u3002u501fu52a9oauthu5bc6u7801u5f0fu9a8cu8bc1uff0cu5e76u8fd4u56deu7528u6237u4fe1u606f0a3ca20id3du7528u6237u9a8cu8bc1u3002u501fu52a9oauthu5bc6u7801u5f0fu9a8cu8bc1uff0cu5e76u8fd4u56deu7528u6237u4fe1u606f713e203ca3e">用户验证。借助OAuth密码式验证，并返回用户信息
<a id=用户验证。借助OAuth密码式验证，并返回用户信息71> </a></a></li><li><a href="#u7b2cu4e09u65b9u767bu5f550a3ca20id3du7b2cu4e09u65b9u767bu5f55713e203ca3e">第三方登录
<a id=第三方登录71> </a></a></li><li><a href="#u83b7u53d6u5e94u7528u516cu94a5uff0cu7528u4e8eu9a8cu8bc1u4ee4u724c0a3ca20id3du83b7u53d6u5e94u7528u516cu94a5uff0cu7528u4e8eu9a8cu8bc1u4ee4u724c713e203ca3e">获取应用公钥，用于验证令牌
<a id=获取应用公钥，用于验证令牌71> </a></a></li><li><a href="#u83b7u53d6u7528u6237u5934u50cf0a3ca20id3du83b7u53d6u7528u6237u5934u50cf713e203ca3e">获取用户头像
<a id=获取用户头像71> </a></a></li><li><a href="#u9a8cu8bc1u4ee4u724cu662fu5426u6709u65480a3ca20id3du9a8cu8bc1u4ee4u724cu662fu5426u6709u6548713e203ca3e">验证令牌是否有效
<a id=验证令牌是否有效71> </a></a></li><li><a href="#u9a8cu8bc1u4ee4u724cu662fu5426u6709u65480a3ca20id3du9a8cu8bc1u4ee4u724cu662fu5426u6709u6548713e203ca3e">验证令牌是否有效
<a id=验证令牌是否有效71> </a></a></li><li><a href="#u9a8cu8bc1u4ee4u724cuff0cu56deu5199cookie0a3ca20id3du9a8cu8bc1u4ee4u724cuff0cu56deu5199cookie713e203ca3e">验证令牌，回写cookie
<a id=验证令牌，回写cookie71> </a></a></li></ul></li></ul></div>
          <div id="right" class="content-right">
           <h1 class="curproject-name"> 魔方 </h1> 
<h1 id="sso">Sso</h1>
<p></p>
<h2 id="1uff0cu9a8cu8bc1u7528u6237u8eabu4efd0a3ca20id3d1uff0cu9a8cu8bc1u7528u6237u8eabu4efd713e203ca3e">1，验证用户身份
<a id=1，验证用户身份71> </a></h2>
<p></p>
<h3 id="">基本信息</h3>
<p><strong>Path：</strong> /Sso/Authorize</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
子系统需要验证访问者身份时，引导用户跳转到这里。<br>
用户登录完成后，得到一个独一无二的code，并跳转回去子系统。</p>
<h3 id="-2">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>client_id</td>
<td>否</td>
<td></td>
<td>应用标识</td>
</tr>
<tr>
<td>redirect_uri</td>
<td>否</td>
<td></td>
<td>回调地址</td>
</tr>
<tr>
<td>response_type</td>
<td>否</td>
<td></td>
<td>响应类型。默认code</td>
</tr>
<tr>
<td>scope</td>
<td>否</td>
<td></td>
<td>授权域</td>
</tr>
<tr>
<td>state</td>
<td>否</td>
<td></td>
<td>用户状态数据</td>
</tr>
<tr>
<td>loginUrl</td>
<td>否</td>
<td></td>
<td>登录页。子系统请求SSO时，如果在SSO未登录则直接跳转的地址，该地址有可能属于子系统自身，适用于password模式登录等场景</td>
</tr>
</tbody>
</table>
<h3 id="-3">返回数据</h3>
<pre><code class="language-javascript">Success
</code></pre>
<h2 id="2uff0cu7528u6237u767bu5f55u6210u529fu540eu8fd4u56deu8fd9u91cc0a3ca20id3d2uff0cu7528u6237u767bu5f55u6210u529fu540eu8fd4u56deu8fd9u91cc713e203ca3e">2，用户登录成功后返回这里
<a id=2，用户登录成功后返回这里71> </a></h2>
<p></p>
<h3 id="-4">基本信息</h3>
<p><strong>Path：</strong> /Sso/Auth2</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
构建身份验证结构，返回code给子系统</p>
<h3 id="-5">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>id</td>
<td>否</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<h3 id="-6">返回数据</h3>
<pre><code class="language-javascript">Success
</code></pre>
<h2 id="3uff0cu6839u636ecodeu83b7u53d6u4ee4u724c0a3ca20id3d3uff0cu6839u636ecodeu83b7u53d6u4ee4u724c713e203ca3e">3，根据code获取令牌
<a id=3，根据code获取令牌71> </a></h2>
<p></p>
<h3 id="-7">基本信息</h3>
<p><strong>Path：</strong> /Sso/Access_Token</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
子系统根据验证用户身份时得到的code，直接在服务器间请求本系统。<br>
传递应用标识和密钥，主要是为了向本系统表明其合法身份。</p>
<h3 id="-8">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>client_id</td>
<td>否</td>
<td></td>
<td>应用标识</td>
</tr>
<tr>
<td>client_secret</td>
<td>否</td>
<td></td>
<td>密钥</td>
</tr>
<tr>
<td>code</td>
<td>否</td>
<td></td>
<td>代码</td>
</tr>
<tr>
<td>grant_type</td>
<td>否</td>
<td></td>
<td>授权类型</td>
</tr>
</tbody>
</table>
<h3 id="-9">返回数据</h3>
<pre><code class="language-javascript">Success
</code></pre>
<h2 id="3uff0cu6839u636ecodeu83b7u53d6u4ee4u724c0a3ca20id3d3uff0cu6839u636ecodeu83b7u53d6u4ee4u724c713e203ca3e-2">3，根据code获取令牌
<a id=3，根据code获取令牌71> </a></h2>
<p></p>
<h3 id="-10">基本信息</h3>
<p><strong>Path：</strong> /Sso/Access_Token</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
子系统根据验证用户身份时得到的code，直接在服务器间请求本系统。<br>
传递应用标识和密钥，主要是为了向本系统表明其合法身份。</p>
<h3 id="-11">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>client_id</td>
<td>否</td>
<td></td>
<td>应用标识</td>
</tr>
<tr>
<td>client_secret</td>
<td>否</td>
<td></td>
<td>密钥</td>
</tr>
<tr>
<td>code</td>
<td>否</td>
<td></td>
<td>代码</td>
</tr>
<tr>
<td>grant_type</td>
<td>否</td>
<td></td>
<td>授权类型</td>
</tr>
</tbody>
</table>
<h3 id="-12">返回数据</h3>
<pre><code class="language-javascript">Success
</code></pre>
<h2 id="3uff0cu6839u636epasswordclient-credentialsu83b7u53d6u4ee4u724c0a3ca20id3d3uff0cu6839u636epasswordclient-credentialsu83b7u53d6u4ee4u724c713e203ca3e">3，根据password/client_credentials获取令牌
<a id=3，根据password/client_credentials获取令牌71> </a></h2>
<p></p>
<h3 id="-13">基本信息</h3>
<p><strong>Path：</strong> /Sso/Token</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
密码式：<br>
用户把用户名和密码，直接告诉该应用。该应用就使用你的密码，申请令牌，这种方式称为&quot;密码式&quot;（password）。<br>
为了避免密码暴露在Url中，需要用表单Post方式提交。<br>
凭证式：<br>
凭证式（client credentials），适用于没有前端的命令行应用，即在命令行下请求令牌。<br>
针对第三方应用，而不是针对用户的，即有可能多个用户共享同一个令牌。</p>
<h3 id="-14">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>client_id</td>
<td>否</td>
<td></td>
<td>应用标识</td>
</tr>
<tr>
<td>client_secret</td>
<td>否</td>
<td></td>
<td>密钥</td>
</tr>
<tr>
<td>username</td>
<td>否</td>
<td></td>
<td>用户名。可以是设备编码等唯一使用者标识</td>
</tr>
<tr>
<td>password</td>
<td>否</td>
<td></td>
<td>密码</td>
</tr>
<tr>
<td>refresh_token</td>
<td>否</td>
<td></td>
<td>刷新令牌</td>
</tr>
<tr>
<td>grant_type</td>
<td>否</td>
<td></td>
<td>授权类型</td>
</tr>
</tbody>
</table>
<h3 id="-15">返回数据</h3>
<pre><code class="language-javascript">Success
</code></pre>
<h2 id="3uff0cu6839u636epasswordclient-credentialsu83b7u53d6u4ee4u724c0a3ca20id3d3uff0cu6839u636epasswordclient-credentialsu83b7u53d6u4ee4u724c713e203ca3e-2">3，根据password/client_credentials获取令牌
<a id=3，根据password/client_credentials获取令牌71> </a></h2>
<p></p>
<h3 id="-16">基本信息</h3>
<p><strong>Path：</strong> /Sso/PasswordToken</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
密码式：<br>
用户把用户名和密码，直接告诉该应用。该应用就使用你的密码，申请令牌，这种方式称为&quot;密码式&quot;（password）。<br>
凭证式：<br>
凭证式（client credentials），适用于没有前端的命令行应用，即在命令行下请求令牌。<br>
针对第三方应用，而不是针对用户的，即有可能多个用户共享同一个令牌。</p>
<h3 id="-17">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> client_id</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">应用标识</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> client_secret</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">应用密钥</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> userName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">用户名。可以是设备编码等唯一使用者标识</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> password</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">密码</span></td><td key=5></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> grant_type</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">授权类型</span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="-18">返回数据</h3>
<pre><code class="language-javascript">Success
</code></pre>
<h2 id="4uff0cu6839u636etokenu83b7u53d6u7528u6237u4fe1u606f0a3ca20id3d4uff0cu6839u636etokenu83b7u53d6u7528u6237u4fe1u606f713e203ca3e">4，根据token获取用户信息
<a id=4，根据token获取用户信息71> </a></h2>
<p></p>
<h3 id="-19">基本信息</h3>
<p><strong>Path：</strong> /Sso/UserInfo</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-20">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>access_token</td>
<td>否</td>
<td></td>
<td>访问令牌</td>
</tr>
</tbody>
</table>
<h3 id="-21">返回数据</h3>
<pre><code class="language-javascript">Success
</code></pre>
<h2 id="5uff0cu5237u65b0u4ee4u724c0a3ca20id3d5uff0cu5237u65b0u4ee4u724c713e203ca3e">5，刷新令牌
<a id=5，刷新令牌71> </a></h2>
<p></p>
<h3 id="-22">基本信息</h3>
<p><strong>Path：</strong> /Sso/Refresh_Token</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
若access_token已超时，那么进行refresh_token会获取一个新的access_token，新的超时时间；<br>
若access_token未超时，那么进行refresh_token不会改变access_token，但超时时间会刷新，相当于续期access_token。</p>
<h3 id="-23">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>client_id</td>
<td>否</td>
<td></td>
<td>应用标识</td>
</tr>
<tr>
<td>grant_type</td>
<td>否</td>
<td></td>
<td>授权类型</td>
</tr>
<tr>
<td>refresh_token</td>
<td>否</td>
<td></td>
<td>刷新令牌</td>
</tr>
</tbody>
</table>
<h3 id="-24">返回数据</h3>
<pre><code class="language-javascript">Success
</code></pre>
<h2 id="5uff0cu5237u65b0u4ee4u724c0a3ca20id3d5uff0cu5237u65b0u4ee4u724c713e203ca3e-2">5，刷新令牌
<a id=5，刷新令牌71> </a></h2>
<p></p>
<h3 id="-25">基本信息</h3>
<p><strong>Path：</strong> /Sso/Refresh_Token</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
若access_token已超时，那么进行refresh_token会获取一个新的access_token，新的超时时间；<br>
若access_token未超时，那么进行refresh_token不会改变access_token，但超时时间会刷新，相当于续期access_token。</p>
<h3 id="-26">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>client_id</td>
<td>否</td>
<td></td>
<td>应用标识</td>
</tr>
<tr>
<td>grant_type</td>
<td>否</td>
<td></td>
<td>授权类型</td>
</tr>
<tr>
<td>refresh_token</td>
<td>否</td>
<td></td>
<td>刷新令牌</td>
</tr>
</tbody>
</table>
<h3 id="-27">返回数据</h3>
<pre><code class="language-javascript">Success
</code></pre>
<h2 id="u6ce8u9500u767bu5f550a3ca20id3du6ce8u9500u767bu5f55713e203ca3e">注销登录
<a id=注销登录71> </a></h2>
<p></p>
<h3 id="-28">基本信息</h3>
<p><strong>Path：</strong> /Sso/Logout</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
子系统引导用户跳转到这里注销登录。</p>
<h3 id="-29">请求参数</h3>
<h3 id="-30">返回数据</h3>
<pre><code class="language-javascript">Success
</code></pre>
<h2 id="u7528u6237u9a8cu8bc1u3002u501fu52a9oauthu5bc6u7801u5f0fu9a8cu8bc1uff0cu5e76u8fd4u56deu7528u6237u4fe1u606f0a3ca20id3du7528u6237u9a8cu8bc1u3002u501fu52a9oauthu5bc6u7801u5f0fu9a8cu8bc1uff0cu5e76u8fd4u56deu7528u6237u4fe1u606f713e203ca3e">用户验证。借助OAuth密码式验证，并返回用户信息
<a id=用户验证。借助OAuth密码式验证，并返回用户信息71> </a></h2>
<p></p>
<h3 id="-31">基本信息</h3>
<p><strong>Path：</strong> /Sso/UserAuth</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-32">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> client_id</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">应用标识</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> client_secret</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">应用密钥</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> userName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">用户名。可以是设备编码等唯一使用者标识</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> password</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">密码</span></td><td key=5></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> grant_type</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">授权类型</span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="-33">返回数据</h3>
<pre><code class="language-javascript">Success
</code></pre>
<h2 id="u7b2cu4e09u65b9u767bu5f550a3ca20id3du7b2cu4e09u65b9u767bu5f55713e203ca3e">第三方登录
<a id=第三方登录71> </a></h2>
<p></p>
<h3 id="-34">基本信息</h3>
<p><strong>Path：</strong> /Sso/Login</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-35">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>name</td>
<td>否</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<h3 id="-36">返回数据</h3>
<pre><code class="language-javascript">Success
</code></pre>
<h2 id="u83b7u53d6u5e94u7528u516cu94a5uff0cu7528u4e8eu9a8cu8bc1u4ee4u724c0a3ca20id3du83b7u53d6u5e94u7528u516cu94a5uff0cu7528u4e8eu9a8cu8bc1u4ee4u724c713e203ca3e">获取应用公钥，用于验证令牌
<a id=获取应用公钥，用于验证令牌71> </a></h2>
<p></p>
<h3 id="-37">基本信息</h3>
<p><strong>Path：</strong> /Sso/GetKey</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-38">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>client_id</td>
<td>否</td>
<td></td>
<td>应用</td>
</tr>
<tr>
<td>client_secret</td>
<td>否</td>
<td></td>
<td>密钥</td>
</tr>
</tbody>
</table>
<h3 id="-39">返回数据</h3>
<pre><code class="language-javascript">Success
</code></pre>
<h2 id="u83b7u53d6u7528u6237u5934u50cf0a3ca20id3du83b7u53d6u7528u6237u5934u50cf713e203ca3e">获取用户头像
<a id=获取用户头像71> </a></h2>
<p></p>
<h3 id="-40">基本信息</h3>
<p><strong>Path：</strong> /Sso/Avatar</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-41">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>id</td>
<td>否</td>
<td></td>
<td>用户编号</td>
</tr>
</tbody>
</table>
<h3 id="-42">返回数据</h3>
<pre><code class="language-javascript">Success
</code></pre>
<h2 id="u9a8cu8bc1u4ee4u724cu662fu5426u6709u65480a3ca20id3du9a8cu8bc1u4ee4u724cu662fu5426u6709u6548713e203ca3e">验证令牌是否有效
<a id=验证令牌是否有效71> </a></h2>
<p></p>
<h3 id="-43">基本信息</h3>
<p><strong>Path：</strong> /Sso/Auth</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-44">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>access_token</td>
<td>否</td>
<td></td>
<td>应用</td>
</tr>
</tbody>
</table>
<h3 id="-45">返回数据</h3>
<pre><code class="language-javascript">Success
</code></pre>
<h2 id="u9a8cu8bc1u4ee4u724cu662fu5426u6709u65480a3ca20id3du9a8cu8bc1u4ee4u724cu662fu5426u6709u6548713e203ca3e-2">验证令牌是否有效
<a id=验证令牌是否有效71> </a></h2>
<p></p>
<h3 id="-46">基本信息</h3>
<p><strong>Path：</strong> /Sso/Auth</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong></p>
<h3 id="-47">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>access_token</td>
<td>否</td>
<td></td>
<td>应用</td>
</tr>
</tbody>
</table>
<h3 id="-48">返回数据</h3>
<pre><code class="language-javascript">Success
</code></pre>
<h2 id="u9a8cu8bc1u4ee4u724cuff0cu56deu5199cookie0a3ca20id3du9a8cu8bc1u4ee4u724cuff0cu56deu5199cookie713e203ca3e">验证令牌，回写cookie
<a id=验证令牌，回写cookie71> </a></h2>
<p></p>
<h3 id="-49">基本信息</h3>
<p><strong>Path：</strong> /Sso/Verify</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-50">请求参数</h3>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>access_token</td>
<td>否</td>
<td></td>
<td>应用</td>
</tr>
<tr>
<td>redirect_uri</td>
<td>否</td>
<td></td>
<td>回调地址</td>
</tr>
</tbody>
</table>
<h3 id="-51">返回数据</h3>
<pre><code class="language-javascript">Success
</code></pre>

            <footer class="m-footer">
              <p>Build by <a href="https://ymfe.org/">YMFE</a>.</p>
            </footer>
          </div>
        </div>
      </body>
      </html>
      