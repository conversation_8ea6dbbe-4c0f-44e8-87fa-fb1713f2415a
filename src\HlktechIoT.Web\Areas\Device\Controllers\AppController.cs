﻿using DG.Web.Framework;

using DH.Core.Infrastructure;
using DH.Entity;

using HlktechIoT.Data;
using HlktechIoT.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Caching;
using NewLife.Data;
using NewLife.Serialization;

using Pek;
using Pek.Helpers;
using Pek.Models;

using System.ComponentModel;

using XCode.Membership;

using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Device.Controllers;

/// <summary>
/// APP列表
/// </summary>
[DisplayName("APP管理")]
[Description("APP管理")]
[DeviceArea]
[DHMenu(25, ParentMenuName = "DeviceManager", CurrentMenuUrl = "~/{area}/App", CurrentMenuName = "AppList", LastUpdate = "20240716")]
public class AppController : BaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 25;

    /// <summary>
    /// APP列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("APP列表")]
    public IActionResult Index(String PId)
    {
        ViewBag.PId = PId;
        return View();
    }

    /// <summary>APP列表</summary>
    /// <returns></returns>
    [DisplayName("APP列表")]
    [EntityAuthorize((PermissionFlags)16)]
    public IActionResult AppList(String key, String PId, Int32 page = 1, Int32 limit = 10)
    {
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = ProjectApp._.Id,
            Desc = true,
        };

        var UId = 0;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            UId = ManageProvider.User?.ID ?? -1;
        }

        var list = ProjectApp.Search(UId, PId, key, pages);

        var data = list.Select(x => new { x.Id, x.AndroidAppKey, x.IosAppKey, x.AndroidPackName, x.IosPackName, x.JPushKey, x.JPushSecret, x.URIAction, x.ApnsProduction, x.CreateTime, x.Name, x.ProjectName, x.ProjectId, x.OtherPId, x.OtherProductKey, x.ProductKey,x.LimitMultiLogin,x.MiniProgramAppKey });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 搜索项目
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [HttpPost]
    [DisplayName("搜索项目")]
    public IActionResult SearchProject(String keyword, Int32 PId, Int32 page)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = Project._.Id,
            Desc = true,
        };

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (modelRole.IsAdmin)
        {
            res.data = Project.Search(0, keyword, pages).Select(e =>
            {
                var selected = false;
                if (e.Id == PId)
                {
                    selected = true;
                }
                return new Xmselect<Int32>
                {
                    name = e.Name,
                    value = e.Id,
                    selected = selected
                };
            });
        }
        else
        {
            res.data = Project.Search(ManageProvider.User?.ID ?? -1, keyword, null).Select(e =>
            {
                var selected = false;
                if (e.Id == PId)
                {
                    selected = true;
                }

                return new Xmselect<Int32>
                {
                    name = e.Name,
                    value = e.Id,
                    selected = selected
                };
            });
        }

        var model = Project.FindById(PId);
        if (model != null)
        {
            res.extdata = new { pages.PageCount, data = new List<NameValueL<Int32?>>() { new() { name = model.Name, value = model.Id } } };
        }
        else
        {
            res.extdata = new { pages.PageCount, data = new List<NameValueL<Int32?>>() };
        }

        //res.extdata = pages.PageCount;
        res.success = true;
        return Json(res);
    }

    /// <summary>
    /// 搜索项目
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [HttpPost]
    [DisplayName("搜索项目")]
    public IActionResult SearchInProject(String keyword, Int32 PId, Int32 page)
    {

        var res = new DResult();
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (modelRole.IsAdmin)
        {
            res.data = ProjectEX.SearchIn(0, PId).Select(e =>
            {
                var selected = true;
                return new Xmselect<Int32>
                {
                    name = e.Name,
                    value = e.Id,
                    selected = selected
                };
            });
        }
        else
        {
            res.data = ProjectEX.SearchIn(ManageProvider.User?.ID ?? -1, PId).Select(e =>
            {
                var selected = true;
                return new Xmselect<Int32>
                {
                    name = e.Name,
                    value = e.Id,
                    selected = selected
                };
            });
        }

        res.success = true;
        return Json(res);
    }

    /// <summary>
    /// 增加APP
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("增加APP")]
    public IActionResult Add()
    {
        return View();
    }

    /// <summary>搜索产品</summary>
    /// <returns></returns>
    [DisplayName("搜索产品")]
    [EntityAuthorize((PermissionFlags)16)]
    public IActionResult SearchProduct(Int32 Id, String keyword, String PId, Int32 page)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = Product._.Id,
            Desc = true,
        };

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (modelRole.IsAdmin)
        {
            res.data = Product.Search(Id, 0, keyword, pages).Select(e =>
            {
                var selected = false;
                if (PId.SplitAsInt().Contains(e.Id))
                {
                    selected = true;
                }
                return new Xmselect<Int32>
                {
                    name = e.Name,
                    value = e.Id,
                    selected = selected
                };
            });
        }
        else
        {
            res.data = Product.Search(Id, ManageProvider.User?.ID ?? -1, keyword, pages).Select(e =>
            {
                var selected = false;
                if (PId.SplitAsInt().Contains(e.Id))
                {
                    selected = true;
                }

                return new Xmselect<Int32>
                {
                    name = e.Name,
                    value = e.Id,
                    selected = selected
                };
            });
        }

        res.extdata = pages.PageCount;
        res.success = true;
        return Json(res);
    }

    /// <summary>搜索产品</summary>
    /// <returns></returns>
    [DisplayName("搜索产品")]
    [EntityAuthorize((PermissionFlags)16)]
    public IActionResult SearchOrProduct(String keyword, String PId,Int32 ProjectId, Int32 page)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = Product._.Id,
            Desc = true,
        };

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (modelRole.IsAdmin)
        {
            res.data = ProductEX.SearchPro(ProjectId, 0, keyword, pages).Select(e =>
            {
                var disabled = false;
                var selected = false;
                if (PId.SplitAsInt().Contains(e.Id))
                {
                    selected = true;
                }
                return new Xmselect<Int32>
                {
                    name = e.Name,
                    value = e.Id,
                    disabled = disabled,
                    selected = selected
                };
            });
        }
        else
        {
            res.data = ProductEX.SearchPro(ProjectId, ManageProvider.User?.ID ?? -1, keyword, pages).Select(e =>
            {
                var disabled = false;
                var selected = false;
                if (PId.SplitAsInt().Contains(e.Id))
                {
                    selected = true;
                }
                return new Xmselect<Int32>
                {
                    name = e.Name,
                    value = e.Id,
                    disabled = disabled,
                    selected = selected
                };
            });
        }

        res.extdata = pages.PageCount;
        res.success = true;
        return Json(res);
    }


    /// <summary>新增APP</summary>
    /// <returns></returns>
    [DisplayName("新增APP")]
    [EntityAuthorize((PermissionFlags)16)]
    [HttpPost]
    public IActionResult Add(Int32 Id, String Name, Int32 DId, String PId, String OtherPId, String AndroidPackName, String IosPackName, String JPushKey, String JPushSecret, String URIAction, String ApnsProduction,String LimitMultiLogin)
    {
        var result = new DResult();

        if (Name.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("名称不能为空");
            return Json(result);
        }

        if (PId.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("请选择产品");
            return Json(result);
        }
        if (DId <= 0)
        {
            result.msg = GetResource("请选择项目");
            return Json(result);
        }
        if (!AndroidPackName.IsNullOrWhiteSpace())
        {
            var modelProjectApp1 = ProjectApp.FindByProjectIdAndAndroidPackName(Id, AndroidPackName);
            if (modelProjectApp1 != null)
            {
                result.msg = GetResource("Android应用包名不能重复");
                return Json(result);
            }
        }
        if (!IosPackName.IsNullOrWhiteSpace())
        {
            var modelProjectApp1 = ProjectApp.FindByProjectIdAndIosPackName(Id, IosPackName);
            if (modelProjectApp1 != null)
            {
                result.msg = GetResource("iOS应用包名不能重复");
                return Json(result);
            }
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);

        var list = Product.FindAllByIds(PId.SplitAsInt(","));
        if (!list.Any())
        {
            result.msg = GetResource("产品不存在");
            return Json(result);
        }
        var list2 = Project.FindById(DId);

        var cacheProvider = EngineContext.Current.Resolve<ICacheProvider>();

        var modelProjectApp = new ProjectApp();
        modelProjectApp.ProjectId = Id;
        modelProjectApp.Name = Name;
        modelProjectApp.PId = $",{PId},";
        modelProjectApp.OtherPId = $",{OtherPId},";
        modelProjectApp.ProjectId = DId;
        modelProjectApp.ProductKey = $",{list.Select(e => e.Code).Join()},";
        modelProjectApp.OtherProductKey = $",{list.Select(e => e.Code).Join()},";
        modelProjectApp.ProjectKey = list2.ProjectKey;
        modelProjectApp.AndroidAppKey = modelProjectApp.CreateProjectKey(cacheProvider);
        modelProjectApp.AndroidAppSecret = Randoms.RandomString(32);
        modelProjectApp.IosAppKey = modelProjectApp.CreateProjectKey(cacheProvider);
        modelProjectApp.IosAppSecret = Randoms.RandomString(32);
        modelProjectApp.MiniProgramAppKey = modelProjectApp.CreateProjectKey(cacheProvider);
        modelProjectApp.MiniProgramAppSecret = Randoms.RandomString(32);
        modelProjectApp.AndroidPackName = AndroidPackName;
        modelProjectApp.IosPackName = IosPackName;
        modelProjectApp.JPushKey = JPushKey;
        modelProjectApp.JPushSecret = JPushSecret;
        modelProjectApp.URIAction = URIAction;
        modelProjectApp.ApnsProduction = ApnsProduction.SafeString() == "on";
        modelProjectApp.LimitMultiLogin = LimitMultiLogin.SafeString() == "on";
        modelProjectApp.Insert();

        result.success = true;
        result.msg = GetResource("APP新增成功");

        return Json(result);
    }

    /// <summary>编辑APP</summary>
    /// <returns></returns>
    [DisplayName("编辑APP")]
    [EntityAuthorize((PermissionFlags)16)]
    public IActionResult Edit(Int32 Id)
    {
        var model = ProjectApp.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("APP不存在"));
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                return Content(GetResource("您没有权限操作"));
            }
        }

        var res = new DResult();
        var modelProduct = Product.FindAllByIds(model.PId.SplitAsInt(","));
        res.data = modelProduct.Select(e =>
        {
            return new Xmselect<Int32>
            {
                name = e.Name,
                value = e.Id,
            };
        });
        ViewBag.ProductList = res.data.ToJson();

        var res2 = new DResult();
        var modelProduct2 = Product.FindAllByIds(model.OtherPId.SplitAsInt(","));
        res2.data = modelProduct2.Select(e =>
        {
            return new Xmselect<Int32>
            {
                name = e.Name,
                value = e.Id,
            };
        });
        ViewBag.ProductList2 =res2.data.ToJson();
        return View(model);
    }

    /// <summary>编辑APP</summary>
    /// <returns></returns>
    [DisplayName("编辑APP")]
    [EntityAuthorize((PermissionFlags)16)]
    [HttpPost]
    public IActionResult Edit(Int32 Id, Int32 DId, String Name, String PId, String OtherPId, String AndroidPackName, String IosPackName, String JPushKey, String JPushSecret, String URIAction, String ApnsProduction,String LimitMultiLogin)
    {
        var result = new DResult();

        if (Name.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("名称不能为空");
            return Json(result);
        }

        if (Id <= 0)
        {
            result.msg = GetResource("APP不存在");
            return Json(result);
        }

        if (PId.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("请选择产品");
            return Json(result);
        }

        var model = ProjectApp.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("APP不存在");
            return Json(result);
        }

        if (!AndroidPackName.IsNullOrWhiteSpace())
        {
            var modelProjectApp = ProjectApp.FindByProjectIdAndAndroidPackName(model.ProjectId, AndroidPackName);
            if (modelProjectApp != null && modelProjectApp.Id != Id)
            {
                result.msg = GetResource("Android应用包名不能重复");
                return Json(result);
            }
        }
        if (!IosPackName.IsNullOrWhiteSpace())
        {
            var modelProjectApp = ProjectApp.FindByProjectIdAndIosPackName(model.ProjectId, IosPackName);
            if (modelProjectApp != null && modelProjectApp.Id != Id)
            {
                result.msg = GetResource("iOS应用包名不能重复");
                return Json(result);
            }
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                result.msg = GetResource("您没有权限操作");
                return Json(result);
            }
        }

        var list = Product.FindAllByIds(PId.SplitAsInt(","));
        var list2 = Product.FindAllByIds(OtherPId.SplitAsInt(","));
        if (!list.Any())
        {
            result.msg = GetResource("产品不存在");
            return Json(result);
        }

        var cacheProvider = EngineContext.Current.Resolve<ICacheProvider>();

        model.Name = Name;
        model.PId = $"{PId}";
        model.OtherPId = $",{OtherPId},";
        model.ProjectId = DId;
        model.ProductKey = $",{list.Select(e => e.Code).Join()},";
        model.OtherProductKey = $",{list2.Select(e => e.Code).Join()},";
        model.AndroidPackName = AndroidPackName;
        model.IosPackName = IosPackName;
        model.JPushKey = JPushKey;
        model.JPushSecret = JPushSecret;
        model.URIAction = URIAction;
        model.ApnsProduction = ApnsProduction.SafeString() == "on";
        model.LimitMultiLogin = LimitMultiLogin.SafeString() == "on";
        model.Update();

        result.success = true;
        result.msg = GetResource("APP编辑成功");

        return Json(result);
    }

    /// <summary>删除APP</summary>
    /// <returns></returns>
    [DisplayName("删除APP")]
    [EntityAuthorize((PermissionFlags)16)]
    [HttpPost]
    public IActionResult Delete(Int32 Id)
    {
        var res = new DResult();

        var model = ProjectApp.FindById(Id);
        if (model == null)
        {
            res.msg = GetResource("APP不存在");
            return Json(res);
        }

        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin)
        {
            if (model.CreateUserID != ManageProvider.User?.ID)
            {
                res.msg = GetResource("您没有权限操作");
                return Json(res);
            }
        }

        model.Delete();

        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }
}
