﻿using DG;

using DH;
using DH.Core.Infrastructure;
using DH.Entity;
using DH.Helpers;
using DH.ServerSentEvents;
using DH.SLazyCaptcha;
using DH.SLazyCaptcha.Generator;

using HlktechIoT.Areas.Admin.Controllers;
using HlktechIoT.Areas.Device;
using HlktechIoT.Areas.Device.Controllers;
using HlktechIoT.Data;
using HlktechIoT.Services;
using HlktechIoT.Services.OAuth;
using HlktechIoT.Services.SSE;

using Microsoft.AspNetCore.ResponseCompression;

using NewLife;
using NewLife.Caching;
using NewLife.Common;
using NewLife.Log;
using NewLife.Model;
using NewLife.Serialization;

using Pek.Configs;
using Pek.Helpers;
using Pek.Infrastructure;
using Pek.Models;
using Pek.VirtualFileSystem;

using XCode;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

using IHostedService = Microsoft.Extensions.Hosting.IHostedService;

namespace HlktechIoT;

public class DGStartup : IPekStartup {
    public void Configure(IApplicationBuilder application)
    {
        var set = SysConfig.Current;
        if (set.IsNew || set.Name == "NewLife.Cube" || set.DisplayName == "HlktechIoT" || set.DisplayName == "创楚平台")
        {
            set.DisplayName = "海凌科IOT云";
            set.Save();
        }

        var _cacheProvider = EngineContext.Current.Resolve<ICacheProvider>();

        var _source = new CancellationTokenSource();

        _ = Task.Run(() => ConsumeMessage(_source));  // 处理其他端传过来的数据

        var site = SiteInfo.FindDefault();
        if (site != null)
        {
            site.SiteName = "海凌科IOT云";
            site.SeoTitle = "海凌科IOT云";
            site.SeoKey = "海凌科IOT云";
            site.SeoDescribe = "海凌科IOT云";
            site.Update();
        }
        else
        {
            site = new SiteInfo();
            site.SiteName = "海凌科IOT云";
            site.SeoTitle = "海凌科IOT云";
            site.SeoKey = "海凌科IOT云";
            site.SeoDescribe = "海凌科IOT云";
            site.Insert();
        }

        #region 初始化数据

        if (OtherMsgTpl.Meta.Cache.Entities.Count == 0)
        {
            if (XTrace.Debug) XTrace.WriteLine("开始初始化OtherMsgTpl[其他消息模板]数据……");

            var list1 = new List<OtherMsgTpl>();

            var entity = new OtherMsgTpl();
            entity.MName = "<strong>[用户]</strong>重置密码通知";
            entity.MTitle = "找回密码通知 - {site_name}";
            entity.MCode = "FindPassword";
            entity.MContent = "【海凌科】您正在申请找回登录密码，动态码：{code}，该验证码5分钟内有效，请勿泄露于他人";
            list1.Add(entity);

            entity = new OtherMsgTpl();
            entity.MName = "<strong>[用户]</strong>注册账户通知";
            entity.MTitle = "注册账户通知 - {site_name}";
            entity.MCode = "RegisteredCode";
            entity.MContent = "【海凌科】您正在申请注册会员，动态码：{code}，该验证码5分钟内有效，请勿泄露于他人";
            list1.Add(entity);

            list1.Insert();

            if (XTrace.Debug) XTrace.WriteLine("完成初始化OtherMsgTpl[其他消息模板]数据！");
        }

        #endregion

        LocaleStringResource.InitInsert("保存成功", "保存成功", "保存成功", "Successfully saved");
        LocaleStringResource.InitInsert("没有登录或登录超时！", "没有登录或登录超时！", "沒有登錄或登錄超時！", "No logins or login timeouts!");
        LocaleStringResource.InitInsert("海凌科IOT云", "海凌科IOT云", "海淩科IOT雲", "Hi-Link IOT Cloud", true);
        LocaleStringResource.InitInsert("一 万 年 太 久，只 争 朝 夕", "一 万 年 太 久，只 争 朝 夕", "一 萬 年 太 久，只 爭 朝 夕", "Ten thousand years is too long, just to seize the day.");
        LocaleStringResource.InitInsert("工作空间", "工作空间", "工作空間", "Workspace");

        var sitemap = CronJob.FindByName("Sitemap");
        if (sitemap != null)
        {
            sitemap.Enable = false;
            sitemap.Update();
        }
    }

    /// <summary>
    /// 将区域路由写入数据库
    /// </summary>
    public void ConfigureArea()
    {
        AreaBase.SetRoute<MainController>(AdminArea.AreaName);
        AreaBase.SetRoute<MassProductionController>(DeviceArea.AreaName);
        //AreaBase.SetRoute<DeviceController>(LabelArea.AreaName);
    }

    public void ConfigureServices(IServiceCollection services, IConfiguration configuration, IWebHostEnvironment webHostEnvironment)
    {
        // OAuth Cookie 服务
        services.AddScoped<IOAuthCookieService, OAuthCookieService>();

        if (RedisSetting.Current.RedisEnabled)
        {
            var fulleRedis = new FullRedis(RedisSetting.Current.RedisConnectionString, RedisSetting.Current.RedisPassWord!, RedisSetting.Current.RedisDatabaseId);

            ObjectContainer.Current.AddSingleton(fulleRedis); // 全局注入Redis连接
            services.AddSingleton(fulleRedis);

            //CacheFields.QueueRedis = new FullRedis(UtilSetting.Current.RedisConnectionString, UtilSetting.Current.RedisPassWord, UtilSetting.Current.RedisQueueDatabaseId);

            //CacheFields.FullRedis = new FullRedis(UtilSetting.Current.RedisConnectionString, UtilSetting.Current.RedisPassWord, UtilSetting.Current.RedisOtherDatabaseId);
        }

        // 验证码
        // 内存缓存
        //services.AddCaptcha(configuration);
        services.AddCaptcha(configuration, option =>
        {
            option.CaptchaType = CaptchaType.WORD_NUMBER_UPPER; // 验证码类型
            option.CodeLength = 4; // 验证码长度, 要放在CaptchaType设置后.  当类型为算术表达式时，长度代表操作的个数
            option.ExpirySeconds = 60; // 验证码过期时间
            option.IgnoreCase = true; // 比较时是否忽略大小写
            option.StoreageKeyPrefix = ""; // 存储键前缀

            option.ImageOption.Animation = true; // 是否启用动画
            option.ImageOption.FrameDelay = 120; // 每帧延迟,Animation=true时有效, 默认30

            option.ImageOption.Width = 150; // 验证码宽度
            option.ImageOption.Height = 50; // 验证码高度
            option.ImageOption.BackgroundColor = SkiaSharp.SKColors.White; // 验证码背景色

            option.ImageOption.BubbleCount = 2; // 气泡数量
            option.ImageOption.BubbleMinRadius = 5; // 气泡最小半径
            option.ImageOption.BubbleMaxRadius = 15; // 气泡最大半径
            option.ImageOption.BubbleThickness = 1; // 气泡边沿厚度

            option.ImageOption.InterferenceLineCount = 2; // 干扰线数量

            option.ImageOption.FontSize = 36; // 字体大小
            option.ImageOption.FontFamily = DefaultFontFamilys.Instance.Actionj; // 字体

            /* 
             * 中文使用kaiti，其他字符可根据喜好设置（可能部分转字符会出现绘制不出的情况）。
             * 当验证码类型为“ARITHMETIC”时，不要使用“Ransom”字体。（运算符和等号绘制不出来）
             */

            option.ImageOption.TextBold = true;// 粗体，该配置2.0.3新增
        });

        #region SSE

        // 注册默认的服务器发送事件服务。
        services.AddServerSentEvents();

        // 注册将由第二个中间件使用的自定义ServerSentEventsService，否则它们最终将共享连接的用户。
        services.AddServerSentEvents<INotificationsServerSentEventsService, NotificationsServerSentEventsService>(options =>
        {
            options.ReconnectInterval = 5000;
        });

        // 为服务器发送的事件注册基于 Cookie 的客户端标识符提供程序
        // services.AddServerSentEventsClientIdProvider<CookieBasedServerSentEventsClientIdProvider>();

        // 注册IServerSentEventsNoReconnectClientsIdsStorage由内存存储支持。
        // services.AddInMemoryServerSentEventsNoReconnectClientsIdsStore();

        services.AddSingleton<IHostedService, HeartbeatService>();
        services.AddNotificationsService(configuration);

        services.AddResponseCompression(options =>
        {
            options.MimeTypes = ResponseCompressionDefaults.MimeTypes.Concat(new[] { "text/event-stream" });
        });

        #endregion
    }

    /// <summary>
    /// 调整菜单
    /// </summary>
    public void ChangeMenu()
    {

    }

    /// <summary>
    /// 注册虚拟文件路径
    /// </summary>
    /// <param name="options"></param>
    public void ConfigureVirtualFileSystem(DHVirtualFileSystemOptions options)
    {
    }

    /// <summary>
    /// 注册路由
    /// </summary>
    /// <param name="endpoints">路由生成器</param>
    public void UseDHEndpoints(IEndpointRouteBuilder endpoints)
    {
        //endpoints.MapGrpcService<GreeterService>().RequireCors(CubeService.corsPolicy).EnableGrpcWeb();

        // 设置第一个服务器发送的事件终结点。
        endpoints.MapServerSentEvents("/see-heartbeat");

        // 设置第二个（分隔的）服务器发送事件终结点。
        endpoints.MapServerSentEvents<NotificationsServerSentEventsService>("/sse-notifications");
    }

    /// <summary>
    /// 升级更新逻辑
    /// </summary>
    public void Update()
    {
        var updateinfo = DHSetting.Current.UpdateInfo.Split("_");

        if (new Version(DHSetting.Current.CurrentVersion) == new Version("0.2.0"))
        {
            #region  0.2.0

            if (updateinfo[0] != "0.2.0" || (updateinfo[0] == "0.2.0" && updateinfo[1] == "0"))
            {
                var list = Device.FindAll();
                foreach(var item in list)
                {
                    item.ExpiredTime = DateTime.Now.AddMonths(15);
                    item.Update();
                }

                DHSetting.Current.UpdateInfo = $"0.2.0_1";
                DHSetting.Current.Save();
            }

            #endregion
        }
    }

    private async Task ConsumeMessage(CancellationTokenSource source)
    {
        XTrace.WriteLine($"启动了吗？");
        DefaultSpan.Current = null;
        var cancellationToken = source.Token;

        var Log = EngineContext.Current.Resolve<NewLife.Log.ILog>();
        var ITracer = EngineContext.Current.Resolve<ITracer>();
        var _queue = EngineContext.Current.Resolve<QueueService>();
        var queue = _queue.GetWebQueue();
        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                XTrace.WriteLine($"执行Web队列");
                ISpan? span = null;
                var mqMsg = await queue.TakeOneAsync(30).ConfigureAwait(false);
                if (mqMsg != null)
                {
                    XTrace.WriteLine($"执行Web队列1:{mqMsg}");

                    // 埋点
                    span = ITracer?.NewSpan($"mqtt:WebQueue", mqMsg);
                    if (Log != null && Log.Level <= NewLife.Log.LogLevel.Debug) XTrace.WriteLine("消费到下发指令消息：{0}", mqMsg);

                    // 解码
                    var dic = JsonParser.Decode(mqMsg);
                    span?.Detach(dic!);
                    var msg = JsonHelper.Convert<DelayQueue>(dic!);

                    if (msg!.Type.EqualIgnoreCase("OnLine"))  // 设备上下线处理
                    {
                        XTrace.WriteLine($"设备上下线处理");
                        var data = msg.InputData.SplitAsDictionary();
                        var ProductKey = data["ProductKey"];
                        var DeviceCode = data["DeviceCode"];
                        var Status = data["Status"].ToDGInt();

                        var model = Device.FindByCode(DeviceCode);
                        if (model != null)
                        {
                            if (Status == 3)  // 离线
                            {
                                model.Online = false;
                                model.Update();
                            }
                            else if (Status == 1)  // 在线
                            {
                                model.Online = true;
                                model.Update();
                            }
                            else if (Status == 98)  // 重置
                            {
                                // var modelDeviceAssociatedUsers = DeviceAssociatedUsers.FindByDeviceName(DeviceCode);
                                // if (modelDeviceAssociatedUsers != null)
                                // {
                                //     var list = new List<String> { modelDeviceAssociatedUsers.IdentityId };

                                //     if (!modelDeviceAssociatedUsers.SubUsers.IsNullOrWhiteSpace())
                                //     {
                                //         var SubUsers = modelDeviceAssociatedUsers.SubUsers.Trim(',').Split(',');
                                //         SubUsers.ForEach(list.Add);
                                //     }

                                //     var searchList = AppUser.FindAllByIdentityId(list);  // 获取所有与设备有关的用户
                                //     foreach (var item in searchList)
                                //     {
                                //         var hashDevices = item.Devices?.Split(',').ToList();
                                //         hashDevices ??= [];
                                //         var listProductKeys = item.ProductKeys?.Split(',').ToList();
                                //         listProductKeys ??= [];

                                //         var index = hashDevices.IndexOf(DeviceCode);
                                //         if (index != -1)
                                //         {
                                //             hashDevices.RemoveAt(index);
                                //             listProductKeys.RemoveAt(index);

                                //             item.Devices = hashDevices.Join();
                                //             item.ProductKeys = listProductKeys.Join();
                                //             item.Update();
                                //         }
                                //     }

                                //     modelDeviceAssociatedUsers.Delete();
                                // }

                                model.Online = false;
                                model.Update();
                            }
                            else if (Status == 99)  // 配网
                            {
                                // var modelDeviceAssociatedUsers = DeviceAssociatedUsers.FindByDeviceName(DeviceCode);
                                // if (modelDeviceAssociatedUsers != null)
                                // {
                                //     var list = new List<String> { modelDeviceAssociatedUsers.IdentityId };

                                //     if (!modelDeviceAssociatedUsers.SubUsers.IsNullOrWhiteSpace())
                                //     {
                                //         var SubUsers = modelDeviceAssociatedUsers.SubUsers.Trim(',').Split(',');
                                //         SubUsers.ForEach(list.Add);
                                //     }

                                //     var searchList = AppUser.FindAllByIdentityId(list);  // 获取所有与设备有关的用户
                                //     foreach (var item in searchList)
                                //     {
                                //         var hashDevices = item.Devices?.Split(',').ToList();
                                //         hashDevices ??= [];
                                //         var listProductKeys = item.ProductKeys?.Split(',').ToList();
                                //         listProductKeys ??= [];

                                //         var index = hashDevices.IndexOf(DeviceCode);
                                //         if (index != -1)
                                //         {
                                //             hashDevices.RemoveAt(index);
                                //             listProductKeys.RemoveAt(index);

                                //             item.Devices = hashDevices.Join();
                                //             item.ProductKeys = listProductKeys.Join();
                                //             item.Update();
                                //         }
                                //     }

                                //     modelDeviceAssociatedUsers.Delete();
                                // }

                                model.Online = true;
                                model.Update();
                            }
                        }
                    }
                    //else if (msg!.Type.EqualIgnoreCase("SelfService"))  // 自定义业务逻辑处理
                    //{
                    //    var data = msg.InputData.SplitAsDictionary();
                    //    var ProductKey = data["ProductKey"];
                    //    var DeviceCode = data["DeviceCode"];
                    //    var Name = data["Name"];
                    //    var Value = data["Value"];
                    //    var DataType = data["DataType"];

                    //    var model = Device.FindByCode(DeviceCode);
                    //    if (model != null && model.Online)
                    //    {
                    //        var modelProduct = Product.FindByCode(ProductKey);
                    //        if (modelProduct != null)
                    //        {
                    //            var list = new HashSet<String>();
                    //            if (modelProduct.BindType == 2 || modelProduct.BindType == 0)  // 抢占式/独占式
                    //            {
                    //                var modelDeviceAssociatedUsers = DeviceAssociatedUsers.FindByDeviceName(DeviceCode);
                    //                if (modelDeviceAssociatedUsers != null && !modelDeviceAssociatedUsers.IdentityId.IsNullOrWhiteSpace())
                    //                {
                    //                    list.Add(modelDeviceAssociatedUsers.IdentityId);
                    //                }
                    //            }
                    //            else if (modelProduct.BindType == 1)  // 分享式
                    //            {
                    //                var modelDeviceAssociatedUsers = DeviceAssociatedUsers.FindByDeviceName(DeviceCode);
                    //                if (modelDeviceAssociatedUsers != null)
                    //                {
                    //                    if (!modelDeviceAssociatedUsers.IdentityId.IsNullOrWhiteSpace())
                    //                    {
                    //                        list.Add(modelDeviceAssociatedUsers.IdentityId);
                    //                    }

                    //                    var listSubUsers = modelDeviceAssociatedUsers.SubUsers?.Split(',');
                    //                    if (listSubUsers != null && listSubUsers.Length != 0)
                    //                    {
                    //                        list.AddRange(listSubUsers);
                    //                    }
                    //                }
                    //            }

                    //            object content = DataType switch
                    //            {
                    //                "short" => Value.ToDGShort(),
                    //                "int" => Value.ToDGInt(),
                    //                "float" or "Single" => Value.ToDGFloat(),
                    //                "bool" => Value.ToDGBool(),
                    //                "byte" => Value.ToDGByte(),
                    //                "long" or "UInt64" => Value.ToDGLong(),
                    //                "double" or "Double" => Value.ToDGDouble(),
                    //                "time" or "DateTime" => Value.ToDGDate(),
                    //                _ => Value,
                    //            };

                    //            var Title = $"有新的信息";
                    //            var Content = $"设备上报信息";
                    //            var notificationTitle = Content;
                    //            var msgTitle = Title;

                    //            foreach (var item in list)
                    //            {
                    //                var modelAppUser = AppUser.FindByIdentityId(item);
                    //                if (modelAppUser != null && !modelAppUser.BindJiGuangId.IsNullOrWhiteSpace())
                    //                {
                    //                    var bind = modelAppUser.BindJiGuangId;

                    //                    var extras = new { modelAppUser.BindJiGuangId, UId = modelAppUser.IdentityId, PType = 1, SendTime = DateTime.Now, Name, content }.ToJson();
                    //                    var msgContent = extras;

                    //                    var pushClient = new JPushClient(DG.UtilSetting.Current.JPushKey, DG.UtilSetting.Current.JPushSecret);

                    //                    var RegistrationId = new Audience();
                    //                    RegistrationId.RegistrationId = [bind];

                    //                    var pushPayload = new PushPayload
                    //                    {
                    //                        Platform = "all",
                    //                        Audience = RegistrationId,
                    //                        // jpush的通知，android的由jpush直接下发，iOS的由apns服务器下发，Winphone的由mpns下发
                    //                        Notification = new Notification
                    //                        {
                    //                            Alert = notificationTitle,
                    //                            Android = new Android  // 指定当前推送的android通知
                    //                            {
                    //                                Alert = notificationTitle,
                    //                                Title = msgTitle,
                    //                                Extras = new Dictionary<string, object>
                    //                                {
                    //                                    ["androidNotificationExtrasKey"] = extras  // 此字段为透传字段，不会显示在通知栏。用户可以通过此字段来做一些定制需求，如特定的key传要指定跳转的页面（value）
                    //                                },
                    //                                URIAction = DG.UtilSetting.Current.URIAction
                    //                            },
                    //                            IOS = new IOS  // 指定当前推送的iOS通知
                    //                            {
                    //                                // 传一个IosAlert对象，指定apns title、title、subtitle等
                    //                                Alert = notificationTitle,
                    //                                // 直接传alert
                    //                                // 此项是指定此推送的badge自动加1
                    //                                Badge = "+1",
                    //                                // 此字段的值default表示系统默认声音；传sound.caf表示此推送以项目里面打包的sound.caf声音来提醒，
                    //                                // 如果系统没有此音频则以系统默认声音提醒；此字段如果传空字符串，iOS9及以上的系统是无声音提醒，以下的系统是默认声音
                    //                                Sound = "default",
                    //                                Extras = new Dictionary<string, object>
                    //                                {
                    //                                    ["iosNotification extras key"] = extras  // 此字段为透传字段，不会显示在通知栏。用户可以通过此字段来做一些定制需求，如特定的key传要指定跳转的页面（value）
                    //                                },
                    //                                // 此项说明此推送是一个background推送，想了解background看：http://docs.jpush.io/client/ios_tutorials/#ios-7-background-remote-notification
                    //                                // 取消此注释，消息推送时ios将无法在锁屏情况接收
                    //                                // ContentAvailable = true
                    //                            }
                    //                        },
                    //                        // Platform指定了哪些平台就会像指定平台中符合推送条件的设备进行推送。 jpush的自定义消息，
                    //                        // sdk默认不做任何处理，不会有通知提示。建议看文档http://docs.jpush.io/guideline/faq/的
                    //                        // [通知与自定义消息有什么区别？]了解通知和自定义消息的区别
                    //                        Message = new Message
                    //                        {
                    //                            Title = msgTitle,
                    //                            Content = msgContent,
                    //                            Extras = new Dictionary<String, String>
                    //                            {
                    //                                ["message extras key"] = extras
                    //                            }
                    //                        },

                    //                        Options = new Jiguang.JPush.Model.Options
                    //                        {
                    //                            // 此字段的值是用来指定本推送要推送的apns环境，false表示开发，true表示生产；对android和自定义消息无意义
                    //                            IsApnsProduction = DG.UtilSetting.Current.ApnsProduction,
                    //                            // 此字段是给开发者自己给推送编号，方便推送者分辨推送记录
                    //                            SendNo = 1,
                    //                            // 此字段的值是用来指定本推送的离线保存时长，如果不传此字段则默认保存一天，最多指定保留十天；
                    //                            TimeToLive = 86400
                    //                        }
                    //                    };

                    //                    var result1 = await pushClient.SendPushAsync(pushPayload).ConfigureAwait(false);
                    //                }
                    //            }
                    //        }
                    //    }
                    //}
                }
                else
                {
                    await Task.Delay(100, cancellationToken).ConfigureAwait(false);
                }
                span?.Dispose();
            }
        }
        catch (TaskCanceledException) { }
        catch (Exception ex)
        {
            XTrace.WriteException(ex);
        }
        finally
        {
            XTrace.WriteLine($"退出队列了吗？{cancellationToken.IsCancellationRequested}");
            source.Cancel();
        }
    }

    public void AfterAuth(IApplicationBuilder application)
    {
    }

    public void BeforeRouting(IApplicationBuilder application)
    {
    }

    public void ConfigureMiddleware(IApplicationBuilder application)
    {

    }

    /// <summary>
    /// 处理数据
    /// </summary>
    public void ProcessData()
    {
    }

    /// <summary>
    /// 获取此启动配置实现的顺序
    /// </summary>
    public int StartupOrder => 999; //常见服务应在错误处理程序之后加载

    /// <summary>
    /// 获取此启动配置实现的顺序。主要针对ConfigureMiddleware、UseRouting前执行的数据、UseAuthentication或者UseAuthorization后面
    /// Endpoints前执行的数据
    /// </summary>
    public int ConfigureOrder => 100;
}
