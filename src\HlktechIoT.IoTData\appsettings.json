{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  //"RedisCache": "server=127.0.0.1;password=******;db=3",
  "ConnectionStrings": {
    //"IoT": "Data Source=..\\Data\\IoT.db;Provider=Sqlite",
    //"IoTData": "Data Source=..\\Data\\IoTData.db;ShowSql=false;Provider=Sqlite",
    //"Membership": "Data Source=..\\Data\\Membership.db;Provider=Sqlite"
    //"IotDb": "DataSource=*************;Username=root;Password=root;Port=6667"

    //"IoT": "Server=*************;Port=3306;Database=IoTApp;Uid=root;Pwd=*********;provider=mysql",
    //"IoTData": "Server=*************;Port=3306;Database=IoData;Uid=root;Pwd=*********;provider=mysql",

    //"IoT_MySql": "server=.;database=***;user=***;password=***;Provider=MySql",
    //"IoTData_MySql": "server=.;database=***data;user=***;password=***;Provider=MySql"
  },
  "Urls": "http://*:2880",
  "Redis": null,
  "DataTopic": "DeviceData",
  "EventTopic": "DeviceEvent",
  "AlarmTopic": "AlarmInfo",
  "HistoryTopic": "DeviceHistory"
}
