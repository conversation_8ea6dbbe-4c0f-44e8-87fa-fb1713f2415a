<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <AssemblyTitle>物联网服务平台</AssemblyTitle>
    <Description>IoT服务平台</Description>
    <Company>深圳市海凌科电子有限公司</Company>
	<Authors>丁川</Authors>
    <Copyright>版权所有(C) 深圳市海凌科电子有限公司 2009-2024</Copyright>
    <VersionPrefix>0.1</VersionPrefix>
    <VersionSuffix>$([System.DateTime]::Now.ToString(`yyyy.MMdd`))</VersionSuffix>
    <Version>$(VersionPrefix).$(VersionSuffix)</Version>
    <FileVersion>$(Version)</FileVersion>
    <AssemblyVersion>$(VersionPrefix).*</AssemblyVersion>
    <Deterministic>false</Deterministic>
	<OutputPath>..\Bin\Server</OutputPath>
	<AppendTargetFrameworkToOutputPath>true</AppendTargetFrameworkToOutputPath>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <ImplicitUsings>enable</ImplicitUsings>
    <LangVersion>latest</LangVersion>
	  
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>$(NoWarn);1701;1702;1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="RpcControllers\**" />
    <Content Remove="RpcControllers\**" />
    <EmbeddedResource Remove="RpcControllers\**" />
    <None Remove="RpcControllers\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Common\DHMqttSetting.cs" />
    <Compile Remove="Common\JsonConverter.cs" />
    <Compile Remove="Common\JsonConverterForBuilder.cs" />
    <Compile Remove="Controllers\MqttController.cs" />
    <Compile Remove="IoTSetting.cs" />
    <Compile Remove="Models\AlarmHistoryModel.cs" />
    <Compile Remove="Models\DeviceDataModel.cs" />
    <Compile Remove="Models\DeviceEventModel.cs" />
    <Compile Remove="Models\MapOptions.cs" />
    <Compile Remove="Models\MqttAclReq.cs" />
    <Compile Remove="Models\MqttAuthReq.cs" />
    <Compile Remove="Models\MqttAuthResult.cs" />
    <Compile Remove="Models\MqttDeviceSession.cs" />
    <Compile Remove="Models\MqttUserSession.cs" />
    <Compile Remove="Models\QueueOption.cs" />
    <Compile Remove="Services\AppService.cs" />
    <Compile Remove="Services\DataService.cs" />
    <Compile Remove="Services\DeviceService.cs" />
    <Compile Remove="Services\LoRaService.cs" />
    <Compile Remove="Services\MqttController.cs" />
    <Compile Remove="Services\MqttExtensions.cs" />
    <Compile Remove="Services\MqttGateway.cs" />
    <Compile Remove="Services\MqttService.cs" />
    <Compile Remove="Services\MyUserService.cs" />
    <Compile Remove="Services\QueueExtensions.cs" />
    <Compile Remove="Services\QueueService.cs" />
    <Compile Remove="Services\RpcExtensions.cs" />
    <Compile Remove="Services\RpcService.cs" />
    <Compile Remove="Services\RuleService.cs" />
    <Compile Remove="Services\SegmentService.cs" />
    <Compile Remove="Services\ThingService.cs" />
    <Compile Remove="Services\TokenService.cs" />
    <Compile Remove="Services\TransformService.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="DH.NIP" Version="4.12.2025.530-beta0137" />
    <PackageReference Include="DH.NStardust.Extensions" Version="4.12.2025.621-beta0339" />
    <PackageReference Include="DynamicExpresso.Core" Version="2.19.2" />
    <PackageReference Include="HlktechIoT.Data" Version="1.7.2025.6210229" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\HlktechIoT.IoTLoRaWAN\HlktechIoT.IoTLoRaWAN.csproj" />
    <ProjectReference Include="..\HlktechIoT.IoTMqtt\HlktechIoT.IoTMqtt.csproj" />
    <ProjectReference Include="..\HlktechIoT.IoTPush\HlktechIoT.IoTPush.csproj" />
    <ProjectReference Include="..\HlktechIoT.IoTRpc\HlktechIoT.IoTRpc.csproj" />
    <ProjectReference Include="..\HlktechIoT.IoTService\HlktechIoT.IoTService.csproj" />
  </ItemGroup>

</Project>
