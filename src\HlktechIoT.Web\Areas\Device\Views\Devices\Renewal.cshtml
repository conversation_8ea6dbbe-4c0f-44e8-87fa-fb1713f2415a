﻿@using System.Collections;
@using DH.Helpers;
@{
    var dic = ViewBag.dic as Dictionary<String, String>;
    //var listThingFields = Model.ThingFields as IList<ThingFields>;
    var deviceName = Model.Code;
}
<div class="layui-row layui-col-space15">
    <div class="layui-col-md12">
        <div class="layui-card">
            <div class="layui-card-body top0" style="padding:0;">
                <div class="dgtable-body Removepadding" style="margin-top: 0px; padding-top: 15px;">
                    <div class="layui-form" style="display: block; width: 100%; margin-bottom: 20px;">
                        @T("续期年份：")
                        <div class="layui-inline">
                            <select name="year" lay-verify="" id="year">
                                <option value="0">@T("请选择续期年份")</option>
                                <option value="1">1 @T("年")</option>
                                <option value="2">2 @T("年")</option>
                                <option value="3">3 @T("年")</option>
                                <option value="4">4 @T("年")</option>
                                <option value="5">5 @T("年")</option>
                                <option value="6">6 @T("年")</option>
                                <option value="7">7 @T("年")</option>
                                <option value="8">8 @T("年")</option>
                                <option value="9">9 @T("年")</option>
                                <option value="10">10 @T("年")</option>
                            </select>
                        </div>
                        <br />
                        <br />
                        @T("续期说明：")
                        <div class="layui-inline">
                            <textarea name="" required lay-verify="required" placeholder="@T("请输入续期说明")" class="layui-textarea" id="remark"></textarea>
                        </div>
                        <br />
                        <br />
                        <div class="layui-inline">
                            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="subRenewal()">@T("续期")</button>
                        </div>

                        <div class="layui-inline" style="float:right">
                            @* <a id="InverterExport" class="layui-btn layui-btn-primary layui-btn-sm" onclick="Export()">@T("导出")</a> *@
                        </div>
                    </div>

                    <div>
                        <table class="" id="tablist2" lay-filter="tool"></table>
                    </div>
@*                     <script type="text/html" id="tool">
                        <a class="layui-btn layui-btn-xs" lay-event="look"> @T("查看")</a>
                        @if (this.Has((PermissionFlags)8))
                        {
                                <a class="layui-btn layui-btn-xs" lay-event="del"> @T("删除")</a>
                        }
                    </script> *@
                </div>
            </div>
        </div>
    </div>
</div>

<script asp-location="Footer">

    function subRenewal(){
        var deviceName = '@Model.Code';
        var year = $("#year").val();
        var remark = $("#remark").val();
        $.post('@Url.Action("Renewal")',{DeviceName:deviceName,Year:year,Remark:remark},function(res){
              if (res.success) {
                    parent.layer.closeAll();
                    layui.dg.reload("tables")
                }
                else {
                    layui.layer.msg(res.msg)
                }
        })
    }

        layui.use(['abp', 'dg', 'form','table','dgcommon','common','laydate'], () => {
         var os = layui.dgcommon;
         var table = layui.table;
         table.render({
            elem: '#tablist2',
            url: '@Url.Action("Renewallist", new { DeviceName = deviceName })',
            loading: true,
            cols:[[
                { field: 'Id', title: '@T("编号")', align: "left", rowspan: 2, width: 60,align:'center' },
                { field: 'LastExpiredTime', title: '@T("续期前授权到期时间")', rowspan: 2, minWidth: 180,align:'center' },
                { field: 'ExpiredTime', title: '@T("最新授权到期时间")', rowspan: 2, minWidth: 180,align:'center' },
                 { field: 'DType', title: '@T("操作类型")', minWidth: 130,align:'center',templet:function(d){
                            if(d.DType == 1){
                                return '@T("手动续期")'
                            }else if(d.DType == 2){
                                return '@T("客户自助支付续期")'
                            }
                 } },
                 { field: 'Remark', title: '@T("续期说明")', rowspan: 2, minWidth: 150,align:'center' },
                 { field: 'CreateUser', title: '@T("操作人")', rowspan: 2, minWidth: 90,align:'center' },
                 { field: 'CreateTime', title: '@T("创建时间")', rowspan: 2, minWidth: 150,align:'center' },
            ]],
            page: { curr: os.GetPageNum("Renewallist") },
            limit: 10,
            limits: [20, 50, 100, 200, 500],
            id: 'tablist2',
            smartReloadModel: true,
            text: { none: '@T("无数据")' },
         });

         window.active = {
             reload: function() {
                 table.reload('tablist2',{
                     page:{curr:1},
                     where:{

                     },
                     text: { none: '@T("无数据")' },
                 })
             }
         };


    })


</script>
