@{
    Html.AppendTitleParts(T("模组固件升级日志").Text);
}
<style asp-location="true">
    .totalcolor {
        font-size: 18px;
        color: #2db7f5;
    }

    .onlinecolor {
        font-size: 18px;
        color: #19be6b;
    }

    .noactivecolor {
        font-size: 18px;
        color: #f90;
    }

    .layui-card-header {
        height: auto;
    }

    .layui-form-label {
        width: auto;
    }

    .dg-form .layui-form-label {
        width: auto;
    }

    .layui-form-select dl {
        top: 32px;
        padding: 0px
    }

    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }
    /* 操作列自适应样式 */
    .layui-table-fixed-r .layui-table-body {
        overflow-x: hidden !important;
    }

    .operation-column {
        white-space: nowrap;
        padding: 0 5px; /* 添加左右边距 */
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center; /* 垂直居中 */
        flex-wrap: nowrap;
        height: 100%; /* 确保容器占满单元格高度 */
        width: 100%; /* 确保容器占满单元格宽度 */
        box-sizing: border-box; /* 确保内边距不增加总宽度 */
    }

        .operation-column .pear-btn {
            margin-right: 6px; /* 保持原有按钮间距 */
            padding: 0 8px; /* 保持按钮内边距 */
            height: 30px; /* 保持按钮高度 */
            line-height: 30px; /* 行高与高度一致 */
            min-width: 44px; /* 按钮最小宽度 */
            text-align: center;
            flex-grow: 1; /* 按钮自动扩展占用空间 */
            flex-basis: 0; /* 所有按钮基础宽度相同 */
        }

            .operation-column .pear-btn:last-child {
                margin-right: 0;
            }

    /* 确保表格单元格内容居中 */
    .layui-table-cell {
        text-align: center;
        padding: 0 2px; /* 保持单元格内边距 */
    }

    /* 固定列样式优化 */
    .layui-table-fixed {
        height: auto !important;
    }

    /* 确保表格右侧固定列正确显示 */
    .layui-table-fixed-r {
        right: 0 !important;
    }

        /* 优化表格右侧固定列的单元格 */
        .layui-table-fixed-r .layui-table-cell {
            padding: 0;
        }
</style>
<form class="layui-form dg-form">
    <div class="layui-form-item" style="margin-bottom: 3px;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width: auto">@T("关键词")：</label>
            <div class="layui-input-inline">
                <input type="text" name="key" id="key" placeholder="@T("请输入设备DeviceName")" autocomplete="off" class="layui-input" lay-filter="key">
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label"> @T("状态")：</label>
            <div class="layui-input-inline seller-inline-3">
                <select name="Status" id="Status" lay-filter="upgradeChange">
                    <option value="-2">@T("请选择")</option>
                    <option value="-1">@T("全部")</option>
                    <option value="0">@T("未升级")</option>
                    <option value="1">@T("升级中")</option>
                    <option value="2">@T("升级成功")</option>
                    <option value="3">@T("升级失败")</option>
                    <option value="4">@T("升级取消")</option>
                </select>
            </div>
        </div>

        <div class="layui-inline" style="float:right">
            <a id="InverterExport" class="layui-btn layui-btn-sm" onclick="ExportDevices()">@T("导出")</a>
        </div>
    </div>
</form>
<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
        <script type="text/html" id="tool">
            <div class="operation-column">
                {{#  layui.each(window.operationButtons, function(index, button){ }}
                    <a class="pear-btn {{button.class}} pear-btn-xs" lay-event="{{button.event}}">{{button.text}}</a>
                {{#  }); }}
                </div>
            
        </script>
    </div>
</div>

<script asp-location="Footer">
    // 设置变量保存选中行信息
    let ids = new Array();
    // 保存当前页全部数据id，点击全选时使用
    let tableIds = new Array();
    
    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon', 'dtree'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;
        var dtree = layui.dtree;
        // 按钮配置集中定义
        var operationButtons = [
            { text: '@T("删除")', event: 'del', class: 'pear-btn-danger' },
            { text: '@T("明细")', event: 'detail', class: 'pear-btn-danger' },
        ];

        // 将按钮配置保存到全局变量，供模板使用
        window.operationButtons = operationButtons;

        // 动态计算操作列宽度的函数
        function calculateOperationColumnWidth() {
            try {
                var buttonMargin = 6; // 按钮间距
                var extraPadding = 24; // 额外边距
                var baseButtonPadding = 20; // 按钮内边距

                if (operationButtons.length === 0) {
                    return 100;
                }

                var totalWidth = 0;

                operationButtons.forEach(function(button) {
                    // 调整文字宽度估算：中文字符约16px，英文字符约9px
                    var text = button.text;
                    var textWidth = 0;
                    for (var i = 0; i < text.length; i++) {
                        var char = text.charAt(i);
                        if (/[\u4e00-\u9fa5]/.test(char)) {
                            textWidth += 16; // 中文字符
                        } else {
                            textWidth += 9;  // 英文字符
                        }
                    }

                    var buttonWidth = textWidth + baseButtonPadding;
                    totalWidth += buttonWidth;
                });

                // 加上按钮间距和额外边距
                totalWidth += (operationButtons.length - 1) * buttonMargin + extraPadding;

                // 设置最小宽度
                if (totalWidth < 120) {
                    totalWidth = 120;
                }

                console.log('计算的操作列宽度:', totalWidth, '按钮数量:', operationButtons.length, '按钮配置:', operationButtons);
                return totalWidth;
            } catch (error) {
                console.error('计算操作列宽度时出错:', error);
                return 200;
            }
        }

        // 应用操作列宽度的函数
        function applyOperationColumnWidth() {
            try {
                var calculatedWidth = calculateOperationColumnWidth();

                var fixedRightHeader = $('.layui-table-fixed-r .layui-table-header table thead tr th:last-child');
                var fixedRightBody = $('.layui-table-fixed-r .layui-table-body table tbody tr td:last-child');

                if (fixedRightHeader.length > 0) {
                    fixedRightHeader.css('width', calculatedWidth + 'px');
                    fixedRightHeader.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                if (fixedRightBody.length > 0) {
                    fixedRightBody.css('width', calculatedWidth + 'px');
                    fixedRightBody.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                var mainTableHeader = $('#tablist').next('.layui-table-view').find('.layui-table-header table thead tr th:last-child');
                var mainTableBody = $('#tablist').next('.layui-table-view').find('.layui-table-body table tbody tr td:last-child');

                if (mainTableHeader.length > 0) {
                    mainTableHeader.css('width', calculatedWidth + 'px');
                    mainTableHeader.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                if (mainTableBody.length > 0) {
                    mainTableBody.css('width', calculatedWidth + 'px');
                    mainTableBody.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                console.log('已应用操作列宽度:', calculatedWidth);
            } catch (error) {
                console.error('应用操作列宽度时出错:', error);
            }
        }

        // 监听窗口大小变化
        $(window).on('resize', function() {
            try {
                setTimeout(function() {
                    applyOperationColumnWidth();
                    console.log('窗口大小变化，已重新调整操作列宽度');
                }, 100);
            } catch (error) {
                console.error('窗口resize时出错:', error);
            }
        });

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetList")'
            , page: true //开启分页
            , loading: true
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cols: [[
                { type: 'checkbox', fixed: 'left' }
                , { field: 'Id', title: 'ID', minWidth: 60 }
                , { field: 'FirmwareInfoOriginFileName', title: '@T("固件名称")', minWidth: 190 }
                , { field: 'IotId', title: '@T("设备DeviceName")', minWidth: 140 }
                , { title: '升级状态', toolbar: '#status', minWidth: 100 }
                , { field: 'Remark', title: '@T("升级失败原因")', minWidth: 160 }
                , { field: 'LastSVersion', title: '@T("上一个软件版本号")', minWidth: 170 }
                , { field: 'SVersions', title: '@T("固件版本号")', minWidth: 170 }
                , { field: 'CreateTime', title: '@T("创建时间")', minWidth: 170 }
                , { field: 'CreateIP', title: '@T("创建地址")', minWidth: 130 }
                , { field: 'UpdateUser', title: '@T("更新者")', minWidth: 70 }
                , { field: 'UpdateTime', title: '@T("更新时间")', minWidth: 170 }
                , { field: 'UpdateIP', title: '@T("更新地址")', minWidth: 120 }
                , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', align: "center", width: calculateOperationColumnWidth() }
            ]]
            , limit: 16
            , limits: [10, 16, 20, 30, 50, 100]
            , smartReloadModel: true
            , height: 'full-110'
            , id: 'tables'
            , done: function (res) {

                try {
                    // 延迟执行，确保DOM已经渲染完成
                    setTimeout(function() {
                        applyOperationColumnWidth();
                    }, 200);
                } catch (error) {
                    console.error('表格done回调中出错:', error);
                }
                // 设置当前页全部数据Id到全局变量
                tableIds = res.data.map(function (value) {
                    return value.Id;
                });

                // 设置当前页选中项
                $.each(res.data, function (idx, val) {
                    if (ids.indexOf(val.Id) > -1) {
                        val["LAY_CHECKED"] = 'true';
                        //找到对应数据改变勾选样式，呈现出选中效果
                        let index = val['LAY_INDEX'];
                        $('tr[data-index=' + index + '] input[type="checkbox"]').click();
                        form.render('checkbox'); //刷新checkbox选择框渲染
                    }
                });
                // 获取表格勾选状态，全选中时设置全选框选中
                let checkStatus = table.checkStatus('tables');
                if (checkStatus.isAll) {
                    $('.layui-table-header th[data-field="0"] input[type="checkbox"]').prop('checked', true);
                    form.render('checkbox'); //刷新checkbox选择框渲染
                }

            }
        });

        // 监听勾选事件
        table.on('checkbox(tool)', function (obj) {
            if (obj.checked == true) {
                if (obj.type == 'one') {
                    ids.push(obj.data.Id);
                } else {
                    for (let i = 0; i < tableIds.length; i++) {
                        //当全选之前选中了部分行进行判断，避免重复
                        if (ids.indexOf(tableIds[i]) == -1) {
                            ids.push(tableIds[i]);
                        }
                    }
                }
            } else {
                if (obj.type == 'one') {
                    let i = ids.length;
                    while (i--) {
                        if (ids[i] == obj.data.Id) {
                            ids.splice(i, 1);
                        }
                    }
                } else {
                    let i = ids.length;
                    while (i--) {
                        if (tableIds.indexOf(ids[i]) != -1) {
                            ids.splice(i, 1);
                        }
                    }
                }
            }
        });

        window.active = {
            reload: function () {
                table.reload('tables',
                    {
                        where: {
                            key: $("#key").val(),
                            Status: $("#Status").val(),
                        },
                        page: {
                            curr: 1
                        }
                    });
            },
            success: function (data) {
                os.success(data);
            }
        }

        $("#key").on("input", function (e) {
            active.reload();
        });
        $("#name").on("input", function () {
            active.reload();
        });
        form.on("select(fromChange)", function () {
            active.reload();
        });
        form.on("select(upgradeChange)", function () {
            active.reload();
        });


        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'refresh') {
                active.reload();
            }
        });

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === "look") {
                if (data.Status == 0) {
                    layer.msg(' @T("设备未激活,无法查看")', {
                        offset: 't',
                        anim: 6
                    });
                    return;
                }
                top.layui.admin.popupRight({
                    id: 'ThingsDetail'
                    , title: ' @T("设备详情")'
                    , closeBtn: 1
                    , area: ['650px']
                    , success: function () {
                        $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Detail")?Id=' + data.IotId + '" frameborder="0" class="layadmin-iframe"></iframe>');
                    }
                });
            } else if (obj.event === "del") {
                layer.confirm('@T("确认删除吗")?', {
                    title: '@T("提示")',
                    btn: ['@T("确认")', '@T("取消")']
                }, function (index) {
                    $.post('@Url.Action("Delete")', { Id: data.Id }, function (data) {
                        if (data.success) {
                            os.success('@T("删除成功")');
                            active.reload();
                        } else {
                            os.warning(data.msg);
                        }
                    });
                    layer.close(index);
                });
            }else if (obj.event === "detail") {
                top.layui.dg.popupRight({
                    id: 'FileAdd'
                    , title: ' @T("日志明细") (' + data.Id + ')'
                    , closeBtn: 1
                    , area: ['1000px']
                    , success: function () {
                        $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Detail")' + abp.utils.formatString("?lId={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                    }
                });
            }

        });

        window.saveCallback = function (data) {
            parent.layer.close(data.index);
            abp.notify.success(data.msg);
            table.reload("tables");
        }

        window.warning = function (msg) {
            os.warning(msg);
        }

        //导出数据
        window.ExportDevices = function () {
            var keys = $("#key").val();
            var status = $("#Status").val()
            if ((keys.length == 0 && status == -2) && ids.length == 0) {
                os.error("@T("请选中设备或者搜索筛选直接导出")");
                return;
            }

            // 创建表单
            var form = $('<form></form>').attr('action', '@Url.Action("ExportDevices")').attr('method', 'POST');
            form.append($('<input>').attr('type', 'hidden').attr('name', 'keys').attr('value', keys));
            form.append($('<input>').attr('type', 'hidden').attr('name', 'status').attr('value', status));
            form.append($('<input>').attr('type', 'hidden').attr('name', 'ids').attr('value', ids.join(',')));

            // 将表单添加到body并提交
            form.appendTo('body').submit().remove();

            // var href = '@Url.Action("ExportDevices")?Versions=' + versions + '&ids=' + ids.join(',');
            // $("#InverterExport").attr("href", href);
            // //alert("获取到ExportDevices打印按钮")
        }
    });

</script>

<script type="text/html" id="status">
    {{# if(d.Status == 0) { }}
    <span class="layui-badge layui-bg-green">@T("未升级")</span>
    {{# } else if(d.Status == 1) { }}
    <span class="layui-badge layui-bg-blue">@T("升级中")</span>
    {{# } else if(d.Status == 2) { }}
    <span class="layui-badge layui-bg-gray"> @T("升级成功")</span>
    {{# } else if(d.Status == 3) { }}
    <span class="layui-badge layui-bg-cyan"> @T("升级失败")</span>
    {{# } else if(d.Status == 4) { }}
    <span class="layui-badge layui-bg-orange"> @T("升级取消")</span>
    {{# } }}
</script>
