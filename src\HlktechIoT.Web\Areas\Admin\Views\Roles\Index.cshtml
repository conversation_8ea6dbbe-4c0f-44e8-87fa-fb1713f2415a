﻿@{
    Html.AppendTitleParts(T("角色管理").Text);

    // Script - 引入动态操作列组件
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/components/dynamic-operation-column.js");

    // Css - 引入动态操作列样式
    Html.AppendCssFileParts(ResourceLocation.Head, "~/css/components/dynamic-operation-column.css");
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    /* 固定列样式优化 */
    .layui-table-fixed-r .layui-table-body {
        overflow-x: hidden !important;
    }

    .layui-table-fixed {
        height: auto !important;
    }

    .layui-table-fixed-r {
        right: 0 !important;
    }

    .layui-table-fixed-r .layui-table-cell {
        padding: 0;
        overflow: visible;
    }

    /* 确保表格行高一致 */
    .layui-table tbody tr {
        height: 38px;
    }

    .layui-table .layui-table-cell {
        height: 38px;
        line-height: 38px;
    }
</style>

<div class="layui-card">
    <div class="layui-card-header">@T("角色管理")</div>
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    layui.use(['table', 'abp', 'dg', 'form'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;

        // 定义操作按钮配置
        var operationButtons = [];
        
        // 根据权限添加按钮配置
        @if (this.Has((PermissionFlags)4))
        {
            @:operationButtons.push({
            @:    text: '@T("编辑")',
            @:    event: 'look',
            @:    class: 'pear-btn pear-btn-primary',
            @:    condition: function(d) { return true; }, // 编辑按钮对所有记录都显示
            @:    alwaysShow: true
            @:});
        }
        
        @if (this.Has((PermissionFlags)8))
        {
            @:operationButtons.push({
            @:    text: '@T("删除")',
            @:    event: 'del',
            @:    class: 'pear-btn pear-btn-danger',
            @:    condition: function(d) { return d.IsSystem != 1; }, // 系统记录不能删除
            @:    alwaysShow: true
            @:});
        }
        
        @if (ViewBag.IsControllerExists == true)
        {
            @:operationButtons.push({
            @:    text: '@T("扩展")',
            @:    event: 'extend',
            @:    class: 'pear-btn pear-btn-primary',
            @:    condition: function(d) { return d.Id != 1; }, // ID为1的记录不显示扩展按钮
            @:    alwaysShow: true
            @:});
        }

        // 初始化动态操作列组件
        var operationColumnWidth = window.dynamicOperationColumn.init({
            buttons: operationButtons,
            tableId: 'tablist',
            debug: true  // 开启调试模式
        });

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetRole")'
            //, page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 80
            , cols: [[
                { field: "Id", title: 'Id', width: 100 },
                { field: "Name", title: '@T("权限组名")', width: 200 },
                { field: "Remark", title: '@T("描述")', align: "left" },
                { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: operationColumnWidth }
            ]]
            , id: 'tables',
            done: function (res, curr, count) {
                try {
                    // 使用通用组件应用操作列宽度
                    window.dynamicOperationColumn.delayApplyWidth('tablist', 300, true);
                    console.log('角色管理表格渲染完成，已应用动态操作列宽度');
                } catch (error) {
                    console.error('表格done回调中出错:', error);
                }
            }
        });

        window.active = {
            reload: function () {
                table.reload('tables', {
                    done: function(res, curr, count) {
                        try {
                            // 使用通用组件重新应用操作列宽度
                            window.dynamicOperationColumn.delayApplyWidth('tablist', 300, true);
                            console.log('角色管理表格重载完成，已重新应用动态操作列宽度');
                        } catch (error) {
                            console.error('表格重载done回调中出错:', error);
                        }
                    }
                });
            }
        }

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            
            // 检查按钮是否被禁用
            if (obj.event === 'disabled') {
                abp.notify.warn('@T("当前状态下不可操作")');
                return false;
            }
            
            if (obj.event === "look") {
                top.layui.dg.popupRight({
                    id: 'RoleDetail'
                    , title: ' @T("修改角色")'
                    , closeBtn: 1
                    , area: ['780px']
                    , success: function () {
                        $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("EditDetail")/' + data.Id + '" frameborder="0" class="layadmin-iframe"></iframe>');

                    }
                });
            } else if (obj.event === "extend") {
                top.layui.dg.popupRight({
                    id: 'RoleExIndex'
                    , title: ' @T("权限扩展")'
                    , closeBtn: 1
                    , area: ['780px']
                    , success: function () {
                        $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Index", "RoleEx")/' + data.Id + '" frameborder="0" class="layadmin-iframe"></iframe>');
                    }
                });
            } else if (obj.event === "del") {
                if (data.Id == 1) {
                    layer.msg('@T("超级管理员不可被删除")', {
                        offset: 't',
                        anim: 6
                    });
                    return;
                }
                parent.layer.confirm('@T("确认删除吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("Delete")', { id: data.Id }, function (data) {
                        if (data.success) {
                            abp.notify.success('@T("删除成功")');
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            }
        });

        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'add') {
                top.layui.dg.popupRight({
                    id: 'RoleDetail'
                    , title: ' @T("创建角色")'
                    , closeBtn: 1
                    , area: ['800px']
                    , success: function () {
                        $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("CreateDetail")" frameborder="0" class="layadmin-iframe"></iframe>');
                    }
                });
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });

        
    
    });
</script>

<script type="text/html" id="tool">
    <div class="operation-column">
        {{#  layui.each(window.operationButtons, function(index, button){ }}
            {{#  var isEnabled = button.condition ? button.condition(d) : true; }}
            {{#  var buttonClass = button.class + ' pear-btn-xs'; }}
            {{#  if(!isEnabled){ }}
                {{#  buttonClass += ' disabled-button'; }}
            {{#  } }}
            <a class="{{buttonClass}}" lay-event="{{isEnabled ? button.event : 'disabled'}}" 
               title="{{!isEnabled ? '当前状态下不可操作' : ''}}"
               data-enabled="{{isEnabled}}">{{button.text}}</a>
        {{#  }); }}
    </div>
</script>

<script type="text/html" id="user-toolbar">
    @if (this.Has((PermissionFlags)2)){
    <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        @T("新增")
    </button>
    }
</script>