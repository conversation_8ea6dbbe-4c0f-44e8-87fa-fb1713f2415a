﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="DH.DotNetDetour" Version="4.0.2025.61700080" />
    <PackageReference Include="DH.NCore" Version="4.12.2025.619-beta1104" />
    <PackageReference Include="IKVM" Version="8.11.2" />
    <PackageReference Include="IKVM.Maven.Sdk" Version="1.9.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
	
  <ItemGroup>
    <MavenReference Include="com.aliyun.openservices:iot-as-bridge-sdk-core" Version="2.4.1" />
	<MavenReference Include="ch.qos.logback:logback-classic" Version="1.1.11" />
  </ItemGroup>
	
  <ItemGroup>
    <None Update="application.conf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="devices.conf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
