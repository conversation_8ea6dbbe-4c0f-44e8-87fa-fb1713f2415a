/* 包装器参数 */
#view * {
  box-sizing: border-box;
}

#view .wapper {
  position: absolute;
  cursor: move;
  box-sizing: border-box;
  border: 1px solid transparent; /* 包装器边框 */
}
#view .wactive {
  border: 1px solid #59c7f9;
}

#view .wapper .wapper-point {
  display: none;
}

#view .wactive .wapper-point {
  display: block;
  position: absolute;
  background: #fff;
  border: 1px solid #59c7f9;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  z-index: 1;
}

/* 八个拉伸点 */
#view .wapper .point-l {
  top: calc(50% - 2px);
  left: -3px;
  cursor: ew-resize;
}

#view .wapper .point-t {
  left: calc(50% - 2px);
  top: -3px;
  cursor: ns-resize;
}

#view .wapper .point-r {
  top: calc(50% - 2px);
  right: -3px;
  cursor: ew-resize;
}

#view .wapper .point-b {
  left: calc(50% - 2px);
  bottom: -3px;
  cursor: ns-resize;
}

#view .wapper .point-lt {
  left: -3px;
  top: -3px;
  cursor: nwse-resize;
}

#view .wapper .point-lb {
  left: -3px;
  bottom: -3px;
  cursor: nesw-resize;
}

#view .wapper .point-rt {
  right: -3px;
  top: -3px;
  cursor: nesw-resize;
}

#view .wapper .point-rb {
  right: -3px;
  bottom: -3px;
  cursor: nwse-resize;
}

/* 控件通用参数 */
#view .ctrl {
  position: absolute;
  height: 100%;
  width: 100%;
}

#view .ctrl-disable {
  pointer-events: none;
  -moz-user-select: none; /*火狐*/
  -webkit-user-select: none; /*webkit浏览器*/
  user-select: none;
}
