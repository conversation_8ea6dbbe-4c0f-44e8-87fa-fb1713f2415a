﻿<?xml version="1.0" encoding="utf-8"?>
<EntityModel xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:schemaLocation="https://newlifex.com https://newlifex.com/Model202407.xsd" Version="11.7.2023.0327" Document="https://newlifex.com/xcode/model" xmlns="https://newlifex.com/Model202407.xsd">
  <Option>
    <!--类名模板。其中{name}替换为Table.Name，如{name}Model/I{name}Dto等-->
    <ClassNameTemplate />
    <!--显示名模板。其中{displayName}替换为Table.DisplayName-->
    <DisplayNameTemplate />
    <!--基类。可能包含基类和接口，其中{name}替换为Table.Name-->
    <BaseClass>CubeEntityBase</BaseClass>
    <!--命名空间-->
    <Namespace>HlktechIoT.Entity</Namespace>
    <!--输出目录-->
    <Output>.\</Output>
    <!--是否使用中文文件名。默认false-->
    <ChineseFileName>False</ChineseFileName>
    <!--用于生成Copy函数的参数类型。例如{name}或I{name}-->
    <ModelNameForCopy />
    <!--带有索引器。实现IModel接口-->
    <HasIModel>False</HasIModel>
    <!--可为null上下文。生成String?等-->
    <Nullable>True</Nullable>
    <!--数据库连接名-->
    <ConnName>DH</ConnName>
    <!--模型类模版。设置后生成模型类，用于接口数据传输，例如{name}Model-->
    <ModelClass>{name}Model</ModelClass>
    <!--模型类输出目录。默认当前目录的Models子目录-->
    <ModelsOutput>.\Models\</ModelsOutput>
    <!--模型接口模版。设置后生成模型接口，用于约束模型类和实体类，例如I{name}-->
    <ModelInterface>I{name}</ModelInterface>
    <!--模型接口输出目录。默认当前目录的Interfaces子目录-->
    <InterfacesOutput>.\Interfaces\</InterfacesOutput>
    <!--用户实体转为模型类的模型类。例如{name}或{name}DTO-->
    <ModelNameForToModel />
    <!--命名格式。Default/Upper/Lower/Underline-->
    <NameFormat>Default</NameFormat>
    <!--魔方区域显示名-->
    <DisplayName />
    <!--魔方控制器输出目录-->
    <CubeOutput />
  </Option>
  <Tables>
    <Table Name="JiLiYu" TableName="DH_JiLiYu" Description="激励语">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="激励语内容" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
    </Table>
    <Table Name="VerifyCode" TableName="DH_VerifyCode" Description="验证码">
      <Columns>
        <Column Name="Key" DataType="String" PrimaryKey="True" Description="验证码唯一键" />
        <Column Name="Code" DataType="String" Description="验证码" />
        <Column Name="EndTime" DataType="DateTime" Description="过期时间" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
      </Columns>
    </Table>
    <Table Name="UserEx" TableName="DH_UserEx" Description="用户扩展表">
      <Columns>
        <Column Name="Id" DataType="Int32" PrimaryKey="True" Description="编号" />
      </Columns>
    </Table>
    <Table Name="OpenPlatform" TableName="DH_OpenPlatform" Description="第三方体系" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Nullable="False" Description="公司名称" />
        <Column Name="AccessId" DataType="String" Nullable="False" Description="商户号" />
        <Column Name="AccessKey" DataType="String" Nullable="False" Description="商户密钥" />
        <Column Name="OfficialUrl" DataType="String" Length="100" Description="第三方服务器地址" />
        <Column Name="Enabled" DataType="Boolean" DefaultValue="True" Description="是否启用" />
        <Column Name="IPWhite" DataType="String" Description="IP白名单。支持多个，以逗号分隔" />
        <Column Name="UName" DataType="String" Description="联系人" />
        <Column Name="Phone" DataType="String" Description="联系电话" />
        <Column Name="UId" DataType="Int32" Description="所属销售" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="AccessId" />
        <Index Columns="Name" />
        <Index Columns="UId" />
      </Indexes>
    </Table>
    <Table Name="DeviceDataPush" TableName="DH_DeviceDataPush" Description="上报设备数据" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" PrimaryKey="True" DataScale="time" Description="编号" />
        <Column Name="DeviceName" DataType="String" Master="True" Nullable="False" Description="设备DeviceName" />
        <Column Name="Content" DataType="String" Length="100" Nullable="False" Description="内容" />
        <Column Name="ReportTime" DataType="DateTime" Description="上报时间" />
        <Column Name="DType" DataType="Int32" Description="类型" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="DeviceName" />
        <Index Columns="ReportTime" />
      </Indexes>
    </Table>
    <Table Name="RenewalPay" TableName="DH_RenewalPay" Description="续期支付表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" PrimaryKey="True" DataScale="time" Description="编号" />
        <Column Name="TradeNo" DataType="String" Description="支付订单号" />
        <Column Name="Devices" DataType="String" Nullable="False" Description="设备DeviceName" />
        <Column Name="RenewalYear" DataType="Int32" Description="续期年限" />
        <Column Name="Amount" DataType="Decimal" Description="金额" />
        <Column Name="Status" DataType="Int32" Description="状态(0待支付 1支付成功 2支付失败 3已取消)" />
        <Column Name="PayWay" DataType="Int32" Description="支付方式(1支付宝 2微信)" />
        <Column Name="PayTime" DataType="DateTime" Description="支付时间" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
    </Table>
    <Table Name="RenewalYear" TableName="DH_RenewalYear" Description="续期年限表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" PrimaryKey="True" DataScale="time" Description="编号" />
        <Column Name="Year" DataType="Int32" Description="续期年限" />
        <Column Name="Amount" DataType="Decimal" Description="金额" />
        <Column Name="Quantity" DataType="Int32" Description="数量" />
        <Column Name="Discount" DataType="Decimal" Description="折扣金额" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
    </Table>
  </Tables>
</EntityModel>