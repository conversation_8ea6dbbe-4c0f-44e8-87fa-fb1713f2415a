@{

}

<script asp-location="Head">
    var layuiNumber = '@T("个")';
    var pleaseSelect = '@T("请选择")';
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPage = '@T("页")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
    var layuiCloseCurrent = '@T("关 闭 当 前")';
    var layuiCloseOther = '@T("关 闭 其 他")';
    var layuiCloseAll = '@T("关 闭 全 部")';
    var layuiMenuStyle = '@T("菜单风格")';
    var layuiTopStyle = '@T("顶部风格")';
    var layuiThemeColor = '@T("主题配色")';
    var layuiMoreSettings = '@T("更多设置")';
    var layuiOpen = '@T("开")';
    var layuiClose = '@T("关")';
    var layuiMenu = '@T("菜单")';
    var layuiView = '@T("视图")';
    var layuiBanner = '@T("通栏")';
    var layuiThroughColor = '@T("通色")';
    var layuiFooter = '@T("页脚")';
    var layuiSelectAll = '@T("全选")';
    var layuiClear = '@T("清空")';
    var layuiReverseSelection = '@T("反选")';
    var layuiPeeling = '@T("换肤")';
    var layuiNoDataYet = '@T("暂无数据")';
    var layuiSearch = '@T("搜索")';
    var layuiPrevious = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNotAllowClose = '@T("前页面不允许关闭")';
    var layuiOpenAtMost = '@T("最多打开")';
    var layuiTabs = '@T("个标签页")';
</script>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    $("body").on("click", function (event) {
        $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
        $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("MultipleList")'
            , page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 60
            , cols: [[
                  { field: 'Id', title: '@T("批次编号")', minWidth: 120 }
                , {
                    field: 'BType', title: '@T("批次类型")', minWidth: 120,
                    templet: function (d) {
                        switch (d.BType) {
                            case 1:
                                return '@T("验证固件")';
                            case 2:
                                return '@T("批量升级")';
                            default:
                                return '';
                        }
                    }
                }
                , {
                    field: 'UType', title: '@T("升级策略")', minWidth: 120,
                    templet: function (d) {
                        switch (d.UType) {
                            case 1:
                                return '@T("静态升级")';
                            case 2:
                                return '@T("动态升级")';
                            default:
                                return '';
                        }
                    }
                }
                , { field: 'Status', title: '@T("执行状态")', width: 120,
                    templet: function (d) {
                        switch (d.Status) {
                            case 0:
                                return '@T("未开始")';
                            case 1:
                                return '@T("进行中")';
                            case 2:
                                return '@T("已完成")';
                            default:
                                return '';
                        }
                    }
                }
                , {
                    field: 'CreateTime', title: '@T("创建时间")', width: 180, templet: function (d) {
                        if (d.CreateTime === null || parseInt(d.CreateTime, 10).toString().substring(0, 4) < '2001') {
                            return "";
                        } else {
                            return d.CreateTime;
                        }
                    }
                }
                , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: 60 }
            ]]
            , limit: 15
            , limits: [10, 15, 20, 30, 50, 100]
            , height: 'full-80'
            , id: 'tables'
            , skin:'line'
            , where: {
                id: '@Model.Id'
            }
        });

        window.active = {
            reload: function () {
                // 第一步：先保存上次输入的值
                let searchData = $("#key").val()
                table.reload('tables', {

                    where: {
                        id: '@Model.Id',
                        key: $("#key").val(),
                    },
                    page: {
                        curr: 1
                    }
                });
                // 第二步：重新渲染回input
                $("#key").val(searchData)
                $("#key").focus()// 第三步：重新渲获取焦点
                setTimeout(function () {
                    $("#key").on("input", function (e) {// 第四步：重新监听
                        active.reload();
                        // console.log('监听输入', $("#key").val());
                    });
                }, 100); // 延迟100毫秒再监听输入框的input事件
            }
        };

        $("#key").on("input", function (e) {
            active.reload();
        });

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if(obj.event === 'selects'){
                window.selects(data)
            }
        });

        table.on('toolbar(tool)', function (obj) {
            var event = obj.event;
            console.log(event);
            if (event === 'update') {
                window.update()
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });
        window.update = function () {
            top.layui.dg.popupRight({
                id: 'update'
                , title: ' @T("批量升级")'
                , closeBtn: 1
                , area: ['620px']
                , success: function () {
                //$('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Update")" frameborder="0" class="layadmin-iframe"></iframe>');
                     $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Update")' + abp.utils.formatString("?id={0}", '@Model.Id') + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }

        window.selects = function (data) {
            top.layui.dg.popupRight({
                id: 'select'
                , title: ' @T("批次任务明细")'
                , closeBtn: 1
                , area: ['780px', '100%']
                , success: function () {
                    console.log(data)
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("BatchDetails")' + abp.utils.formatString("?id={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }
    });
    
</script>
<script type="text/html" id="tool">
    <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="selects"> @T("查看")</a>
</script>
<script type="text/html" id="switchTpl">
    <input type="checkbox" name="status" data-id="{{d.Id}}" lay-skin="switch" disabled lay-text="ON|OFF" lay-filter="statusedit" {{ d.Enable == "1" ? 'checked' : ''}}>
</script>

<style asp-location="true">
</style>
<script type="text/html" id="user-toolbar">
    <div style="display:flex;align-items:center;height:50px;padding:5px;margin-top:-10px;">
        <button class="pear-btn pear-btn-warming pear-btn-md" lay-event="update">
            <i class="layui-icon layui-icon-util"></i>
            @T("批量升级")
        </button>
        <form class="layui-form dg-form" style="margin-left:10px;">
            <div class="layui-form-item" id="search">
                <div style="display:flex;align-items:center;">
                    <div style="margin-right:5px;">@T("关键词")：</div>
                    <div class="layui-input-inline">
                        <input type="text" style="width:300px;" name="key" id="key" placeholder="@T("批次编号")" autocomplete="off" class="layui-input" lay-filter="key">
                    </div>
                </div>
            </div>
        </form>
    </div>
</script>