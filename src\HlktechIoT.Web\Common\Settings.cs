﻿using NewLife.Configuration;

using System.ComponentModel;

namespace HlktechIoT.Common;

[DisplayName("基类设置")]
[Config("Settings")]
public class Settings : Config<Settings>
{
    [DisplayName("支付宝私钥")]
    public String? AliPrivateKey { get; set; } = "MIIEpgIBAAKCAQEAlhs5y7n9SyxY9aTsF9rA76K26tUCibrV7zgIvf0Uzc7uy8V3SBcwmZmyRK0tbYiNm0mAndh5UGEgQlYrA3l9GUvarmABuLHGemlJ6gqSWRc8TpOHmt71mn63/NshzLcSuxfRbGqWlSE/5xqyUNKS2tP6klgnKSjYt6BU33EbAEMCsM/tl1eyFRZAB446vGhaOp/U8yMwGzA2t01gdPKPjuwZwCCyWyzgVYl6PFrcrj8ki2T9T5MMFmlew2STdPEu45g/YWNF5lDOZNp6zydI3Mv6po0dkqqFo1xF45mkrlPTUGTgigt9geatSsM5zLssxV3VXpGWXTDp6n4oFGfeiwIDAQABAoIBAQCU0klrAutuLHnGkczBmdDPxbM1ABuUEfkpQ5TnSJPnuNJolU692sAEia2FCW2Qu2IqI6Ww2X7tgl5JZsUenMS638ngOK98PkVZ+CmgMPsH+VGBViOMGKHImvovZ9MK9DdN5z9rDgJduKlyp7VDDIMtEh3MGjo5RJYY+I0tOyZaCogthCE0WdM/9+9GUWmYCcK8XH9AQtz7oS9qAnLW9OpppqXx5iXGLxMJKOUzvP174GqH7TQEIfF/35y9AWL2sI7uy36E8Aqe8x2gKHHXYbEti0F9uTrOF29HaZ/8tcjPVQMdHVMkk7H4thDIP/BpixJoSudlsofmW7Eb278EheIBAoGBAPOCgA1xQzx3R7Qcf+HBGtgTInF++mxad2so4060QQe9wDtJsFqPhpUdrKrkVhsg7zL+3yc3D1chImoLBrMLW3rL9g1O9CDJrsuEqzI+IaohP7VVmNYxgSgeQP1z5hc4KpbsbZlgipPR+oqyZHLv7Kgo9yHzrkfbVyowwZ6HYbRbAoGBAJ3OQXHcWCINNxFQm86C8uEhIDwRPkWOLAyhkhx+yZsc0EaBfGqITiEjlILUAcnWkkxKrhIqlUXAMbmqkZGE7f+dfJJ019qap/NK0nuQwYErpQx7pvW3M1X1uLlwj6luOg69+8Cxc3W10P25Zk6saSr06wUtIkqwnHIogf6c09WRAoGBANLP+s4obR4O4duzBJ9MD1JbFmmNlX1ywIncJxW/BcGO67ooLG3qbYfw3chAz67Cz3KlpkmL6FL1E5ccnpsozPS9WPHilYVBNXtqCs//bCOej0oMpblhuEAkGP2TdfJKJMzuOGAdbTtWAQJMOJwszAQ1Guag0RPSd17GQQleiR6bAoGBAI6d4l2h0vDrWpJGdJl9lT3Lb1N6DfpUlBDSULd6CoAp2T1vNt8c0ud2PXjDW95rEorqj0kRvRhtq0LuGWMXbPSwAu5ltNL3JMRnIbTUzV0M8bxHYEv/RuDwEwNc7Ckm2keKgaTTetKt//Peg8ykWuOct25VLQGtfaZaX6mZWIVRAoGBAOXsok2hhtB1sevSwzaVF7+RsgjGHOb8qcUTALMe4Wg7DeB8nGjrCwD14V6AHgdtjqAC77mv38nnOBT3Okl+7l6jHjtEf2EJ78PlgkPh+sedaWoBZBoCPAhZx0zyvfSn3MP7pwJetKrmYor6k+2MtgnKwJjJOlKj5DsHDkuHEP4u";

    [DisplayName("支付宝公钥")]
    public String? AliPublicKey { get; set; } = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxEJhVCvqqjeZ9F5xIQsZcoa8HXe5EUVj3BpF8O/hjB7xPSJ219LqqXQ8DOpVJKqr0n5fTcLJa68pfHlXJqqV6i1NsdeMbGpg+LXXebiLGaEoZpKSk/o32WuYZmgL7QH0sHxx+fKip4+PlcmbnINCXiwx0p+qVfZRRJU/R5JwalyKjkHViTSThDsPTDB8dDuIsBbBh9YyjFt+t2sMUGM9b21SOD2ptYXCQPGN6ciXbDa3v3C2HY6hU6nz/k6QtnzUhKSQXlhv/pi5+8FETvcGO+9RNaSVYewYOuPTmsM7Z7Hln7aZDr0OCQpOuCVtVO/Gq3a/au7BJaiGqWcgEatfkwIDAQAB";

    //https://openapi.alipay.com/gateway.do
    [DisplayName("支付宝网关")]
    public String? AliServerUrl { get; set; } = "https://openapi.alipay.com/gateway.do";

    [DisplayName("支付宝AppId")]
    public String? AliAppId { get; set; } = "2021001160645030";

    [DisplayName("支付宝商户Id")]
    public String? AliSellerId { get; set; } = "2088121354668703";

    [DisplayName("微信AppId")]
    public String? WxAppId { get; set; } = "wx2800276154780d44";

    [DisplayName("微信商户号")]
    public String? WxMchId { get; set; } = "1428567302";

    [DisplayName("微信私钥")]
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

    [DisplayName("微信AppSecret")]
    public String? WxAppSecret { get; set; } = "5b88d0d8a0cb1c5ac799aba9f7773b38";

    [DisplayName("微信商户API证书序列号")]
    public String? WxSerialNumber { get; set; } = "287A3B01F4AD29AE9F67341984440FC40D5628F3";

    [DisplayName("微信AppName")]
    public String? WxAppName { get; set; } = "海凌科电子";

    [DisplayName("回调地址")]
    public String? NotifyUrl { get; set; } = "https://cloud.hlktech.com";

    /// <summary>
    /// 允许直接访问的IP地址
    /// </summary>
    [Description("允许直接访问的IP地址")]
    public String AllowIps { get; set; } = "***********,**************";
}
