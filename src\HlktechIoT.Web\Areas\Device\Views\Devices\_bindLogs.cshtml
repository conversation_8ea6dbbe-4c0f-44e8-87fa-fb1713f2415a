﻿@using System.Collections;
@using DH.Helpers;
@{
    var dic = ViewBag.dic as Dictionary<String, String>;
    //var listThingFields = Model.ThingFields as IList<ThingFields>;
}
<div class="layui-row layui-col-space15">
    <div class="layui-col-md12">
        <div class="layui-card">
            <div class="layui-card-body top0" style="padding:0;">
                <div class="dgtable-body Removepadding" style="margin-top: 0px; padding-top: 15px;">
                    <div class="layui-form" style="display: block; width: 100%; margin-bottom: 20px;">
                        @T("按日期查询：")
                        <div class="layui-inline">
                            <input type="text" name="date" id="datetimes2" lay-verify="date" placeholder="yyyy-MM-dd" autocomplete="off" class="layui-input text1 text2">
                        </div>
                        &nbsp;&nbsp;@T("至")&nbsp;&nbsp;
                        <div class="layui-inline">
                            <input type="text" name="date" id="datetimes3" lay-verify="date" placeholder="yyyy-MM-dd" autocomplete="off" class="layui-input text1 text2">
                        </div>&nbsp;&nbsp;
                        <div class="layui-inline">
                            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="SearchTime2()">@T("搜索")</button>
                        </div>

                       <div class="layui-inline" style="float:right">
                            @* <a id="InverterExport" class="layui-btn layui-btn-primary layui-btn-sm" onclick="Export()">@T("导出")</a> *@
                        </div>
                    </div>

                    <div>
                        <table class="" id="tablist2" lay-filter="tool"></table>
                    </div>
                    <script type="text/html" id="tool">
                        <a class="layui-btn layui-btn-xs" lay-event="look"> @T("查看")</a>
                        @if (this.Has((PermissionFlags)8))
                        {
                            <a class="layui-btn layui-btn-xs" lay-event="del"> @T("删除")</a>
                        }
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>