﻿@{
    Html.AppendTitleParts(T("设备属性历史数据").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    .layui-layer-title {
        background-color: #fff;
    }

    .dtree-nav-item {
        padding-left: 0;
    }

    .layui-table-tool {
        z-index: 1;
    }
</style>

<form class="layui-form dg-form">
    <div class="layui-form-item-his" id="search" style="margin-bottom: 3px;">
        <div class="layui-inline" style="margin-left:10px;">
            <div class="layui-input-inline">
                <div id="demo1" style="width: 150px;"></div>
            </div>
        </div>
        <div class="layui-inline" id="selectTime" style="display:none;">
             <label class="layui-form-label" style="width: auto">@T("筛选时间"):</label>
                
                <div class="layui-input-inline">
                    <input type="text" name="start" id="start" readonly placeholder="@T("开始时间")" autocomplete="off" class="layui-input">
                </div>
                 -
                <div class="layui-input-inline">
                    <input type="text" name="end" id="end" readonly placeholder="@T("结束时间")" autocomplete="off" class="layui-input">
                </div>
            @* <div class="layui-inline" style="margin-left:40px;">
                <label class="layui-form-label"> @T("关键词")：</label>
                <div class="layui-input-inline">
                    <input type="text" name="key" id="HistoricalDatakey" placeholder="@T("搜索..")" autocomplete="off" class="layui-input">
                </div>
            </div> *@

            <div class="layui-inline">
                <button type="button" class="layui-btn layui-btn-sm" id="SearchHistoricalData">@T("搜索") </button>
            </div>

            
        </div>
    </div>
</form>


<div class="layui-card-his">
    <div class="layui-card-body-his">
        <table class="layui-hide" id="historicalDataTable" lay-filter="tool2"></table>
    </div>
</div>


<script asp-location="Footer">
    $("body").on("click", function (event) {
        $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
        $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon', 'dtree'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;
        var laydate = layui.laydate;
        window.active = {
            reload3: function () {
                table.reload('historicalData_TableId',
                    {
                        where: {
                            deviceId: '@Model.DeviceId',
                            names: '@Model.Name',
                            start: $("#start").val(),
                            end: $("#end").val(),
                        },
                        page: {
                            curr: 1
                        }
                    });
            }
        };
          table.render({
                elem: '#historicalDataTable'
                , url: '@Url.Action("GetHistoryDataList")'
                , page: true //开启分页
                , toolbar: '#user-toolbar'
                  , defaultToolbar: [{
                    title: '@T("刷新")',
                    layEvent: 'refresh',
                    icon: 'layui-icon-refresh',
                }, 'filter', 'print']
                , cellMinWidth: 80
                , cols: [[
                    { field: 'Name', title: '@T("名称")', minWidth: 140 }
                    , { field: 'Kind', title: '@T("类型")', width: 100 }
                    , { title: '@T("内容")', minWidth: 100,templet:(d)=>{
                            if (d.Value != '' && d.Value != null) {
                                return `
                                <div class="noneHover" lay-event="readDetail_tool2" style="cursor:pointer;color:rgb(50,50,50)"> ${d.Value}</div>
                                `; // 禁止溢出显示
                            }
                            else {
                                return '';
                            }
                        } }
                    , { field: 'Timestamp', title: '@T("设备数据时间")', width: 164, align: 'left' }
                    , { field: 'Creator', title: '@T("创建者")', minWidth: 200  }
                    , { field: 'CreateTime', title: '@T("创建时间")', width: 164  }
                    , { field: 'CreateIP', title: '@T("创建IP地址")', width: 140  }

                ]]
                , limit: 13
                , limits: [10, 13, 20, 30, 50, 100]
                , height: 'full-160'
                , id: 'historicalData_TableId'
                , where:{
                    deviceId: '@Model.DeviceId',
                    names: '@Model.Name'
                }
            });

        $("#SearchHistoricalData").click(function (e) {
            active.reload3();
        });
        table.on('toolbar', function (obj) {
            console.log(obj);
            if (obj.event === 'select') {
                window.select();
            } else if (obj.event === 'refresh') {
                active.reload3();
            }
        });

        // 静态搜索
        xmSelect.render({
            el: '#demo1',
            radio: true,
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            clickClose: true,
            multiple: false, // 开启多选功能
            data: [{ name: '@T("1小时")', value: -1 }, { name: '@T("24小时")', value: 0 }, { name: '@T("7天")', value: 1 }, { name: '@T("自定义")', value: 2 }],
            on: function (data) {  // 监听选择
                if (data.arr.length > 0) {
                    var a = data.arr[0].value;
                    if (a == -1) {
                        $("#start").val(getCurrentDate(1));
                        $("#end").val(getCurrentDate(0));
                        $("#selectTime").hide();
                    } else if (a == 0) {
                        $("#start").val(getCurrentDate(2));
                        $("#end").val(getCurrentDate(0));
                        $("#selectTime").hide();
                    } else if (a == 1) {
                        $("#start").val(getCurrentDate(3));
                        $("#end").val(getCurrentDate(0));
                        $("#selectTime").hide();
                    } else {
                        $("#selectTime").show();
                    }
                }
                if (data.change.length > 0) {
                    active.reload3();
                }
            }
        });
        
        table.on('tool(tool2)', function (obj) {
                if (obj.event === 'readDetail_tool2') {
                    // console.log('查看详情：',obj);
                    window.readDetail_tool2(obj.data);
                }
            });

        window.readDetail_tool2 = (data) => {
            // 在此处输入 layer 的任意代码
            layer.open({
                type: 1, // page 层类型
                area: ['420px', '300px'],
                title: '@T("查看详情")',
                shade: 0.1, // 遮罩透明度
                shadeClose: true, // 点击遮罩区域，关闭弹层
                maxmin: true, // 允许全屏最小化
                anim: 0, // 0-6 的动画形式，-1 不开启
                content: `<div style="max-width:400px;margin:10px;word-wrap: break-word;line-height:20px;"> ${data.Value} </div>`
            });
        }

        // 获取当前日期
        function getCurrentDate(val) {
            var today = new Date();
            if (val == 1) {
                today.setHours(today.getHours() - 1);
            } else if (val == 2) {
                today.setDate(today.getDate() - 1);
            } else if (val == 3) {
                today.setDate(today.getDate() - 7);
            }
            var year = today.getFullYear();
            var month = String(today.getMonth() + 1).padStart(2, '0');
            var day = String(today.getDate()).padStart(2, '0');
            var hours = String(today.getHours()).padStart(2, '0');
            var minutes = String(today.getMinutes()).padStart(2, '0')
            var seconds = String(today.getSeconds()).padStart(2, '0')
            var currentDate = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
            return currentDate;
        }
        // 设置默认时间
        //$("#start").val(getCurrentDate());
        //$("#end").val(getCurrentDate());
        //时间插件
        var startDate = laydate.render({
            elem: '#start',
            btns: ['clear', "confirm", 'now'],//只显示清空和确定按钮
            type: 'datetime',       // 设置日期选择类型为年月-日
            format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月-日
            done: function (value, date) {
                console.log('选择后:', value + '-' + date.date);

                $("#start").val(value);
                checkDateValidity();
            },
            choose: function (date) {
                // 用户选择日期的回调函数
                // 在这里可以处理用户选择日期后的逻辑
                laydate.close(); // 关闭日期选择器弹窗
            }
        });

        var endDate = laydate.render({
            elem: '#end',
            btns: ["clear", "confirm", 'now'],
            type: 'datetime',       // 设置日期选择类型为年月-日
            format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月-日
            done: function (value, date) {
                console.log('选择后:', value + '-' + date.date);

                $("#end").val(value);
                checkDateValidity();
            },
            choose: function (date) {
                // 用户选择日期的回调函数
                // 在这里可以处理用户选择日期后的逻辑
                laydate.close(); // 关闭日期选择器弹窗
            }
        });

        function checkDateValidity() {
            var startValue = $("#start").val();
            var endValue = $("#end").val();

            if (startValue && endValue) {
                var startDate = new Date(startValue + "-01");  // 这里是格式化-作-可对比的日期---格式
                var endDate = new Date(endValue + "-01");

                if (startDate > endDate) {
                    os.warning('开始时间不能晚于结束时间，请重新选择。');
                    $("#start").val(""); // 清空开始时间输入框
                    $("#end").val("");   // 清空结束时间输入框
                }
            }
        }
      });

</script>