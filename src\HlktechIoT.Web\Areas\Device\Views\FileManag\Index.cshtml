﻿@{
    Html.AppendTitleParts(T("模组固件管理").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    .layui-layer-title {
        background-color: #fff;
    }

    .dtree-nav-item {
        padding-left: 0;
    }

    .layui-table-tool {
        z-index: 1;
    }

    /* 操作列自适应样式 */
    .layui-table-fixed-r .layui-table-body {
        overflow-x: hidden !important;
    }

    .operation-column {
        white-space: nowrap;
        padding: 0 2px; /* 减少左右边距，让按钮更靠近边界 */
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center; /* 垂直居中 */
        flex-wrap: nowrap;
        height: 100%; /* 确保容器占满单元格高度 */
        width: 100%; /* 确保容器占满单元格宽度 */
        box-sizing: border-box; /* 确保内边距不增加总宽度 */
    }

        .operation-column .pear-btn {
            margin-right: 4px; /* 减少按钮间距 */
            padding: 0 10px; /* 稍微减少按钮内边距 */
            height: 30px; /* 保持按钮高度 */
            line-height: 30px; /* 行高与高度一致 */
            min-width: 50px; /* 保持按钮最小宽度 */
            text-align: center;
            display: inline-block; /* 改为inline-block，避免flex导致的布局问题 */
            vertical-align: middle; /* 垂直居中 */
            white-space: nowrap; /* 防止文字换行 */
            box-sizing: border-box; /* 确保padding计算在内 */
        }

            .operation-column .pear-btn:last-child {
                margin-right: 0;
            }

    /* 确保表格单元格内容居中 */
    .layui-table-cell {
        text-align: center;
        padding: 0 2px; /* 保持单元格内边距 */
    }

    /* 固定列样式优化 */
    .layui-table-fixed {
        height: auto !important;
    }

    /* 确保表格右侧固定列正确显示 */
    .layui-table-fixed-r {
        right: 0 !important;
    }

        /* 优化表格右侧固定列的单元格 */
        .layui-table-fixed-r .layui-table-cell {
            padding: 0;
        }
</style>

<form class="layui-form dg-form">
    <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width: auto">@T("关键词")：</label>
            <div class="layui-input-inline">
                <input type="text" name="key" id="key" placeholder="@T("文件名、版本、备注、PK")" autocomplete="off" class="layui-input" lay-filter="key">
            </div>
        </div>
        <div class="layui-inline select">
            <label class="layui-form-label" style="padding-top: 10px;">@T("固件类型")：</label>
            <div class="layui-input-inline ">
                <input type="hidden" value="" id="PType" name="PType" />
                <div id="demo1" style=" width: 100%;"></div>
            </div>
        </div>
    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    $("body").on("click", function (event) {
        $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
        $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon', 'dtree'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;
        var dtree = layui.dtree;

        // 按钮配置集中定义
        var operationButtons = [
            @if (this.Has((PermissionFlags)4))
            {
                @:{ text: '@T("编辑")', event: 'edit', class: 'pear-btn-primary' },
            }
            @if (this.Has((PermissionFlags)8))
            {
                @:{ text: '@T("删除")', event: 'del', class: 'pear-btn-danger' },
            }
            { text: '@T("批量升级")', event: 'multipleUpdate', class: 'pear-btn-warming' },
            { text: '@T("查看")', event: 'MultipleList', class: 'pear-btn-warming' }
        ];

        // 将按钮配置保存到全局变量，供模板使用
        window.operationButtons = operationButtons;

        // 动态计算操作列宽度的函数
        function calculateOperationColumnWidth() {
            try {
                var buttonMargin = 4; // 减少按钮间距，对应CSS中的margin-right: 4px
                var extraPadding = 8; // 减少额外边距，对应CSS中的padding: 0 2px
                var baseButtonPadding = 20; // 调整按钮内边距，对应CSS中的padding: 0 10px

                if (operationButtons.length === 0) {
                    return 100;
                }

                var totalWidth = 0;

                operationButtons.forEach(function(button) {
                    // 优化文字宽度估算：中文字符约17px，英文字符约10px
                    var text = button.text;
                    var textWidth = 0;
                    for (var i = 0; i < text.length; i++) {
                        var char = text.charAt(i);
                        if (/[\u4e00-\u9fa5]/.test(char)) {
                            textWidth += 17; // 中文字符宽度
                        } else {
                            textWidth += 10;  // 英文字符宽度
                        }
                    }

                    // 确保按钮最小宽度
                    var buttonWidth = Math.max(textWidth + baseButtonPadding, 50);
                    totalWidth += buttonWidth;
                });

                // 加上按钮间距和额外边距
                totalWidth += (operationButtons.length - 1) * buttonMargin + extraPadding;

                // 设置最小宽度
                if (totalWidth < 120) {
                    totalWidth = 120;
                }

                console.log('计算的模组固件管理操作列宽度:', totalWidth, '按钮数量:', operationButtons.length, '按钮配置:', operationButtons);
                return totalWidth;
            } catch (error) {
                console.error('计算操作列宽度时出错:', error);
                return 260; // 返回原来的固定宽度作为备用
            }
        }

        // 应用操作列宽度的函数
        function applyOperationColumnWidth() {
            try {
                var calculatedWidth = calculateOperationColumnWidth();

                var fixedRightHeader = $('.layui-table-fixed-r .layui-table-header table thead tr th:last-child');
                var fixedRightBody = $('.layui-table-fixed-r .layui-table-body table tbody tr td:last-child');

                if (fixedRightHeader.length > 0) {
                    fixedRightHeader.css('width', calculatedWidth + 'px');
                    fixedRightHeader.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                if (fixedRightBody.length > 0) {
                    fixedRightBody.css('width', calculatedWidth + 'px');
                    fixedRightBody.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                var mainTableHeader = $('#tablist').next('.layui-table-view').find('.layui-table-header table thead tr th:last-child');
                var mainTableBody = $('#tablist').next('.layui-table-view').find('.layui-table-body table tbody tr td:last-child');

                if (mainTableHeader.length > 0) {
                    mainTableHeader.css('width', calculatedWidth + 'px');
                    mainTableHeader.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                if (mainTableBody.length > 0) {
                    mainTableBody.css('width', calculatedWidth + 'px');
                    mainTableBody.find('.layui-table-cell').css('width', calculatedWidth + 'px');
                }

                console.log('已应用模组固件管理操作列宽度:', calculatedWidth);
            } catch (error) {
                console.error('应用操作列宽度时出错:', error);
            }
        }

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetList")'
            , page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 80
            , cols: [[
                { field: 'Id', title: '@T("ID")' }
                , { field: 'OriginFileName', title: '@T("文件名")', minWidth: 200 }
                , { field: 'FileSize', title: '@T("文件大小")', width: 110 }
                , { field: 'DataSize', title: '@T("固件大小")', width: 110 }
                , { field: 'SVersions', title: '@T("版本号")', minWidth: 200 }
                , { field: 'ProductKey', title: '@T("PK")', minWidth: 140 }
                , {
                    field: 'FileUrl', title: '@T("文件路径")', minWidth: 300, templet: (d) => {
                        return `<a href="/${d.FileUrl}" download="${d.OriginFileName}">${d.FileUrl}</a>`
                    }
                }
                , { field: 'FType', title: '@T("固件类型")', width: 180, templet: function (d){
                    if(d.FType == 0){
                        return `@T("正式固件")`;
                    }else{
                        return `@T("测试固件")`;
                    }
                } }
                , { field: 'UpdateTime', title: '@T("更新时间")', width: 180 }
                , { field: 'Remark', title: '@T("备注")', minWidth: 180 }
                , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: calculateOperationColumnWidth() }
            ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-100'
            , id: 'tables'
            , done: function() {
                try {
                    // 延迟执行，确保DOM已经渲染完成
                    setTimeout(function() {
                        applyOperationColumnWidth();
                        console.log('模组固件管理表格渲染完成，已应用操作列样式');
                    }, 200);
                } catch (error) {
                    console.error('表格done回调中出错:', error);
                }
            }
        });

        var demo1 = xmSelect.render({
            el: '#demo1',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            clickClose: true,
            multiple:false, // 开启多选功能
            data:[
                {name:'@T("正式固件")',value:0},
                {name:'@T("测试固件")',value:1}
            ],
            on: function (data) {  // 监听选择
                console.log(111,data);
                if (data.arr.length>1) {
                    data.arr.splice(0,1)
                }
                if (data.arr.length > 0) {
                    var a = "";
                    for (var i = 0; i < data.arr.length; i++) {
                        if (i == 0) {
                            a = data.arr[i].value;
                        }
                        else {
                            a += "," + data.arr[i].value;
                        }
                    }
                    $("[name=PType]").val(a);
                }
                else {
                    $("[name=PType]").val("");
                }

                if (data.change.length > 0) {
                    active.reload();
                }
            }
        });

        window.active = {
            reload: function () {
                table.reload('tables',
                    {
                        where: {
                            key: $("#key").val(),
                            PType: $("#PType").val(),
                        },
                        page: {
                            curr: 1
                        },
                        done: function(res, curr, count) {
                            try {
                                setTimeout(function() {
                                    applyOperationColumnWidth();
                                    console.log('模组固件管理表格重载完成，已重新应用操作列样式');
                                }, 200);
                            } catch (error) {
                                console.error('表格重载done回调中出错:', error);
                            }
                        }
                    });
            }
        }

        $("#key").on("input", function (e) {
            active.reload();
        });

        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("Delete")', { Id: data.Id }, function (data) {
                        if (data.success) {
                            abp.notify.success(data.msg);
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            } else if (obj.event === 'edit') {
                window.edit(data);
            }else if( obj.event === 'multipleUpdate'){
                window.multipleUpdate(data)
            } else if (obj.event === 'MultipleList') {
                window.MultipleList(data)
            }
        });

        window.saveCallback = function (data) {
            parent.layer.close(data.index);
            abp.notify.success(data.msg);
            table.reload("tables");
        }

        window.add = function () {
            top.layui.dg.popupRight({
                id: 'FileAdd'
                , title: ' @T("上传固件")'
                , closeBtn: 1
                , area: ['580px']
                , success: function () {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Add")" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }

        window.edit = function (data) {
            top.layui.dg.popupRight({
                id: 'FileAdd'
                , title: ' @T("编辑固件")'
                , closeBtn: 1
                , area: ['580px']
                , success: function () {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Edit")' + abp.utils.formatString("?id={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }

        window.multipleUpdate = function (data) {
            top.layui.dg.popupRight({
                id: 'MultipleUpdate'
                , title: ' @T("批量升级固件")'
                , closeBtn: 1
                , area: ['720px','100%']
                , success: function () {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Update")' + abp.utils.formatString("?id={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }

        window.MultipleList = function (data) {
            top.layui.dg.popupRight({
                id: 'MultipleList'
                , title: ' @T("批次管理")'
                , closeBtn: 1
                , area: ['780px', '100%']
                , success: function () {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("MultipleUpdate")' + abp.utils.formatString("?id={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }
        //监听开关事件
        form.on('switch(statusedit)', function (data) {
            //开关是否开启，true或者false
            var checked = data.elem.checked;
            //获取所属属性值
            var id = data.elem.attributes['data-id'].nodeValue;
            $.post('@Url.Action("ModifyState")', { id: id, status: checked }, function (res) {
                if (res.success) {
                    abp.notify.success(res.msg);
                }
                else {
                    abp.notify.error(res.msg);
                }
            });
        });

        window.warning = function (msg) {
            os.warning(msg);
        }
    });
</script>

<script type="text/html" id="tool">
    <div class="operation-column">
    {{#  layui.each(window.operationButtons, function(index, button){ }}
        <a class="pear-btn {{button.class}} pear-btn-xs" lay-event="{{button.event}}">{{button.text}}</a>
    {{#  }); }}
    </div>
</script>

<script type="text/html" id="user-toolbar">
    @if (this.Has((PermissionFlags)2)){
    <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon  layui-icon-add-1"></i>
        @T("上传")
    </button>
    }
</script>