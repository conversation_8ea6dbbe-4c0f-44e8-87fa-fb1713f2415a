{
  // 微信支付
  // 更多配置，请查看WeChatPayOptions类
  "WeChatPay": {
    // 应用号
    // 如：微信公众平台AppId、微信开放平台AppId、微信小程序AppId、企业微信CorpId等
    "AppId": "",

    // 商户号
    // 为微信支付商户平台的商户号
    "MchId": "",

    // API密钥
    // 为微信支付商户平台的API密钥，请注意不是APIv3密钥
    "APIKey": "",

    // APIv3密钥
    // 为微信支付商户平台的APIv3密钥，请注意不是API密钥，v3接口必填
    "APIV3Key": "",

    // 支付分服务ID
    // 要调用微信支付分接口必须配置
    "PayScoreServiceId": "",

    // API证书(.p12)
    // 为微信支付商户平台的API证书(.p12)，v3接口必填
    // 可为证书文件(.p12)路径 / 证书文件(.p12)的base64字符串
    "Certificate": "",

    // RSA公钥
    // 目前仅调用"企业付款到银行卡API"时使用，执行本示例中的"获取RSA加密公钥API"即可获取。
    "RsaPublicKey": ""
  },

  // 支付宝
  // 更多配置，请查看AlipayOptions类
  "Alipay": {

    // 注意: 
    // 若涉及资金类支出接口(如转账、红包等)接入，必须使用“公钥证书”方式。不涉及到资金类接口，也可以使用“普通公钥”方式进行加签。
    // 本示例默认的加签方式为“公钥证书”方式，并调用 CertificateExecuteAsync 方法 执行API。
    // 若使用“普通公钥”方式，除了遵守下方注释的规则外，调用 CertificateExecuteAsync 也需改成 ExecuteAsync。
    // 支付宝后台密钥/证书官方配置教程：https://opendocs.alipay.com/open/291/105971
    // 密钥格式：请选择 PKCS1(非JAVA适用)，切记 切记 切记

    // 应用Id
    // 为支付宝开放平台-APPID
    "AppId": "",

    // 支付宝公钥 RSA公钥
    // 为支付宝开放平台-支付宝公钥
    // “公钥证书”方式时，留空
    // “普通公钥”方式时，必填
    "AlipayPublicKey": "",

    // 应用私钥 RSA私钥
    // 为“支付宝开放平台开发助手”所生成的应用私钥
    "AppPrivateKey": "",

    // 服务网关地址
    // 默认为正式环境地址
    "ServerUrl": "https://openapi.alipay.com/gateway.do",

    // 签名类型
    // 支持：RSA2(SHA256WithRSA)、RSA1(SHA1WithRSA)
    // 默认为RSA2
    "SignType": "RSA2",

    // 应用公钥证书
    // 可为证书文件路径 / 证书文件的base64字符串
    // “公钥证书”方式时，必填
    // “普通公钥”方式时，留空
    "AppPublicCert": "",

    // 支付宝公钥证书
    // 可为证书文件路径 / 证书文件的base64字符串
    // “公钥证书”方式时，必填
    // “普通公钥”方式时，留空
    "AlipayPublicCert": "",

    // 支付宝根证书
    // 可为证书文件路径 / 证书文件的base64字符串
    // “公钥证书”方式时，必填
    // “普通公钥”方式时，留空
    "AlipayRootCert": ""
  },

  // 银联
  // 更多配置，请查看UnionPayOptions类
  "UnionPay": {
    // 商户代码
    "MerId": "",

    // 签名证书
    "SignCert": "",

    // 签名证书密码
    "SignCertPassword":  ""
  }
}
