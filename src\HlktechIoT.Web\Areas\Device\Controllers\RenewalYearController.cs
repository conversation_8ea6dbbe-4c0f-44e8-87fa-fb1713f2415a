﻿using DG.Web.Framework;
using DH.Entity;
using DH.Helpers;
using HlktechIoT.Data;
using HlktechIoT.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife.Data;
using Pek;
using Pek.Models;
using System.ComponentModel;
using XCode.Membership;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Device.Controllers
{
    [DisplayName("设备续期年限")]
    [Description("设备续期年限")]
    [DeviceArea]
    [DHMenu(9, ParentMenuName = "DeviceManager", CurrentMenuUrl = "~/{area}/RenewalYear", CurrentMenuName = "RenewalYear", LastUpdate = "20250416")]
    public class RenewalYearController : BaseAdminControllerX
    {
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("列表")]
        public IActionResult Index()
        {
            return View();
        }

        public IActionResult GetList(Int32 page,Int32 limit)
        {
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = RenewalYear._.Year,
                Desc = true,
            };

            var data = RenewalYear.Search(0, DateTime.MinValue, DateTime.MinValue, "", pages).Select(e => new
            {
                Id = e.Id.SafeString(),
                e.Year,
                e.Amount,
                e.Quantity,
                e.Discount,
                e.CreateTime,
            });

            return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
        }

        [EntityAuthorize(PermissionFlags.Insert)]
        [DisplayName("新增")]
        public IActionResult Add()
        {
            return View();
        }

        [EntityAuthorize(PermissionFlags.Insert)]
        [DisplayName("新增")]
        [HttpPost]
        public IActionResult Add(Int32 Year, Decimal Amount, Int32 Quantity, Decimal Discount)
        {
            var result = new DResult();

            if (Year == 0)
            {
                result.msg = GetResource("年限不能为空");
                return Json(result);
            }
            if (Amount == 0)
            {
                result.msg = GetResource("价格不能为空");
                return Json(result);
            }
            if (RenewalYear.FindByYear(Year) != null)
            {
                result.msg = GetResource("年限已经存在");
                return Json(result);
            }
            var entity = new RenewalYear
            {
                Year = Year,
                Amount = Amount,
                Quantity = Quantity,
                Discount = Discount
            };
            entity.Insert();
            result.success = true;
            result.msg = GetResource("续期年限新增成功");

            return Json(result);
        }

        [EntityAuthorize(PermissionFlags.Update)]
        [DisplayName("编辑")]
        public IActionResult Edit(Int64 Id)
        {
            var model = RenewalYear.FindById(Id);
            if (model == null)
            {
                return Content(GetResource("续期年限不存在"));
            }
            return View(model);
        }

        [EntityAuthorize(PermissionFlags.Update)]
        [DisplayName("编辑")]
        [HttpPost]
        public IActionResult Edit(Int64 Id,Int32 Year, Decimal Amount, Int32 Quantity, Decimal Discount)
        {
            var result = new DResult();
            var model = RenewalYear.FindById(Id);
            if (model == null)
            {
                return Content(GetResource("续期年限不存在"));
            }
            if (Year == 0)
            {
                result.msg = GetResource("年限不能为空");
                return Json(result);
            }
            if (Amount == 0)
            {
                result.msg = GetResource("价格不能为空");
                return Json(result);
            }
            var exit = RenewalYear.FindByYear(Year);
            if (exit != null && exit.Id != Id)
            {
                result.msg = GetResource("年限已经存在");
                return Json(result);
            }
            model.Year = Year;
            model.Amount = Amount;
            model.Quantity = Quantity;
            model.Discount = Discount;
            model.Update();
            result.success = true;
            result.msg = GetResource("续期年限编辑成功");
            return Json(result);
        }

        [DisplayName("删除")]
        [EntityAuthorize(PermissionFlags.Delete)]
        [HttpPost]
        public IActionResult Delete(Int64 Id)
        {
            var res = new DResult();

            var model = RenewalYear.FindById(Id);
            if (model == null)
            {
                return Content(GetResource("续期年限不存在"));
            }

            model.Delete();

            res.success = true;
            res.msg = GetResource("删除成功");
            return Json(res);
        }

    }
}
