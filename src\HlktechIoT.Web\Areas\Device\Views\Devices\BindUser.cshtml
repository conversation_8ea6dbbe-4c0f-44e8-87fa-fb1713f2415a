@{
    Html.AppendTitleParts(T("设备用户绑定").Text);
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    .layui-form-item {
        margin-top: -25px !important;
        display: block;
        width: 100% !important;
        height: 100% !important;
        font-size: 15px;
        /* border:2px solid ; */
    }

    ._box {
        /* border:2px solid ; */
        display: flex;
        justify-content: center;
        place-items: center;
        padding: 0px
    }
</style>

<form class="layui-form dg-form">
    <div class="" id="search" style="margin-bottom: 3px;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width: auto">@T("关键词")：</label>
            <div class="layui-input-inline">
                <input type="text" name="key1" id="key1" placeholder="@T("IdentityId")" autocomplete="off" class="layui-input" lay-filter="key1">
            </div>
        </div>
    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    $("body").on("click", function (event) {
        $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
        $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetUserList")'
            , page: false //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 60
            , cols: [[
                { field: 'Id', title: '@T("ID")', width: 70 }
                , { field: 'Name', title: '@T("账号")', minWidth: 180 }
                , { field: 'DisplayName', title: '@T("姓名")', minWidth: 180 }
                , { field: 'ProjectName', title: '@T("所属项目")', width: 140 }
                , { field: 'Enable', title: '@T("启用")', templet: '#switchTpl', width: 100 }
                , {
                    title: '@T("在线")', width: 80, templet: (d) => {
                        if (d.Online) {
                            return '<span style="color:mediumseagreen !important;">在线<i class="layui-icon layui-icon-rss" style="margin-left:1px"></i></span>'
                        }
                        return '<span style="color:gray !important;">离线<i class="layui-icon layui-icon-close" style="color:red;margin-left:2px"></i></span>'
                    }
                }
                , { field: 'Mail', title: '@T("邮箱")', minWidth: 180 }
                , { field: 'Mobile', title: '@T("手机号")', minWidth: 160 }
                , { field: 'Role', title: '@T("用户身份")', width: 100 }
                , { field: 'RegisterTime', title: '@T("创建时间")', minWidth: 180 }
                , { field: 'IdentityId', title: '@T("IdentityId")', minWidth: 180 }
                ,{ fixed: 'right', title: '@T("操作")', toolbar: '#tool', minWidth: 100 }
            ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-100'
            , id: 'tables'
            , where: {
                Id: '@Model.Device.Id'
            }
        });

        window.userActive = {
            reload: function () {
                table.reload('tables',
                    {
                         page: {
                            curr: 1
                        },
                        where: {
                            key: $("#key1").val(),
                            Id: '@Model.Device.Id',
                        }
                    });
            }
        }
        $("#key1").on("input", function (e) {
            userActive.reload();
        });

        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'refresh') {
                userActive.reload();
            }
        });

         table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if(obj.event === 'untie'){
                layer.confirm('@T("确认解绑吗")?', {title: '@T("信息")'}, function (index) {
                    $.post('@Url.Action("Untie")', { IdentityId: data.IdentityId,DeviceName:'@Model.Device.Code' }, function (data) {
                        if (data.success) {
                           userActive.reload();
                        }
                        else {
                            alert(data.msg);
                        }
                    });
                    layer.close(index);
                });
            }
         })
    });
</script>

<script type="text/html" id="switchTpl">
    <input type="checkbox" name="status" data-id="{{d.Id}}" lay-skin="switch" disabled lay-text="ON|OFF" lay-filter="statusedit" {{ d.Enable == "1" ? 'checked' : ''}}>
</script>
<script type="text/html" id="tool">
    <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="untie">@T("解绑")</a>
</script>