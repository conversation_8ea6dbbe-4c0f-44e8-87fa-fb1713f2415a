﻿@model Device
@{

}
<style asp-location="true">
    .layui-form-label{
        white-space:nowrap;
        /* border:2px solid ; */
    }
    .layui-card-his{
        padding:20px 14px;
        background-color:white;
    }
    .layui-form-item-his{
        padding:3px 0px;
    }
</style>


<form class="layui-form dg-form">
    <div class="layui-form-item-his" id="search" style="margin-bottom: 3px;">
        <div class="layui-inline">
             <label class="layui-form-label" style="width: auto">@T("筛选时间"):</label>
             
                <div class="layui-input-inline">
                    <input type="text" name="start" id="start" readonly placeholder="@T("开始时间")" autocomplete="off" class="layui-input">
                </div>
                 -
                <div class="layui-input-inline">
                    <input type="text" name="end" id="end" readonly placeholder="@T("结束时间")" autocomplete="off" class="layui-input">
                </div>
            <!--  -->
            <div class="layui-inline" style="margin-left:40px;">
                <label class="layui-form-label"> @T("关键词")：</label>
                <div class="layui-input-inline">
                    <input type="text" name="key" id="HistoricalDatakey" placeholder="@T("搜索..")" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-inline">
                <button type="button" class="layui-btn layui-btn-sm" id="SearchHistoricalData">@T("搜索") </button>
            </div>

            
        </div>
    </div>
</form>


<div class="layui-card-his">
    <div class="layui-card-body-his">
        <table class="layui-hide" id="historicalDataTable" lay-filter="tool2"></table>
    </div>
</div>

<script type="text/html" id="Value3">
    {{# if(d.Value) { }}
      <i class="layui-icon layui-icon-ok" style="color: #16b777"></i>
    {{# } else { }}
      <i class="layui-icon layui-icon-close" style="color: #ff5722"></i>
    {{# } }}
</script>