# 海凌科物联网平台

修改时间:2024 年 8 月 15 日 19:36:00

---

物联网平台为各种设备提供快速接入能力，支持 Https/WebSocket/MQTT 等多种标准协议以及自定义协议，提供设备管理、安全通信、规则引擎和消息下发等功能。向下支持连接海量设备，采集设备数据上云；向上提供开放 API，服务端可通过调用开放 API 将指令消息下发至设备端，实现远程控制。

设备 <-http/mqtt/tcp-> IoT 平台 <-api-> 业务系统

## 设备 MQTT 直连

硬件设备可以通过 MQTT 直连物联网服务平台 IoTServer，简化接入流程。  
由于设备的物模型从出厂起就已经固定，该接入方式无需太多设置即可使用。  
但也因此失去了整个 IoT 解决方案的许多功能，以及灵活性。

支持的功能：

1. 设备鉴权。支持一机一密和一型一密两种方式验证接入设备的合法性。
2. 数据上报。设备采集数据后，向指定 topic 上报数据，IoT 平台存储并驱动下游业务。
3. 指令下发。业务系统调用 IoT 平台的 WebApi 接口，向指定设备 topic 推送服务调用消息。
4. x509 证书。保护通信链路安全。

不支持的功能：

1. 物模型下发。根据下发的物模型动态采集点位数据。
2. 边缘端策略引擎。下发策略，本地执行，不依赖网络。
3. 数据缓存。在网络异常时本地缓存数据。

支持的 MQTT 协议版本：

1. MQTT3.1
2. MQTT3.1.1
3. MQTT5.0

---

## 功能说明

平台源码包中提供了例程 IoTClientMqtt，模拟设备实现了以下所有功能。  
MQTT 消息体部分使用 Json 格式。

参数说明：

1. ProductKey。产品编码，用于唯一标识产品
2. ProductSecret。产品密钥，用于一型一密验证
3. DeviceCode。设备编码，也叫设备证书，唯一标识设备
4. DeviceSecret。设备密钥，用于一机一密验证
5. UUID。设备本机唯一码，例如芯片 ID 或 MAC 地址等
6. LocalIp。本地 IP 地址，可选填
7. Time。本地 UTC 时间，1970/1/1 以来的总毫秒数，例如 DateTime.UtcNow.ToLong()
8. ModuleType。模块类型，如 B35

产品参数和设备参数分别来自产品管理和设备管理，可以在 IoTWeb 中管理。  
Topic 中的`ProductKey`和`DeviceCode`可以是网关设备或子设备，平台验证其从属关系。

### 设备鉴权（简化）

建立 MQTT 连接后，第一个 CONNECT 指令需要几个必备参数：  
|   参数   |            格式            |               备注               |
| :------: | :------------------------: | :------------------------------: |
| ClientId | {ProductKey}\|\|\|{UUID}\| | 客户端标识。产品编码，设备唯一码 |
| Username |        {DeviceCode}        |    用户名。设备证书/设备编码     |
| Password |          {Secret}          |     密码。设备密钥或产品密钥     |

一型一密接入时，Secret=ProductSecret  
一机一密接入时，Secret=DeviceSecret

设备鉴权成功后，可以在 IoTWeb 设备在线中查到，同时在设备历史中记录一行日志。  
若使用星尘监控，可根据埋点`MqttLogin`找到登录调用链。  
网关设备使用设备鉴权，子设备通过网关设备上报数据，无需对接设备鉴权。

### 设备鉴权（完整）

建立 MQTT 连接后，第一个 CONNECT 指令需要几个必备参数：  
|   参数   |                               格式                                |                           备注                           |
| :------: | :---------------------------------------------------------------: | :------------------------------------------------------: |
| ClientId | {ProductKey}\|{ProjectId}\|{ModuleType}\|{DeviceCode}\|{NET_TYPE} |        客户端标识。设备类型，设备唯一码,网络类型         |
| Username |              {DeviceCode}\|{Time}\|{LocalIp}\|{Mac}               | 用户名。设备编码，当前 UTC 时间（毫秒）,本地 IP,Mac 地址 |
| Password |              $md5${salt}$MD5(MD5({Secret}), {salt})               |           密码。设备密钥原文，或 MD5 加盐散列            |

ClientId 中的网络类型,是用来区分连接平台的种类的:

- `NET_LORA` (表示 LoRa)
- `NET CELLULAR` (表示 2G/3G/4G/5G 蜂窝网)
- `NET WIFI` (表示 Wi-Fi)
- `NET ZIGBEE` (表示 ZigBee)
- `NET_ETHERNET` (表示以太网)
- `NET_OTHER` (表示其他网络类型)

#### 参数示例

ClientId 示例：`test|abc|********|2D8A3958-1E00-3A8C-7BCC-D45D64B3D95F|NET WIFI`

Username 示例：`abc|1669528190488`

一型一密接入时，Secret=ProductSecret  
一机一密接入时，Secret=DeviceSecret

Hash 为 MD5 加盐算法：  
`$md5${salt}$MD5(MD5({Secret}), {salt})`
salt 可使用随机字符串，一般使用 UTC 时间，也就是 1970/1/1 以来的总秒数 DateTime.UtcNow.ToInt()。

Password 示例：`$md5$1669528190$1BD0D72B022196C64FCD102BF5ADCC37`

设备鉴权成功后，可以在 IoTWeb 设备在线中查到，同时在设备历史中记录一行日志。  
若使用星尘监控，可根据埋点`MqttLogin`找到登录调用链。  
网关设备使用设备鉴权，子设备通过网关设备上报数据，无需对接设备鉴权。

### 非设备端鉴权

| 参数     | 格式                                 | 备注                                    |
| -------- | ------------------------------------ | --------------------------------------- |
| ClientId | "{UId}\|{IdentityId}\|{ip}\|3"       | UId 标识,IdentityId,本地 IP，客户端种类 |
| UserName | "{Name}\|{DateTime.UtcNow.ToLong()}" | 用户名,时间戳                           |
| Password | Password                             | 密码                                    |

非设备端连接 Mqtt 方式：
ClientId = "{UId}|{IdentityId}|{ip}|3",
UserName = "{Name}|{DateTime.UtcNow.ToLong()}",
Password = Password,
KeepAlive = 60,
上面的 UId 和为登录之后的 Id 和 IdentityId，ip 为手机当前 IP。

`UId`：

`IdentityId`：

客户端种类:

- `0`:为未知
- `1`:为 Android
- `2`:为 IOS
- `3`:为 PC 端

`Name`：为用户名
`Password`：为登录密码

### 心跳

MQTT 自带心跳机制，平台收到后会更新设备在线的活跃时间。

平台还设计了应用层心跳，用于上报设备工作状态。  
Topic: `sys/{ProductKey}/{DeviceCode}/thing/event/ping/post`

消息内容为 Json 格式，对应`NewLife.IoT.Models.PingInfo`，字段如下
|        名称        |  类型  |                    备注                     |
| :----------------: | :----: | :-----------------------------------------: |
|       Memory       | ulong  |                  内存大小                   |
|  AvailableMemory   | ulong  |                可用内存大小                 |
|     TotalSize      | ulong  |                  磁盘大小                   |
| AvailableFreeSpace | ulong  |                磁盘可用空间                 |
|      CpuRate       | float  |                 CPU 使用率                  |
|    Temperature     | double |                    温度                     |
|      Battery       | double |                    电量                     |
|         IP         | string |                   本地 IP                   |
|       Uptime       |  int   |              开机时间，单位 s               |
|        Time        |  long  |           本地 UTC 时间。ms 毫秒            |
|       Delay        |  int   |                延迟。ms 毫秒                |
|       Module       | string | 硬件版本,将来可能会用来区分不同客户定制固件 |
|      Version       | string |                  软件版本                   |

心跳数据主要用于更新设备在线表。  
Topic 中的`ProductKey`和`DeviceCode`可以是网关设备或子设备，平台验证其从属关系。

消息示例：

```json
{
  "Memory": 34176749568,
  "AvailableMemory": 16339456000,
  "TotalSize": 1000202039296,
  "AvailableFreeSpace": 187829858304,
  "CpuRate": 0.2464,
  "Temperature": 0,
  "Battery": 0,
  "IP": "********,************",
  "Uptime": 111584,
  "Time": 1669542703615,
  "Delay": 0,
  "Module": "B35",
  "Version": "HB35V1-231206.1643"
}
```

_响应消息_  
设备端可订阅以下主题来接收响应消息。  
Topic: `sys/{ProductKey}/{DeviceCode}/thing/event/ping/post_reply`

|    名称    | 类型  |                备注                |
| :--------: | :---: | :--------------------------------: |
|    Time    | long  | 客户端上报的本地 UTC 时间。ms 毫秒 |
| ServerTime | long  |      服务器 UTC 时间。ms 毫秒      |

客户端可根据 Time 计算网络延迟 Delay，可根据 ServerTime 实现简单的时间同步。

### 设备上下线

Topic: `sys/{ProductKey}/{DeviceCode}/thing/status/update`

| 名称       | 类型   | 备注                                                        |
| ---------- | ------ | ----------------------------------------------------------- |
| Status     | int    | 0:末激活，1:在线，3:离线，8:禁用，98:设备重置 ，99:设备配网 |
| Time       | long   | 带毫秒的时间戳                                              |
| DeviceName | string | DeviceCode 值                                               |

消息示例：

```json
{
    "Status": 1,
    "Time": 1702552894221,
    "DeviceName": 3VF3f5QliJsPPV0OZOj3
}
```

### 属性上报

设备端采集到数据后，可以批量上传。  
平台支持主设备下挂多个子设备，因此数据上报需要指定 DeviceCode，它是主设备或子设备编码。而 Topic 中的 DeviceCode 只能是主设备编码。  
Topic: `sys/{ProductKey}/{DeviceCode}/thing/property/post`

消息内容为 Json 格式，对应`NewLife.IoT.ThingModels.DataModels`，字段如下
|    名称    |       |    类型     |             备注             |
| :--------: | :---: | :---------: | :--------------------------: |
|     Id     |       |    long     |             编号             |
| DeviceCode |       |   string    |           设备编码           |
|   Items    |       | DataModel[] |           数据集合           |
|            | Time  |    long     | 时间。数据采集时间，UTC 毫秒 |
|            | Name  |   string    |             名称             |
|            | Value |   string    |             数据             |

平台收到数据后，更新设备属性表，写入设备数据表，驱动策略引擎，同时推送消息队列。  
Topic 中的`ProductKey`和`DeviceCode`可以是网关设备或子设备，平台验证其从属关系。  
数据归属关系，优先使用消息内容里面的`DeviceCode`，其次 Topic 的`DeviceCode`，最后使用登录时的设备。

_注：根据产品物模型定义，超过最大最小值或者最大步进的异常数据可能在落库前被抛弃。上面的 Id 值为下发的指令中的 Id，如果是设备端主动上报，则 Id 由设备端生成_

消息示例：

```json
{
  "Id": 7002693834011197441,
  "DeviceCode": "abc",
  "Items": [
    {
      "Time": 1669542121477,
      "Name": "TestValue",
      "Value": "9"
    }
  ]
}
```

_响应消息_  
设备端可订阅以下主题来接收响应消息。  
Topic: `sys/{ProductKey}/{DeviceCode}/thing/property/post_reply`  
消息内容未定义。

### 服务调用

设备端可订阅以下主题来接收服务调用消息。  
Topic: `sys/{ProductKey}/{DeviceCode}/thing/service/post`

消息内容为 Json 格式，对应`NewLife.IoT.ThingModels.ServiceModel`，字段如下
|    名称    |   类型   |             备注             |
| :--------: | :------: | :--------------------------: |
|     Id     |   long   |             编号             |
|    Name    |  string  |            服务名            |
| InputData  |  string  |             入参             |
|   Expire   | datetime | 过期时间。未指定时表示不限制 |
| DeviceCode |  float   |           设备编码           |

服务调用下发前，会在设备服务中记录一行日志，状态为`处理中`。  
平台没有设计专门的`thing/property/set`主题，而是统一归类到`thing/service/post`中。  
Topic 中的`ProductKey`和`DeviceCode`可以是网关设备或子设备，平台验证其从属关系。  
服务归属关系，优先使用消息内容里面的`DeviceCode`，其次 Topic 的`DeviceCode`，最后使用登录时的设备。

_注：消息中的 DeviceCode 支持主设备和子设备_

消息示例：

```json
{
  "Id": 7002693834011197441,
  "Name": "SetProperty",
  "InputData": "{\"Name\":\"TestValue\",\"Value\":\"666\",\"Address\":null}",
  "Expire": "",
  "DeviceCode": "abc",
  "TraceId": null,
  "traceparent": "00-0a00000316695435134780343e8174-0a000003b4d40729-01"
}
```

_注：实际下发消息带有 TraceId 和 traceparent，用于星尘监控构建完整调用链_

_响应消息_  
平台订阅以下主题来接收服务响应消息。  
Topic: `sys/{ProductKey}/{DeviceCode}/thing/service/post_reply`  
消息内容为 Json 格式，对应`NewLife.IoT.ThingModels.ServiceReplyModel`，字段如下
|  名称  |     类型      |             备注              |
| :----: | :-----------: | :---------------------------: |
|   Id   |     long      |             编号              |
| Status | ServiceStatus |            状态。             |
|        |               | 0, 就绪; 1, 处理中; 2, 已完成 |
|        |               |       3, 取消; 4, 错误        |
|  Data  |    string     |           返回数据            |

平台根据 Id 更新设备服务日志的状态和输出参数。  
Topic 中的`ProductKey`和`DeviceCode`可以是网关设备或子设备，平台验证其从属关系。

```json
{
  "Id": 7002693834011197441,
  "Status": 2,
  "Data": "ok"
}
```

### App 透传下发（单属性）

设备端可订阅以下主题来接收服务调用消息。

Topic: `sys/{ProductKey}/{DeviceCode}/thing/property/set`

消息内容为 Json 格式，对应`HlktechIoT.Services.TransmissionModel`，字段如下
|    名称    |         |        类型        |             备注             |
| :--------: | :-----: | :----------------: | :--------------------------: |
|     Id     |         |        long        |             编号             |
|    Name    |         |       string       |            服务名            |
| InputData  |         | TransmissionData[] |             入参             |
|            |  Name   |       string       |           属性名称           |
|            |  Value  |       object       |            属性值            |
|            | Address |       string       |             地址             |
|   Expire   |         |      datetime      | 过期时间。未指定时表示不限制 |
| DeviceCode |         |       float        |           设备编码           |
|    Type    |         |       string       |    类型。如 Transmission     |

可订阅以下主题来接收响应消息。
Topic: sys/{ProductKey}/{DeviceCode}/thing/property/set_reply
消息内容为 Json 格式，字段如下
| 名称  |  类型  |   备注   |
| :---: | :----: | :------: |
|  Id   |  long  |   编号   |
| Data  | string | 返回数据 |

```json
{
  "Id": 7002693834011197441,
  "Data": "ok"
}
```

### App 透传下发（多属性）

设备端可订阅以下主题来接收服务调用消息。

Topic: `sys/{ProductKey}/{DeviceCode}/thing/property/set`

消息内容为 Json 格式，对应`HlktechIoT.Services.TransmissionsModel`，字段如下
|    名称    |         |        类型         |             备注             |
| :--------: | :-----: | :-----------------: | :--------------------------: |
|     Id     |         |        long         |             编号             |
|    Name    |         |       string        |            服务名            |
| InputData  |         | TransmissionsData[] |             入参             |
|            |  Name   |       string        |           属性名称           |
|            |  Value  |       object        |            属性值            |
|            | Address |       string        |             地址             |
|   Expire   |         |      datetime       | 过期时间。未指定时表示不限制 |
| DeviceCode |         |        float        |           设备编码           |
|    Type    |         |       string        |    类型。如 Transmissions    |

可订阅以下主题来接收响应消息。
Topic: sys/{ProductKey}/{DeviceCode}/thing/property/set_reply
消息内容为 Json 格式，字段如下
| 名称  |  类型  |   备注   |
| :---: | :----: | :------: |
|  Id   |  long  |   编号   |
| Data  | string | 返回数据 |

```json
{
  "Id": 7002693834011197441,
  "Data": "ok"
}
```

> 注:Type = Transmissions 时 为多属性，如果Type = Transmission时为单属性

### x509 证书

平台 MQTT 服务支持 x509 证书，启动前在配置文件`Config/IoTServer.config`中填写配置信息。  
|       名称       |  类型  |                     备注                      |
| :--------------: | :----: | :-------------------------------------------: |
|   MqttCertPath   | string | MQTT 证书地址，默认为空。设置了才启用安全连接 |
| MqttCertPassword | string |                 MQTT 证书密码                 |

### OTA 升级

MQTT 支持 OTA 升级，平台产品版本管理可新增产品版本，上传升级包并配置升级策略。

~~设备发送消息请求 OTA 升级包信息。~~
~~Topic:`sys/{ProductKey}/{DeviceCode}/thing/ota/firmware/get`~~

~~消息内容未定义。~~
Topic 中的`ProductKey`和`DeviceCode`可以是网关设备或子设备，平台验证其从属关系。

~~响应消息~~
设备端可订阅以下主题来接收 OTA 升级包消息。
Topic: `sys/{ProductKey}/{DeviceCode}/thing/ota/get`

|    名称     |  类型  |             备注             |
| :---------: | :----: | :--------------------------: |
|    MsgId    | Int32  |   消息编号，用于上下行对应   |
|   Version   | string |            版本号            |
|   Source    | string |       更新源，Url 地址       |
|  FileHash   | string |        文件哈希。MD5         |
|  FileSize   |  long  |           文件大小           |
|  Executor   | string |      更新后要执行的命令      |
|    Force    |  bool  | 是否强制更新，不需要用户同意 |
| Description | string |             描述             |

客户端可根据 Time 计算网络延迟 Delay，可根据 ServerTime 实现简单的时间同步。

消息示例：

```json
{
  "MsgId": 1,
  "Version": "v1.0",
  "Source": "https://cloud.hlktech.com/api/v1/device/devicecommon/otaupgrade?Code=FAD2_1",  //~~http://iot.feifan.link/cube?id=12345678.zip~~
  "FileHash": "1000202039296",
  "FileSize": 102400,
  "Executor": null,
  "Force": 0,
  "Description": "新版本"
}
```

设备端通过以下以下主题来回报 OTA 升级进度。

Topic: `sys/{ProductKey}/{DeviceCode}/thing/ota/get_reply`

| 名称    | 类型   | 备注                                        |
| ------- | ------ | ------------------------------------------- |
| Code    | int    | 状态码;0 正常,非 0,则认为失败               |
| Message | string | 回报消息描述                                |
| Step    | int    | 进度条,0-100                                |
| MsgId   | Int32  | 消息编号，用于上下行对应                    |
| Module  | string | 硬件版本,将来可能会用来区分不同客户定制固件 |
| Version | string | 软件版本                                    |

Code:

- 0:正常
- 1:版本不匹配
- 2:flash 校验失败
- 3:url 不可用
- 5:不在升级状态

升级状态,设备端会回报平台升级状态

```json
{
  "Code": 0,
  "Message": "Upgrading",
  "Step": 15,
  "MsgId": 1,
  "Module": "B35",
  "Version": "HB35V1-231206.1643"
}
```

固件版本格式

- RM65-1.6.2-bz-20240402094606.ota
- 产品型号-版本号-标识(bz为标准，dz为定制)-日期时间.ota

> ~~是否判断升级成功,是根据设备重新上线后的心跳包内的上传的版本号做判断,因为可能会出现错误升级固件把设备升死的情况,要确保升级后设备端依旧具备联网能力,才判断升级成功~~  
> 是否判断升级成功，是根据设备上报的Step为100来做判断的，因为心跳包频率太高，不适合做常规的判断，也无从判断版本号是否升级成功，毕竟升级过程中也是有心跳包的。  
> 云端会有机制使用服务的主题下发指令CheckOTA检查升级状态。已完成或者不在升级状态为2，接收到升级指令状态为0，升级中为1，取消为3，错误为4

### 重置云端与设备重启、重置、配网

当设备希望恢复出厂设置或者配网时，除了删除设备上本地的配置信息，如果也希望云平台清除一些配置，可以发送 reset 消息重置云端状态, 解除用户和设备绑定关系

设备发送消息请求重置云端信息。  
Topic: `sys/{ProductKey}/{DeviceCode}/thing/event/reset`

| 名称  | 类型 | 备注                                                                   |
| ----- | ---- | ---------------------------------------------------------------------- |
| Reset | int  | 标记,为 1 重置，2 为配网,当 Reply 有值时不起作用                       |
| Reply | int  | 回复,为 1 时 Reset 不起作用，收到 reset_reply 主题时回复为 1（重置）/2（配网），否则为 0 |
| Time  | long | 设备当前 UTC 时间,毫秒                                                 |

消息示例：

```json
{
  "Reset": 1,
  "Reply": 1,
  "Time": 1669542703615
}
```

响应消息

| 名称        | 类型 | 备注                     |
| ----------- | ---- | ------------------------ |
| DeviceReset | int  | 1, 设备重启;0,无操作     |
| ConfigClean | int  | 1, 恢复出厂设置;0,无操作 |

> 注:DeviceReset = 1 时 设备会重启,重置标志 ConfigClean 也会生效;为 0 时,ConfigClean 无作用

设备端可订阅以下主题来接收重置消息,可以作为平台是否收到重置消息的检查  
Topic: `sys/{ProductKey}/{DeviceCode}/thing/event/reset_reply`

- 按键重置回复

```json
{
  "DeviceReset": 1,
  "ConfigClean": 1,
  "Time": 1709778302124
}
```

- 配网重置回复:

```json
{
  "DeviceReset": 0,
  "ConfigClean": 0,
  "Time": 1709778302124
}
```

此外云端还可以通过这个主题,主动控制设备行为

- 云端控制设备重置,恢复出厂

```json
{
  "DeviceReset": 1,
  "ConfigClean": 1,
  "Time": 1709778302124
}
```

> 云端下发该条指令重置设备端时,也需要自己清除到相应绑定信息

- 云端控制设备重启

```json
{
  "DeviceReset": 1,
  "ConfigClean": 0,
  "Time": 1709778302124
}
```

> 云端下发该条指令让设备端重新配网时,也需要自己清除到相应绑定信息

- 云端控制设备配网

```json
{
  "DeviceReset": 0,
  "ConfigClean": 0,
  "Time": 1709778302124
}
```

> 此响应也作为远程重启设备功能,在需要重启时,云端调用主题,发送到设备端,设备端接收到消息进行重启

### APP通信
APP专属主题用于接收服务端往APP发送内容
Topic: `sys/user/{IdentityId}/event/set`

|    名称    |         |        类型        |             备注             |
| :--------: | :-----: | :----------------: | :--------------------------: |
|     Id     |         |        long        |             编号             |
| IdentityId |         |        string        |         用户惟一码            |
|    Type    |         |        int         |  类型,为 1 退出登录，为2消息提醒  |
| InputData  |         |      object       |             入参             |
|            |  Type  |       int       |           为1时Value为网址，为2时Value为内容           |
|            |  Name  |       string       |           标题           |
|            |   Value   |       string       |            内容            |
|    Time    |         |        long         |  当前 UTC 时间,毫秒  |