@{
  var Id = Model.Device.Id;
}
<style asp-location="true">
    .pageBox {
        width: 98%;
        /* border: 1px solid ; */
    }

    .title {
        padding: 10px;
        font-size: 18px;
        font-weight: 600;
        color: rgba(70, 70, 70, 1);
        position: relative;
        /* font-weight: bold; */
    }
    .title::before{
        position: absolute;
        left: 0px;
        content: '';
        width: 4px;
        height: 20px;
        background-color: #f56c6c;
    }

    .card {
        padding: 10px 10px 20px 10px;
        border-radius: 5px;
        background-color: white;
    }

    .description {
        border: 1px solid #e0e0e0;
        display: grid;
        grid-template-columns: repeat(3, 1fr); /* 3 columns with equal width */
        grid-template-rows: repeat(3, 1fr); /* 3 rows with equal height */
    }

    .desc_item {
        grid: 1fr 1fr;
        display: flex;
        min-width: 200px;
        border-bottom: 1px solid #dad9d9;
    }

    .desc_item_label {
        background-color: #efeff1;
        width: 120px !important;
        text-align: left;
        min-width: 60px;
        color: gray;
        padding: 17px;
        border-left: 1px solid #e0e0e0;
        border-right: 1px solid #e0e0e0;
        font-size: 15px;
    }

    .desc_item_value {
        text-indent: 0px;
        line-height: 25px;
        padding: 17px;
        color: rgba(80, 80, 80, 1);
        white-space: break-spaces;
    }

    .tagBox {
        padding: 10px;
    }

    .tagItem {
        display: flex;
    }

    .tagItemValue {
        background-color: rgba(135, 207, 235, 0.4);
        padding: 5px;
        /* height: 30px; */
        border-radius: 7px;
        margin-left: 10px;
        color: gray;
        box-shadow: 1px 1px 4px #ccc;
    }

    .icon {
        width: 20px;
        border-radius: 5px;
    }

    .copy {
        margin-left: 10px;
        color: dodgerblue;
        text-shadow: 0 0 1px skyblue;
        cursor: pointer;
    }

    .copy:hover {
        text-decoration: underline;
    }
    .btn_width{
        width: 60px;
    }
    .layui-layer-btn0{
        background-color: #f56c6c !important;
    }
</style>
<div class="pageBox">
    <div class="card">
        <div class="title">@T("设备信息")</div>
        <div class="description">
            <div class="desc_item">
                <div class="desc_item_label">@T("产品名称") @* <img class="icon" src="~/images/act.jpg"> *@ </div>
                <div class="desc_item_value">@Model.ProductName </div>
            </div>
            <div class="desc_item">
                <div class="desc_item_label">ProjectKey <img class="icon" src="" alt=""></div>
                <div class="desc_item_value">@Model.ProjectKey </div>
            </div>
            <div class="desc_item">
                <div class="desc_item_label">ProductKey <img class="icon" src="" alt=""></div>
                <div class="desc_item_value">@Model.ProductKey  </div>
            </div>
            <div class="desc_item">
                <div class="desc_item_label">ProductSecret <img class="icon" src="" alt=""></div>
                <div class="desc_item_value">@Model.ProductSecret </div>
            </div>
            <div class="desc_item">
                <div class="desc_item_label">DeviceName <img class="icon" src="" alt=""></div>
                <div class="desc_item_value">@Model.DeviceName</div>
            </div>
            <div class="desc_item">
                <div class="desc_item_label">DeviceSecret <img class="icon" src="" alt=""></div>
                <div class="desc_item_value">@Model.DeviceSecret</div>
            </div>
            <div class="desc_item">
                <div class="desc_item_label">@T("今日有效消息数") <img class="icon" src="" alt=""></div>
                <div class="desc_item_value">@Model.Counts</div>
            </div>
            <div class="desc_item">
                <div class="desc_item_label">@T("今日总消息数") <img class="icon" src="" alt=""></div>
                <div class="desc_item_value">@Model.AllCount</div>
            </div>
            <div class="desc_item">
                <div class="desc_item_label">@T("消息限制最小条数") <img class="icon" src="" alt=""></div>
                <div class="desc_item_value">@Model.MesLimitMin</div>
            </div>
            <div class="desc_item" style="grid-column: span 2;border-bottom: none">
                <div class="desc_item_label">@T("消息限制最大条数") <img class="icon" src="" alt=""></div>
                <div class="desc_item_value">@Model.MesLimitMax</div>
            </div>
        </div>
    </div>

    @* <div class="card" style="margin-top: 15px;">
        <div class="title">@T("设备扩展信息")</div>
        <div class="description" style="grid-template-columns: repeat(3, 1fr);grid-template-rows: repeat(2, 1fr); ">
            <div class="desc_item">
                <div class="desc_item_label">@T("产品名称") <img class="icon" src="" alt=""></div>
                <div class="desc_item_value">@T("新凯达24G雷达插座")</div>
            </div>
            <div class="desc_item">
                <div class="desc_item_label">@T("产品名称") <img class="icon" src="" alt=""></div>
                <div class="desc_item_value">@T("新凯达24G雷达插座")</div>
            </div>
            <div class="desc_item">
                <div class="desc_item_label">@T("产品名称") <img class="icon" src="" alt=""></div>
                <div class="desc_item_value">@T("新凯达24G雷达插座")</div>
            </div>
            <div class="desc_item" style="grid-column: span 2;border-bottom: none">
                <div class="desc_item_label">@T("产品名称") <img class="icon" src="" alt=""></div>
                <div class="desc_item_value">@T("新凯达24G雷达插座")</div>
            </div>
        </div>
    </div> *@

    <div class="card" style="margin-top: 15px;">
        <div class="title" style="margin-top: 8px;">@T("操作")</div>
        <div class="tagBox">
            <div class="tagItem">
                <button class="pear-btn pear-btn-danger pear-btn-xs btn_width" lay-active="reset">
                    <i class="layui-icon layui-icon-refresh"></i>
                     @T("重置")
                </button>
                <button class="pear-btn pear-btn-danger pear-btn-xs btn_width" lay-active="reboot" style="margin-left: 20px;">
                     <i class="layui-icon layui-icon-logout"></i>
                    @T("重启")
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    function copy(e) {
        var $ = layui.$;
        let text = $(e).parent().text().split(" ")[0];
        var textarea = document.createElement("textarea");
        textarea.value = text;
        document.body.appendChild(textarea);
        textarea.select();
        try {
            document.execCommand("copy"); // Security exception may be thrown by some browsers.
        } catch (ex) {
            console.warn("Copy to clipboard failed.", ex);
            layui.dgcommon.error("复制失败");
            return;
        } finally {
            document.body.removeChild(textarea);
        }
        layui.dgcommon.success("复制成功");
        // layui.adb.msg('复制成功');
        textarea.remove();
    }
</script>
<script asp-location="Footer">
    layui.use(['form','util','abp'], function () {
        var util = layui.util,
            form = layui.form,
            abp = layui.abp,
            layer = layui.layer;

         util.event('lay-active', {
            reset: function(){
                window.reset("@Id");
            },
            reboot: function(){
                window.reboot("@Id");
            }
        });


        window.reset = function (Id) {
            parent.layer.confirm('@T("确认重置吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                $.post('@Url.Action("Reset")', { Id }, function (data) {
                    if (data.success) {
                        abp.notify.success(data.msg);
                        active.reload();
                    } else {
                        abp.notify.warn(data.msg);
                    }
                });
                parent.layer.close(index);
            });
        }

        window.reboot = function (Id) {
            parent.layer.confirm('@T("确认重启吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                $.post('@Url.Action("Reboot")', { Id}, function (data) {
                    if (data.success) {
                        abp.notify.success(data.msg);
                        active.reload();
                    } else {
                        abp.notify.warn(data.msg);
                    }
                });
                parent.layer.close(index);
            });
        }
    });
</script>