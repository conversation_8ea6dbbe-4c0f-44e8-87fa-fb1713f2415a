﻿<?xml version="1.0" encoding="utf-8"?>
<DirectedGraph DataVirtualized="True" FilterState="CodeMap_TestAsset,CodeMap_ExternalReference,CodeMap_ProjectReference,CodeSchema_Assembly,CodeMap_SolutionFolder,CodeSchema_Namespace,UncategorizedRelationship" Layout="Sugiyama" ZoomLevel="-1" xmlns="http://schemas.microsoft.com/vs/2009/dgml">
  <Nodes>
    <Node Id="(@1 @10 @27 Member=(Name=BuildRule OverloadingParameters=[(@43 @44 Type=ProductFunction),@85]))" Category="CodeSchema_Method" Bounds="733.471651208706,590.933126110741,90.2166666666667,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="BuildRule" />
    <Node Id="(@1 @10 @27 Member=(Name=PushConfig OverloadingParameters=[@45,(@29 @30 Type=(Name=IDictionary GenericParameterCount=2 GenericArguments=[@68,@33])),@68]))" Category="CodeSchema_Method" Bounds="1099.75827311626,590.933126110741,101.76,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="PushConfig" />
    <Node Id="(@1 @10 @27 Member=(Name=QueryProperty OverloadingParameters=[@45,(@29 @32 Type=(Name=String @38 ParentType=String))]))" Category="CodeSchema_Method" Bounds="733.47154337993,646.173116345116,120.35,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="QueryProperty" />
    <Node Id="(@1 @10 @27 Member=(Name=QueryShadow @46))" Category="CodeSchema_Method" Bounds="1769.45495443136,590.933126110741,116.646666666667,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="QueryShadow" />
    <Node Id="(@1 @10 @27 Member=(Name=SetProperty OverloadingParameters=[@45,(@36 @37 Type=(Name=PropertyModel @38 ParentType=PropertyModel)),@68]))" Category="CodeSchema_Method" Bounds="1363.45163493266,590.933126110741,103.64,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="SetProperty" />
    <Node Id="(@1 @10 @27 Member=.cctor)" Category="CodeSchema_Method" Bounds="1207.62163981548,646.173116345116,143.06,25.24" CodeSchemaProperty_IsConstructor="True" CodeSchemaProperty_IsPrivate="True" CodeSchemaProperty_IsSpecialName="True" CodeSchemaProperty_IsStatic="True" DelayedCrossGroupLinksState="Fetched" Label="static ThingService" />
    <Node Id="@100" Category="CodeSchema_Method" Bounds="1035.21663411886,480.453084606835,92.5666666666666,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="PostEvent" />
    <Node Id="@101" Category="CodeSchema_Method" Bounds="1615.2849674522,369.973012585351,110.43,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="PostProperty" />
    <Node Id="@102" Category="CodeSchema_Method" Bounds="1158.21663411886,480.453084606835,92.5666666666666,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="PostEvent" />
    <Node Id="@103" Category="CodeSchema_Method" Bounds="1329.550296242,816.41284860183,91.3666666666668,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="AddEvent" />
    <Node Id="@104" Category="CodeSchema_Method" Bounds="1016.62537660723,761.412675857974,111.126666666667,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="AddSegment" />
    <Node Id="@105" Category="CodeSchema_Method" Bounds="1632.72824788839,590.933126110741,106.726666666667,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="PostShadow" />
    <Node Id="@106" Category="CodeSchema_Method" Bounds="1043.32490072042,646.173116345116,134.296666666667,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="PostSpecification" />
    <Node Id="@107" Category="CodeSchema_Method" Bounds="500.690027445491,89.0433259032217,140.81,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="ConsumeMessage" />
    <Node Id="@108" Category="CodeSchema_Method" Bounds="890.083270267951,480.453084606835,114.833333333333,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="InvokeService" />
    <Node Id="@109" Category="CodeSchema_Method" Bounds="1280.91990641704,480.453084606835,101.16,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="ValidRange" />
    <Node Id="@11" Category="CodeSchema_Namespace" Bounds="668.16203786033,320.655120497261,1845,1042.285" CodeSchemaProperty_IsPublic="True" CodeSchemaProperty_IsStatic="True" CommonLabel="IoTServer.Services" DelayedChildNodesState="Incomplete" DelayedCrossGroupLinksState="Fetched" FetchedChildrenCount="19" Group="Expanded" Label="IoTServer‎.Services" />
    <Node Id="@110" Category="CodeSchema_Method" Bounds="775.351709370946,144.283354284569,111.486666666667,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="PublishAsync" />
    <Node Id="@111" Category="CodeSchema_Method" Bounds="1857.56330078553,369.973012585351,87.8733333333334,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="PostData" />
    <Node Id="@112" Category="CodeSchema_Method" Bounds="759.553209232795,425.213063854882,121.893333333333,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="BuildDataPoint" />
    <Node Id="@113" Category="CodeSchema_Method" Bounds="1535.6349674522,425.213063854882,103.73,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="SaveHistory" />
    <Node Id="@114" Category="CodeSchema_Method" Bounds="1585.89696290867,816.41284860183,86.6733333333332,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="AddData" />
    <Node Id="@116" Category="CodeSchema_Field" Bounds="602.431455147697,1273.49419938552,112.853333333333,25.2399999999998" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_alarmQueue" />
    <Node Id="@117" Category="CodeSchema_Field" Bounds="747.027789783113,1265.09869157302,105.98,25.2399999999998" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_dataQueue" />
    <Node Id="@119" Category="CodeSchema_Field" Bounds="1277.28330078553,535.693135876366,108.433333333333,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_dataService" />
    <Node Id="@12" Category="CodeSchema_Namespace" Bounds="654.712650815041,1447.94012049726,191.306666666667,25" DelayedChildNodesState="NotFetched" DelayedCrossGroupLinksState="Fetched" Group="Collapsed" Label="Microsoft‎.CodeAnalysis" />
    <Node Id="@120" Category="CodeSchema_Field" Bounds="833.575315572078,816.652849197818,101.226666666667,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_dayQueue" />
    <Node Id="@121" Category="CodeSchema_Field" Bounds="1359.83727179809,1208.75988542067,87.9033333333332,25.2399999999998" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_defData" />
    <Node Id="@122" Category="CodeSchema_Field" Bounds="1477.74072720173,1208.75988542067,92.5966666666668,25.2399999999998" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_defEvent" />
    <Node Id="@123" Category="CodeSchema_Field" Bounds="1600.33746304158,1208.75988542067,102.086666666667,25.2399999999998" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_defHistory" />
    <Node Id="@124" Category="CodeSchema_Field" Bounds="625.18164833579,144.283354284569,119.826666666667,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_deviceService" />
    <Node Id="@125" Category="CodeSchema_Field" Bounds="984.554062976473,1263.99993669021,112.35,25.2399999999998" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_eventQueue" />
    <Node Id="@126" Category="CodeSchema_Field" Bounds="1126.90418667439,1272.32708512771,119.736666666667,25.2399999999998" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_historyQueue" />
    <Node Id="@127" Category="CodeSchema_Field" Bounds="1631.84466607488,231.702216473014,112.203333333333,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_hookService" />
    <Node Id="@128" Category="CodeSchema_Field" Bounds="515.863330261246,144.283354284569,78.4633333333331,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_queue" />
    <Node Id="@13" Category="CodeSchema_Namespace" Bounds="665.567773748217,1392.94012049726,251.006666666667,25" DelayedChildNodesState="NotFetched" DelayedCrossGroupLinksState="Fetched" Group="Collapsed" Label="System‎.Runtime‎.CompilerServices" />
    <Node Id="@130" Category="CodeSchema_Field" Bounds="1412.26830078553,480.453084606835,78.4633333333331,25.2400000000001" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_queue" />
    <Node Id="@131" Category="CodeSchema_Field" Bounds="1315.68208497898,871.652960906517,119.103333333333,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_queueService" />
    <Node Id="@132" Category="CodeSchema_Field" Bounds="884.357292143139,1263.99993669021,70.1966666666667,25.2399999999998" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_redis" />
    <Node Id="@133" Category="CodeSchema_Field" Bounds="1701.9999674522,425.213063854882,105,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_ruleService" />
    <Node Id="@134" Category="CodeSchema_Field" Bounds="1521.01157308371,480.453084606835,132.976666666667,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_segmentService" />
    <Node Id="@135" Category="CodeSchema_Field" Bounds="392.598452331558,144.283354284569,90.9933333333333,25.24" CodeSchemaProperty_IsPrivate="True" CodeSchemaProperty_IsStatic="True" DelayedCrossGroupLinksState="Fetched" Label="_Sessions" />
    <Node Id="@136" Category="CodeSchema_Field" Bounds="1465.26054038263,871.652960906517,81.9466666666667,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_setting" />
    <Node Id="@137" Category="CodeSchema_Field" Bounds="1860.52657308371,480.453084606835,81.9466666666667,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_setting" />
    <Node Id="@138" Category="CodeSchema_Field" Bounds="1050.52340655519,144.283354284569,113.143333333333,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_thingService" />
    <Node Id="@139" Category="CodeSchema_Field" Bounds="1774.04809940821,231.702216473014,113.143333333333,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_thingService" />
    <Node Id="@140" Category="CodeSchema_Field" Bounds="1455.0626290572,1359.23992692458,75.4766666666665,25.2399999999998" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_tracer" />
    <Node Id="@141" Category="CodeSchema_Field" Bounds="1577.49554038263,871.652960906517,75.4766666666665,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_tracer" />
    <Node Id="@142" Category="CodeSchema_Field" Bounds="965.450315572078,816.652727127505,75.4766666666665,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="_tracer" />
    <Node Id="@143" Category="CodeSchema_Method" Bounds="611.403423734936,1208.75988542067,128.486666666667,25.2399999999998" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="GetAlarmQueue" />
    <Node Id="@144" Category="CodeSchema_Method" Bounds="769.890224272045,1208.75988542067,121.266666666667,25.2399999999998" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="GetDataQueue" />
    <Node Id="@145" Category="CodeSchema_Method" Bounds="1038.42722459757,1208.75988542067,125.96,25.2399999999998" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="GetEventQueue" />
    <Node Id="@146" Category="CodeSchema_Method" Bounds="1194.38718065225,1208.75988542067,135.45,25.2399999999998" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="GetHistoryQueue" />
    <Node Id="@147" Category="CodeSchema_Method" Bounds="921.157020740144,1203.43707047927,87.27,25.2399999999998" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="GetRedis" />
    <Node Id="@148" Category="CodeSchema_Property" Bounds="674.118543497328,1103.75561788477,85.3823947873816,25" CodeSchemaProperty_IsPublic="True" DelayedChildNodesState="NotFetched" DelayedCrossGroupLinksState="Fetched" Group="Collapsed" Label="Host" />
    <Node Id="@149" Category="CodeSchema_Property" Bounds="1561.48312007265,1359.35988246595,96.6357281207147,25" CodeSchemaProperty_IsPublic="True" DelayedChildNodesState="NotFetched" DelayedCrossGroupLinksState="Fetched" Group="Collapsed" Label="Queue" />
    <Node Id="@15" Category="CodeSchema_Class" Bounds="1276.64099533516,1263.99988953179,417.905061035156,140.4803" CodeSchemaProperty_IsPrivate="True" DelayedChildNodesState="Fetched" DelayedCrossGroupLinksState="Fetched" FetchedChildrenCount="5" Group="Expanded" Label="MyDeferredQueue" UseManualLocation="True" />
    <Node Id="@16" Category="CodeSchema_Class" Bounds="823.533734195009,871.893002007471,121.31,25" CodeSchemaProperty_IsInternal="True" DelayedChildNodesState="NotFetched" DelayedCrossGroupLinksState="Fetched" Group="Collapsed" Label="DayQueue" />
    <Node Id="@18" Category="CodeSchema_Class" Bounds="1177.76632234236,776.425803825145,514.765,140.480312304687" CodeSchemaProperty_IsPublic="True" CommonLabel="DataService" DelayedChildNodesState="Fetched" DelayedCrossGroupLinksState="Fetched" FetchedChildrenCount="7" Group="Expanded" Icon="CodeSchema_Class" Label="DataService" SourceLocation="(Assembly=file:///D:/X/NewLife.IoT/IoTServer/Services/DataService.cs StartLineNumber=9 StartCharacterOffset=13 EndLineNumber=9 EndCharacterOffset=24)" />
    <Node Id="@2" Category="CodeSchema_Assembly" AssemblyTimestamp="02/20/2023 00:24:00" Bounds="-33.2885548064953,-1255.26228833263,1788.65466908371,1957.82045247461" CodeSchemaProperty_StrongName="IoTServer, Version=1.6.8451.15120, Culture=neutral, PublicKeyToken=null" CommonLabel="IoTServer" DelayedChildNodesState="Incomplete" DelayedCrossGroupLinksState="Fetched" FetchedChildrenCount="10" FilePath="$(a5f35672-10fb-428a-abf3-eb9b726c48b6.OutputPath)" Group="Expanded" Label="IoTServer.dll" UseManualLocation="True" />
    <Node Id="@20" Category="CodeSchema_Class" Bounds="372.598457726579,-6.21513097063126,963.474862670898,306.200357580566" CodeSchemaProperty_IsPublic="True" CommonLabel="MqttController" DelayedChildNodesState="Incomplete" DelayedCrossGroupLinksState="Fetched" FetchedChildrenCount="25" Group="Expanded" Icon="CodeSchema_Class" Label="MqttController" SourceLocation="(Assembly=file:///D:/X/NewLife.IoT/IoTServer/Services/MqttController.cs StartLineNumber=18 StartCharacterOffset=13 EndLineNumber=18 EndCharacterOffset=27)" />
    <Node Id="@22" Category="CodeSchema_Class" Bounds="507.909334365111,946.906116129832,1214.51482828776,477.574173401956" CodeSchemaProperty_IsPublic="True" CommonLabel="QueueService" DelayedChildNodesState="Incomplete" DelayedCrossGroupLinksState="Fetched" FetchedChildrenCount="25" Group="Expanded" Icon="CodeSchema_Class" Label="QueueService" SourceLocation="(Assembly=file:///D:/X/NewLife.IoT/IoTServer/Services/QueueService.cs StartLineNumber=12 StartCharacterOffset=13 EndLineNumber=12 EndCharacterOffset=25)" />
    <Node Id="@24" Category="CodeSchema_Class" Bounds="718.217889009025,721.425631081289,429.54839436849,195.480556625985" CodeSchemaProperty_IsPublic="True" CommonLabel="SegmentService" DelayedChildNodesState="Fetched" DelayedCrossGroupLinksState="Fetched" FetchedChildrenCount="5" Group="Expanded" Icon="CodeSchema_Class" Label="SegmentService" SourceLocation="(Assembly=file:///D:/X/NewLife.IoT/IoTServer/Services/SegmentService.cs StartLineNumber=12 StartCharacterOffset=17 EndLineNumber=12 EndCharacterOffset=31)" />
    <Node Id="@26" Category="CodeSchema_Class" Bounds="1366.07343565627,25.9822164730142,903.427266666667,250.96" CodeSchemaProperty_IsPublic="True" CommonLabel="ThingController" DelayedChildNodesState="Incomplete" DelayedCrossGroupLinksState="Fetched" FetchedChildrenCount="20" Group="Expanded" Icon="CodeSchema_Class" Label="ThingController" SourceLocation="(Assembly=file:///D:/X/NewLife.IoT/IoTServer/Controllers/ThingController.cs StartLineNumber=16 StartCharacterOffset=13 EndLineNumber=16 EndCharacterOffset=28)" />
    <Node Id="@28" Category="CodeSchema_Class" Bounds="593.731177548098,329.985249498118,1407.98517781575,361.440651269531" CodeSchemaProperty_IsPublic="True" CommonLabel="ThingService" DelayedChildNodesState="Incomplete" DelayedCrossGroupLinksState="Fetched" FetchedChildrenCount="36" Group="Expanded" Icon="CodeSchema_Class" Label="ThingService" SourceLocation="(Assembly=file:///D:/X/NewLife.IoT/IoTServer/Services/ThingService.cs StartLineNumber=17 StartCharacterOffset=13 EndLineNumber=17 EndCharacterOffset=25)" />
    <Node Id="@3" Category="CodeSchema_Namespace" Bounds="527.111937860332,822.295260333892,111.05,25" DelayedChildNodesState="NotFetched" DelayedCrossGroupLinksState="Fetched" Group="Collapsed" Label="IoTServer" />
    <Node Id="@34" Category="CodeSchema_Method" Bounds="1296.64096239054,1303.99993669021,94.3199999999997,25.2399999999998" CodeSchemaProperty_IsProtected="True" CodeSchemaProperty_IsVirtual="True" DelayedCrossGroupLinksState="Fetched" Label="ProcessAll" />
    <Node Id="@35" Category="CodeSchema_Method" Bounds="1595.056145496,1303.99993669021,79.4899999999998,25.2399999999998" CodeSchemaProperty_IsPublic="True" CodeSchemaProperty_IsVirtual="True" DelayedCrossGroupLinksState="Fetched" Label="Process" />
    <Node Id="@39" Category="CodeSchema_Method" Bounds="1526.50353565627,176.462216473014,93.4266666666667,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="PostDatas" />
    <Node Id="@4" Category="CodeSchema_Namespace" Bounds="471.055271193665,877.295260333892,167.106666666667,25" DelayedChildNodesState="NotFetched" DelayedCrossGroupLinksState="Fetched" Group="Collapsed" Label="IoTServer‎.Common" />
    <Node Id="@40" Category="CodeSchema_Method" Bounds="1772.4970689896,164.306932688175,98.1199999999999,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="PostEvents" />
    <Node Id="@41" Category="CodeSchema_Method" Bounds="1484.37020232294,121.222216473014,119.636666666667,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="PostProperties" />
    <Node Id="@42" Category="CodeSchema_Method" Bounds="1900.6171689896,176.462216473014,87.8733333333334,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="PostData" />
    <Node Id="@47" Category="CodeSchema_Method" Bounds="853.688268640347,590.933126110741,91.7866666666666,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="GetPoints" />
    <Node Id="@48" Category="CodeSchema_Method" Bounds="1231.51828613709,590.933126110741,101.933333333333,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="GetShadow" />
    <Node Id="@49" Category="CodeSchema_Method" Bounds="883.821512455451,646.173116345116,129.503333333333,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="GetSpecification" />
    <Node Id="@50" Category="CodeSchema_Method" Bounds="1197.80541831232,816.41284860183,100.856666666667,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="AddHistory" />
    <Node Id="@51" Category="CodeSchema_Method" Bounds="1649.93030232294,176.462216473014,92.5666666666666,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="PostEvent" />
    <Node Id="@52" Category="CodeSchema_Method" Bounds="1151.29065070434,1153.51971208083,86.6733333333332,25.2399999999998" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="AddData" />
    <Node Id="@53" Category="CodeSchema_Method" Bounds="1290.78327847126,1153.51971208083,91.3666666666668,25.2399999999998" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="AddEvent" />
    <Node Id="@54" Category="CodeSchema_Method" Bounds="1412.15003384236,1153.51971208083,100.856666666667,25.2399999999998" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="AddHistory" />
    <Node Id="@55" Category="CodeSchema_Method" Bounds="1837.67663411886,425.213063854882,127.646666666667,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="UpdateProperty" />
    <Node Id="@58" Category="CodeSchema_Method" Bounds="917.681709370946,144.283354284569,102.826666666667,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="CheckLogin" />
    <Node Id="@6" Category="CodeSchema_Namespace" Bounds="221.231462181708,-56.4803641263039,1303.80883340106,347.135484623564" CodeSchemaProperty_IsPublic="True" CodeSchemaProperty_IsStatic="True" CommonLabel="IoTServer.Controllers" DelayedChildNodesState="Incomplete" DelayedCrossGroupLinksState="Fetched" Group="Expanded" Label="IoTServer‎.Controllers" />
    <Node Id="@60" Category="CodeSchema_Method" Bounds="654.697258777254,1034.4779029988,126.24,25.2399999999998" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="GetReplyQueue" />
    <Node Id="@62" Category="CodeSchema_Method" Bounds="738.203648905411,761.41273689313,127.97,25.24" CodeSchemaProperty_IsConstructor="True" CodeSchemaProperty_IsPublic="True" CodeSchemaProperty_IsSpecialName="True" DelayedCrossGroupLinksState="Fetched" Label="SegmentService" />
    <Node Id="@63" Category="CodeSchema_Method" Bounds="789.473467590347,199.523329260155,83.2433333333338,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="Serialize" />
    <Node Id="@64" Category="CodeSchema_Method" Bounds="1386.07343565627,176.462216473014,110.43,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="PostProperty" />
    <Node Id="@65" Category="CodeSchema_Method" Bounds="810.937274646394,1048.51562272536,77.73,25.2399999999998" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="Publish" />
    <Node Id="@66" Category="CodeSchema_Method" Bounds="1634.0069689896,121.222216473014,108.49,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="ServiceReply" />
    <Node Id="@67" Category="CodeSchema_Method" Bounds="2142.77403565627,176.462216473014,106.726666666667,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="PostShadow" />
    <Node Id="@7" Category="CodeSchema_Namespace" Bounds="475.595271193665,935.665297175625,162.566666666667,25" DelayedChildNodesState="NotFetched" DelayedCrossGroupLinksState="Fetched" Group="Collapsed" Label="IoTServer‎.Features" />
    <Node Id="@70" Category="CodeSchema_Method" Bounds="2018.49060232294,176.462216473014,94.2833333333328,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="GetConfig" />
    <Node Id="@71" Category="CodeSchema_Method" Bounds="1904.43050232294,121.222216473014,91.7866666666664,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="GetPoints" />
    <Node Id="@72" Category="CodeSchema_Method" Bounds="2026.2172689896,121.222216473014,105.636666666667,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="GetProperty" />
    <Node Id="@73" Category="CodeSchema_Method" Bounds="530.307073230379,1048.51562272536,94.3899999999999,25.2399999999998" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="GetQueue" />
    <Node Id="@74" Category="CodeSchema_Method" Bounds="1772.4970689896,89.0378386747675,101.933333333333,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="GetShadow" />
    <Node Id="@75" Category="CodeSchema_Method" Bounds="1612.99363565627,65.9822164730142,129.503333333333,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="GetSpecification" />
    <Node Id="@76" Category="CodeSchema_Method" Bounds="527.909362455639,1116.70977555739,87.5866666666666,25.2399999999998" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="GetTopic" />
    <Node Id="@77" Category="CodeSchema_Method" Bounds="1904.43050232294,65.9822164730142,134.296666666667,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="PostSpecification" />
    <Node Id="@78" Category="CodeSchema_Method" Bounds="613.681505538133,646.173116345116,89.79,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="SetPoints" />
    <Node Id="@79" Category="CodeSchema_Method" Bounds="975.474938969123,590.933126110741,94.2833333333332,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="GetConfig" />
    <Node Id="@8" Category="CodeSchema_Namespace" Bounds="1175.09464041488,1392.94012049726,156.526666666667,25" DelayedChildNodesState="NotFetched" DelayedCrossGroupLinksState="Fetched" Group="Collapsed" Label="IoTServer‎.Models" />
    <Node Id="@80" Category="CodeSchema_Method" Bounds="1497.09166829855,590.933126110741,105.636666666667,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="GetProperty" />
    <Node Id="@83" Category="CodeSchema_Method" Bounds="419.638330261246,33.8033356688467,100.913333333333,25.24" CodeSchemaProperty_IsProtected="True" CodeSchemaProperty_IsVirtual="True" DelayedCrossGroupLinksState="Fetched" Label="OnConnect" />
    <Node Id="@84" Category="CodeSchema_Method" Bounds="671.700134257014,89.0433259032217,116.79,25.24" CodeSchemaProperty_IsProtected="True" CodeSchemaProperty_IsVirtual="True" DelayedCrossGroupLinksState="Fetched" Label="OnDisconnect" />
    <Node Id="@86" Category="CodeSchema_Method" Bounds="818.705103739436,89.0433259032217,78.7799999999997,25.24" CodeSchemaProperty_IsProtected="True" CodeSchemaProperty_IsVirtual="True" DelayedCrossGroupLinksState="Fetched" Label="OnPing" />
    <Node Id="@87" Category="CodeSchema_Method" Bounds="781.236542566128,480.453084606835,78.5266666666666,25.2400000000001" CodeSchemaProperty_IsPublic="True" CodeSchemaProperty_IsStatic="True" DelayedCrossGroupLinksState="Fetched" Label="FixData" />
    <Node Id="@88" Category="CodeSchema_Method" Bounds="927.643437072769,89.0433259032217,94.9033333333334,25.24" CodeSchemaProperty_IsProtected="True" CodeSchemaProperty_IsVirtual="True" DelayedCrossGroupLinksState="Fetched" Label="OnPublish" />
    <Node Id="@89" Category="CodeSchema_Method" Bounds="903.351709370946,199.523329260155,111.486666666667,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="PublishAsync" />
    <Node Id="@9" Category="CodeSchema_Namespace" Bounds="946.574540414884,1392.94012049726,198.52,25" DelayedChildNodesState="NotFetched" DelayedCrossGroupLinksState="Fetched" Group="Collapsed" Label="IoTServer‎.RpcControllers" />
    <Node Id="@90" Category="CodeSchema_Method" Bounds="537.77605028116,986.906247725362,77.73,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="Publish" />
    <Node Id="@91" Category="CodeSchema_Method" Bounds="1396.2549674522,425.213063854882,108.49,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="ServiceReply" />
    <Node Id="@92" Category="CodeSchema_Method" Bounds="613.681542566128,480.453084606835,105.636666666667,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="GetProperty" />
    <Node Id="@93" Category="CodeSchema_Method" Bounds="1052.60664833579,89.0433259032217,108.976666666666,25.24" CodeSchemaProperty_IsProtected="True" CodeSchemaProperty_IsVirtual="True" DelayedCrossGroupLinksState="Fetched" Label="OnSubscribe" />
    <Node Id="@94" Category="CodeSchema_Method" Bounds="613.681505538133,590.933126110741,89.79,25.24" CodeSchemaProperty_IsPublic="True" DelayedCrossGroupLinksState="Fetched" Label="SetPoints" />
    <Node Id="@95" Category="CodeSchema_Method" Bounds="1192.11664833579,89.0433259032217,123.956666666666,25.24" CodeSchemaProperty_IsProtected="True" CodeSchemaProperty_IsVirtual="True" DelayedCrossGroupLinksState="Fetched" Label="OnUnsubscribe" />
    <Node Id="@96" Category="CodeSchema_Method" Bounds="909.813376037613,254.763311865136,98.5633333333335,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="SendAsync" />
    <Node Id="@97" Category="CodeSchema_Method" Bounds="894.37651204855,535.693135876366,106.246666666667,25.24" CodeSchemaProperty_IsPrivate="True" DelayedCrossGroupLinksState="Fetched" Label="VerifyModel" />
    <Node Id="@98" Category="CodeSchema_Method" Bounds="1421.38429572387,1303.99993669021,142.833333333333,25.2399999999998" CodeSchemaProperty_IsConstructor="True" CodeSchemaProperty_IsPublic="True" CodeSchemaProperty_IsSpecialName="True" DelayedCrossGroupLinksState="Fetched" Label="MyDeferredQueue" />
    <Node Id="@99" Category="CodeSchema_Method" Bounds="1451.47541831232,816.41284860183,103.516666666667,25.24" CodeSchemaProperty_IsConstructor="True" CodeSchemaProperty_IsPublic="True" CodeSchemaProperty_IsSpecialName="True" DelayedCrossGroupLinksState="Fetched" Label="DataService" />
  </Nodes>
  <Links>
    <Link Source="@100" Target="@103" Category="CodeSchema_Calls" Bounds="577.458852195421,235.505810932139,283.242841053441,257.419053762959" Weight="1" />
    <Link Source="@100" Target="@119" Category="CodeSchema_FieldRead" Bounds="1127.51721191406,503.292114257813,141.686645507813,31.3070678710938" Weight="1" />
    <Link Source="@100" Target="@97" Category="CodeSchema_Calls" Bounds="986.227172851563,505.698455810547,64.45263671875,26.5698547363281" Weight="1" />
    <Link Source="@101" Target="@112" Category="CodeSchema_Calls" Bounds="890.220886230469,386.186767578125,724.857238769531,47.1072082519531" Weight="1" />
    <Link Source="@101" Target="@113" Category="CodeSchema_Calls" Bounds="1613.74743652344,395.218444824219,37.583740234375,25.0135498046875" Weight="1" />
    <Link Source="@101" Target="@133" Category="CodeSchema_FieldRead" Bounds="1689.48364257813,395.218444824219,38.099365234375,25.0549011230469" Weight="1" />
    <Link Source="@101" Target="@137" Category="CodeSchema_FieldRead" Bounds="1670.29309082031,395.218444824219,181.16552734375,89.0056762695313" Weight="1" />
    <Link Source="@101" Target="@3" Category="CodeSchema_Calls" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@101" Target="@55" Category="CodeSchema_Calls" Bounds="1722.93762207031,395.187561035156,116.82861328125,27.9377136230469" Weight="1" />
    <Link Source="@102" Target="@103" Category="CodeSchema_Calls" Bounds="583.703213231136,235.505765155772,394.622015293556,258.540032377106" Weight="1" />
    <Link Source="@102" Target="@119" Category="CodeSchema_FieldRead" Bounds="1233.30725097656,505.698455810547,60.7186279296875,26.4102478027344" Weight="1" />
    <Link Source="@102" Target="@97" Category="CodeSchema_Calls" Bounds="1008.69635009766,503.022888183594,149.331115722656,32.0974731445313" Weight="1" />
    <Link Source="@103" Target="@131" Category="CodeSchema_FieldRead" Bounds="1375.29333496094,841.656555175781,0,21" Weight="1" />
    <Link Source="@103" Target="@53" Category="CodeSchema_Calls" Weight="1" />
    <Link Source="@104" Target="@120" Category="CodeSchema_FieldRead" Bounds="935.790100097656,786.627685546875,93.4649047851563,27.4628295898438" Weight="2" />
    <Link Source="@104" Target="@142" Category="CodeSchema_FieldRead" Bounds="1025.99450683594,786.627685546875,30.447021484375,24.3753051757813" Weight="1" />
    <Link Source="@104" Target="@16" Category="CodeSchema_Calls" Bounds="946.064025878906,786.627685546875,126.141052246094,83.1776733398438" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@107" Target="@110" Category="CodeSchema_Calls" Bounds="630.494079589844,114.282447814941,136.864868164063,29.0785293579102" Weight="1" />
    <Link Source="@107" Target="@124" Category="CodeSchema_FieldRead" Bounds="597.139221191406,114.282447814941,53.8124389648438,26.0754165649414" Weight="2" />
    <Link Source="@107" Target="@128" Category="CodeSchema_FieldRead" Bounds="561.254272460938,114.282447814941,6.18548583984375,21.3553237915039" Weight="1" />
    <Link Source="@107" Target="@13" Category="CodeSchema_AttributeUse" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@107" Target="@73" Category="CodeSchema_Calls" Weight="1" />
    <Link Source="@108" Target="@97" Category="CodeSchema_Calls" Bounds="947.293151855469,505.698455810547,0,20.9999694824219" Weight="1" />
    <Link Source="@109" Target="@103" Category="CodeSchema_Calls" Bounds="216.163175842536,235.505783368662,318.181172998661,257.806326435586" Weight="1" />
    <Link Source="@109" Target="@119" Category="CodeSchema_FieldRead" Bounds="1331.29309082031,505.698455810547,0,20.9999694824219" Weight="1" />
    <Link Source="@109" Target="@13" Category="CodeSchema_AttributeUse" IsTargetVirtualized="True" Weight="2" />
    <Link Source="@11" Target="@18" Category="Contains" FetchingParent="@11" />
    <Link Source="@11" Target="@20" Category="Contains" FetchingParent="@11" />
    <Link Source="@11" Target="@22" Category="Contains" FetchingParent="@11" />
    <Link Source="@11" Target="@24" Category="Contains" FetchingParent="@11" />
    <Link Source="@110" Target="@13" Category="CodeSchema_AttributeUse" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@110" Target="@63" Category="CodeSchema_Calls" Bounds="831.095092773438,169.522445678711,0,21" Weight="1" />
    <Link Source="@110" Target="@89" Category="CodeSchema_Calls" Bounds="860.337646484375,169.522445678711,61.2515258789063,26.4338684082031" Weight="1" />
    <Link Source="@111" Target="@112" Category="CodeSchema_Calls" Bounds="890.232360839844,387.180999755859,967.124084472656,47.8028259277344" Weight="1" />
    <Link Source="@111" Target="@113" Category="CodeSchema_Calls" Bounds="1648.02197265625,390.327941894531,209.33447265625,36.8268737792969" Weight="1" />
    <Link Source="@111" Target="@133" Category="CodeSchema_FieldRead" Bounds="1796.30126953125,395.218444824219,71.4085693359375,26.8341064453125" Weight="1" />
    <Link Source="@111" Target="@137" Category="CodeSchema_FieldRead" Bounds="1922.99658203125,395.218444824219,49.546630859375,80.7159729003906" Weight="1" />
    <Link Source="@111" Target="@3" Category="CodeSchema_Calls" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@111" Target="@55" Category="CodeSchema_Calls" Bounds="1901.29309082031,395.218444824219,0,21" Weight="1" />
    <Link Source="@112" Target="@109" Category="CodeSchema_Calls" Bounds="881.239807128906,443.03076171875,391.088439941406,37.3202209472656" Weight="1" />
    <Link Source="@112" Target="@13" Category="CodeSchema_AttributeUse" IsTargetVirtualized="True" Weight="2" />
    <Link Source="@112" Target="@87" Category="CodeSchema_Calls" Bounds="820.293151855469,450.458465576172,0,21" Weight="1" />
    <Link Source="@112" Target="@92" Category="CodeSchema_Calls" Bounds="709.947143554688,450.458465576172,75.1635131835938,26.9612426757813" Weight="1" />
    <Link Source="@112" Target="@97" Category="CodeSchema_Calls" Bounds="760.543701171875,450.458465576172,125.507507324219,84.4181213378906" Weight="1" />
    <Link Source="@113" Target="@104" Category="CodeSchema_Calls" Bounds="1656.48302414725,456.679578994103,46.5471754660778,302.065689256467" Weight="1" />
    <Link Source="@113" Target="@114" Category="CodeSchema_Calls" Bounds="663.551760535516,451.530367557824,314.164276059532,359.424398301577" Weight="1" />
    <Link Source="@113" Target="@119" Category="CodeSchema_FieldRead" Bounds="1394.46374511719,450.458465576172,278.744995117188,91.4461975097656" Weight="1" />
    <Link Source="@113" Target="@130" Category="CodeSchema_FieldRead" Bounds="1490.70178222656,450.458465576172,65.5211181640625,26.6131286621094" Weight="1" />
    <Link Source="@113" Target="@134" Category="CodeSchema_FieldRead" Bounds="1587.29309082031,450.458465576172,0,21" Weight="1" />
    <Link Source="@113" Target="@52" Category="CodeSchema_Calls" Weight="1" />
    <Link Source="@114" Target="@136" Category="CodeSchema_FieldRead" Bounds="1542.60375976563,841.656555175781,58.58935546875,26.3128662109375" Weight="1" />
    <Link Source="@114" Target="@141" Category="CodeSchema_FieldRead" Bounds="1620.70288085938,841.656555175781,5.39208984375,21.27587890625" Weight="1" />
    <Link Source="@114" Target="@3" Category="CodeSchema_Calls" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@119" Target="@18" Category="References" Bounds="897.260589912585,562.010567557824,152.267915210586,208.452824030739" Weight="1" />
    <Link Source="@120" Target="@16" Category="References" Bounds="884.205078125,841.867736816406,0,21" Weight="1" />
    <Link Source="@121" Target="@15" Category="References" Bounds="1412.93631116655,1233.99988542067,16.4630430461405,22.7129264342232" Weight="1" />
    <Link Source="@122" Target="@15" Category="References" Bounds="1512.42272977173,1233.99988542067,7.3173587168053,21.4807321393446" Weight="1" />
    <Link Source="@123" Target="@15" Category="References" Bounds="1596.21336579393,1233.99988542067,36.6291309439773,24.9353846665847" Weight="1" />
    <Link Source="@128" Target="@22" Category="References" Bounds="1292.10371821357,176.869141047393,175.556461664225,768.627482164224" Weight="1" />
    <Link Source="@13" Target="@12" Category="CodeSchema_AttributeUse" Bounds="764.971170893034,1417.94012049726,16.8487718855216,22.7657451182488" IsSourceVirtualized="True" IsTargetVirtualized="True" Weight="3" />
    <Link Source="@130" Target="@22" Category="References" Bounds="764.364809360476,294.532187367153,217.035267887756,444.428640323314" Weight="1" />
    <Link Source="@131" Target="@22" Category="References" Bounds="1495.05089857072,875.620054882812,222.947019072561,74.6434798472909" Weight="1" />
    <Link Source="@134" Target="@24" Category="References" Bounds="1626.83855851479,511.919987792969,117.563022037652,207.886327578403" Weight="1" />
    <Link Source="@136" Target="@3" Category="References" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@137" Target="@3" Category="References" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@138" Target="@28" Category="References" Weight="1" />
    <Link Source="@139" Target="@28" Category="References" Bounds="531.154650351673,-35.1509376610007,2.75509401862985,85.9410336849909" Weight="1" />
    <Link Source="@143" Target="@116" Category="CodeSchema_FieldRead" Bounds="664.390451838329,1233.99988542067,7.98334898884423,30.7825271154497" Weight="2">
      <Category Ref="CodeSchema_FieldWrite" />
    </Link>
    <Link Source="@143" Target="@132" Category="CodeSchema_FieldRead" Bounds="731.346703911161,1233.99988542067,144.233063619366,32.6790484739697" Weight="1" />
    <Link Source="@144" Target="@117" Category="CodeSchema_FieldRead" Bounds="811.136484230568,1233.99988542067,12.5537232169114,23.1845263796815" Weight="2">
      <Category Ref="CodeSchema_FieldWrite" />
    </Link>
    <Link Source="@144" Target="@132" Category="CodeSchema_FieldRead" Bounds="850.840749040576,1233.99988542067,40.6524969124938,25.2512515163335" Weight="1" />
    <Link Source="@145" Target="@125" Category="CodeSchema_FieldRead" Bounds="1061.24663580857,1233.99988542067,26.2982120884058,23.941308458421" Weight="2">
      <Category Ref="CodeSchema_FieldWrite" />
    </Link>
    <Link Source="@145" Target="@132" Category="CodeSchema_FieldRead" Bounds="962.999421245516,1233.99988542067,96.8396091137017,29.4002635767001" Weight="1" />
    <Link Source="@146" Target="@126" Category="CodeSchema_FieldRead" Bounds="1208.6083594939,1233.99988542067,38.5466337768721,32.5233953320983" Weight="2">
      <Category Ref="CodeSchema_FieldWrite" />
    </Link>
    <Link Source="@146" Target="@132" Category="CodeSchema_FieldRead" Bounds="963.290954589844,1230.70397949219,231.096252441406,35.0467529296875" Weight="1" />
    <Link Source="@147" Target="@132" Category="CodeSchema_FieldRead" Bounds="934.29622471968,1228.67707047927,21.0486653936744,28.1179722988998" Weight="1" />
    <Link Source="@15" Target="@140" Category="Contains" FetchingParent="@15" />
    <Link Source="@15" Target="@149" Category="Contains" FetchingParent="@15" />
    <Link Source="@15" Target="@34" Category="Contains" FetchingParent="@15" />
    <Link Source="@15" Target="@35" Category="Contains" FetchingParent="@15" />
    <Link Source="@15" Target="@98" Category="Contains" FetchingParent="@15" />
    <Link Source="@18" Target="@103" Category="Contains" FetchingParent="@18" />
    <Link Source="@18" Target="@114" Category="Contains" FetchingParent="@18" />
    <Link Source="@18" Target="@131" Category="Contains" FetchingParent="@18" />
    <Link Source="@18" Target="@136" Category="Contains" FetchingParent="@18" />
    <Link Source="@18" Target="@141" Category="Contains" FetchingParent="@18" />
    <Link Source="@18" Target="@50" Category="Contains" FetchingParent="@18" />
    <Link Source="@18" Target="@99" Category="Contains" FetchingParent="@18" />
    <Link Source="@2" Target="@11" Category="Contains" FetchingParent="@2" />
    <Link Source="@2" Target="@12" Category="Contains" FetchingParent="@2" />
    <Link Source="@2" Target="@13" Category="Contains" FetchingParent="@2" />
    <Link Source="@2" Target="@3" Category="Contains" FetchingParent="@2" />
    <Link Source="@2" Target="@4" Category="Contains" FetchingParent="@2" />
    <Link Source="@2" Target="@6" Category="Contains" FetchingParent="@2" />
    <Link Source="@2" Target="@7" Category="Contains" FetchingParent="@2" />
    <Link Source="@2" Target="@8" Category="Contains" FetchingParent="@2" />
    <Link Source="@2" Target="@9" Category="Contains" FetchingParent="@2" />
    <Link Source="@20" Target="@107" Category="Contains" FetchingParent="@20" />
    <Link Source="@20" Target="@110" Category="Contains" FetchingParent="@20" />
    <Link Source="@20" Target="@124" Category="Contains" FetchingParent="@20" />
    <Link Source="@20" Target="@128" Category="Contains" FetchingParent="@20" />
    <Link Source="@20" Target="@135" Category="Contains" FetchingParent="@20" />
    <Link Source="@20" Target="@138" Category="Contains" FetchingParent="@20" />
    <Link Source="@20" Target="@58" Category="Contains" FetchingParent="@20" />
    <Link Source="@20" Target="@63" Category="Contains" FetchingParent="@20" />
    <Link Source="@20" Target="@83" Category="Contains" FetchingParent="@20" />
    <Link Source="@20" Target="@84" Category="Contains" FetchingParent="@20" />
    <Link Source="@20" Target="@86" Category="Contains" FetchingParent="@20" />
    <Link Source="@20" Target="@88" Category="Contains" FetchingParent="@20" />
    <Link Source="@20" Target="@89" Category="Contains" FetchingParent="@20" />
    <Link Source="@20" Target="@93" Category="Contains" FetchingParent="@20" />
    <Link Source="@20" Target="@95" Category="Contains" FetchingParent="@20" />
    <Link Source="@20" Target="@96" Category="Contains" FetchingParent="@20" />
    <Link Source="@22" Target="@116" Category="Contains" FetchingParent="@22" />
    <Link Source="@22" Target="@117" Category="Contains" FetchingParent="@22" />
    <Link Source="@22" Target="@121" Category="Contains" FetchingParent="@22" />
    <Link Source="@22" Target="@122" Category="Contains" FetchingParent="@22" />
    <Link Source="@22" Target="@123" Category="Contains" FetchingParent="@22" />
    <Link Source="@22" Target="@125" Category="Contains" FetchingParent="@22" />
    <Link Source="@22" Target="@126" Category="Contains" FetchingParent="@22" />
    <Link Source="@22" Target="@132" Category="Contains" FetchingParent="@22" />
    <Link Source="@22" Target="@143" Category="Contains" FetchingParent="@22" />
    <Link Source="@22" Target="@144" Category="Contains" FetchingParent="@22" />
    <Link Source="@22" Target="@145" Category="Contains" FetchingParent="@22" />
    <Link Source="@22" Target="@146" Category="Contains" FetchingParent="@22" />
    <Link Source="@22" Target="@147" Category="Contains" FetchingParent="@22" />
    <Link Source="@22" Target="@148" Category="Contains" FetchingParent="@22" />
    <Link Source="@22" Target="@15" Category="Contains" FetchingParent="@22" />
    <Link Source="@22" Target="@52" Category="Contains" FetchingParent="@22" />
    <Link Source="@22" Target="@53" Category="Contains" FetchingParent="@22" />
    <Link Source="@22" Target="@54" Category="Contains" FetchingParent="@22" />
    <Link Source="@22" Target="@60" Category="Contains" FetchingParent="@22" />
    <Link Source="@22" Target="@65" Category="Contains" FetchingParent="@22" />
    <Link Source="@22" Target="@73" Category="Contains" FetchingParent="@22" />
    <Link Source="@22" Target="@76" Category="Contains" FetchingParent="@22" />
    <Link Source="@22" Target="@90" Category="Contains" FetchingParent="@22" />
    <Link Source="@24" Target="@104" Category="Contains" FetchingParent="@24" />
    <Link Source="@24" Target="@120" Category="Contains" FetchingParent="@24" />
    <Link Source="@24" Target="@142" Category="Contains" FetchingParent="@24" />
    <Link Source="@24" Target="@16" Category="Contains" FetchingParent="@24" />
    <Link Source="@24" Target="@62" Category="Contains" FetchingParent="@24" />
    <Link Source="@26" Target="@127" Category="Contains" FetchingParent="@26" />
    <Link Source="@26" Target="@139" Category="Contains" FetchingParent="@26" />
    <Link Source="@26" Target="@39" Category="Contains" FetchingParent="@26" />
    <Link Source="@26" Target="@40" Category="Contains" FetchingParent="@26" />
    <Link Source="@26" Target="@41" Category="Contains" FetchingParent="@26" />
    <Link Source="@26" Target="@42" Category="Contains" FetchingParent="@26" />
    <Link Source="@26" Target="@51" Category="Contains" FetchingParent="@26" />
    <Link Source="@26" Target="@64" Category="Contains" FetchingParent="@26" />
    <Link Source="@26" Target="@66" Category="Contains" FetchingParent="@26" />
    <Link Source="@26" Target="@67" Category="Contains" FetchingParent="@26" />
    <Link Source="@26" Target="@70" Category="Contains" FetchingParent="@26" />
    <Link Source="@26" Target="@71" Category="Contains" FetchingParent="@26" />
    <Link Source="@26" Target="@72" Category="Contains" FetchingParent="@26" />
    <Link Source="@26" Target="@74" Category="Contains" FetchingParent="@26" />
    <Link Source="@26" Target="@75" Category="Contains" FetchingParent="@26" />
    <Link Source="@26" Target="@77" Category="Contains" FetchingParent="@26" />
    <Link Source="@28" Target="(@1 @10 @27 Member=(Name=BuildRule OverloadingParameters=[(@43 @44 Type=ProductFunction),@85]))" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="(@1 @10 @27 Member=(Name=PushConfig OverloadingParameters=[@45,(@29 @30 Type=(Name=IDictionary GenericParameterCount=2 GenericArguments=[@68,@33])),@68]))" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="(@1 @10 @27 Member=(Name=QueryProperty OverloadingParameters=[@45,(@29 @32 Type=(Name=String @38 ParentType=String))]))" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="(@1 @10 @27 Member=(Name=QueryShadow @46))" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="(@1 @10 @27 Member=(Name=SetProperty OverloadingParameters=[@45,(@36 @37 Type=(Name=PropertyModel @38 ParentType=PropertyModel)),@68]))" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="(@1 @10 @27 Member=.cctor)" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@100" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@101" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@102" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@105" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@106" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@108" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@109" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@111" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@112" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@113" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@119" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@130" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@133" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@134" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@137" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@47" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@48" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@49" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@55" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@78" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@79" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@80" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@87" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@91" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@92" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@94" Category="Contains" FetchingParent="@28" />
    <Link Source="@28" Target="@97" Category="Contains" FetchingParent="@28" />
    <Link Source="@3" Target="@13" Category="CodeSchema_AttributeUse" Bounds="607.995178222656,847.295288085938,122.948364257813,543.517456054688" IsSourceVirtualized="True" IsTargetVirtualized="True" Weight="18" />
    <Link Source="@3" Target="@18" Category="References" IsSourceVirtualized="True" Weight="1" />
    <Link Source="@3" Target="@24" Category="References" IsSourceVirtualized="True" Weight="1" />
    <Link Source="@3" Target="@28" Category="References" IsSourceVirtualized="True" Weight="1" />
    <Link Source="@3" Target="@4" Category="CodeSchema_FunctionPointer" Bounds="565.065107949358,847.295260333892,11.2017541533976,21.9812027746996" IsSourceVirtualized="True" IsTargetVirtualized="True" Weight="2" />
    <Link Source="@3" Target="@7" Category="References" Bounds="597.237731933594,847.295288085938,51.0584106445313,85.0933227539063" IsSourceVirtualized="True" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@34" Target="@140" Category="CodeSchema_FieldRead" Bounds="1377.84118652344,1329.23986816406,72.4808349609375,26.8714599609375" Weight="1" />
    <Link Source="@35" Target="@149" Category="CodeSchema_Calls" Bounds="1619.1689453125,1329.23986816406,9.920654296875,21.920654296875" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@39" Target="@111" Category="CodeSchema_Calls" Bounds="1594.62519608809,201.702266473014,277.713291808113,163.709276605095" Weight="1" />
    <Link Source="@39" Target="@127" Category="CodeSchema_FieldRead" Bounds="1599.42769144705,201.702216473014,54.198800710172,26.0956658675156" Weight="1" />
    <Link Source="@39" Target="@139" Category="CodeSchema_FieldRead" Bounds="1619.90352374578,199.101415316284,146.409431239562,31.4202251538531" Weight="1" />
    <Link Source="@39" Target="@4" Category="CodeSchema_AttributeUse" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@4" Target="@13" Category="CodeSchema_AttributeUse" Bounds="587.379455566406,902.295288085938,143.564086914063,488.517456054688" IsSourceVirtualized="True" IsTargetVirtualized="True" Weight="2" />
    <Link Source="@40" Target="@100" Category="CodeSchema_Calls" Bounds="1119.31731249061,189.546932688175,672.698809944143,287.379382571017" Weight="1" />
    <Link Source="@40" Target="@127" Category="CodeSchema_FieldRead" Bounds="1721.00100846801,189.546932688175,75.5369880174467,38.1020035263957" Weight="1" />
    <Link Source="@40" Target="@139" Category="CodeSchema_FieldRead" Bounds="1823.25409036548,189.546932688175,4.46921301926682,33.2355673917215" Weight="1" />
    <Link Source="@40" Target="@4" Category="CodeSchema_AttributeUse" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@41" Target="@101" Category="CodeSchema_Calls" Bounds="1550.59652589239,146.462166473014,109.420758352046,215.495017863752" Weight="1" />
    <Link Source="@41" Target="@127" Category="CodeSchema_FieldRead" Bounds="1577.54931640625,146.462219238281,92.053466796875,78.7464141845703" Weight="1" />
    <Link Source="@41" Target="@139" Category="CodeSchema_FieldRead" Bounds="1604.0068359375,143.164916992188,204.6669921875,82.7544097900391" Weight="1" />
    <Link Source="@41" Target="@4" Category="CodeSchema_AttributeUse" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@42" Target="@111" Category="CodeSchema_Calls" Bounds="1906.26215353667,201.702216473014,35.4840107544233,159.494526772409" Weight="1" />
    <Link Source="@42" Target="@127" Category="CodeSchema_FieldRead" Bounds="1751.84130525695,198.540480316037,148.775863732651,32.0270398146649" Weight="1" />
    <Link Source="@42" Target="@139" Category="CodeSchema_FieldRead" Bounds="1864.747222857,201.702216473014,53.7775042164124,26.0735822377732" Weight="1" />
    <Link Source="@42" Target="@4" Category="CodeSchema_AttributeUse" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@50" Target="@131" Category="CodeSchema_FieldRead" Bounds="1277.30749511719,841.656555175781,60.7186279296875,26.4102783203125" Weight="1" />
    <Link Source="@50" Target="@54" Category="CodeSchema_Calls" Weight="1" />
    <Link Source="@51" Target="@100" Category="CodeSchema_Calls" Bounds="1115.086084099,201.702266473014,555.608825538254,274.770118738228" Weight="1" />
    <Link Source="@51" Target="@127" Category="CodeSchema_FieldRead" Bounds="1691.16717886902,201.702216473014,3.15772827921069,21.0991313543024" Weight="1" />
    <Link Source="@51" Target="@139" Category="CodeSchema_FieldRead" Bounds="1726.91974293148,201.702216473014,64.6695535671081,26.5787440492557" Weight="1" />
    <Link Source="@51" Target="@4" Category="CodeSchema_AttributeUse" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@52" Target="@121" Category="CodeSchema_FieldRead" Bounds="1237.44402646977,1177.44772556863,114.31010487717,30.1896207100074" Weight="4">
      <Category Ref="CodeSchema_FieldWrite" />
    </Link>
    <Link Source="@52" Target="@144" Category="CodeSchema_Calls" Bounds="884.142822265625,1171.92236328125,267.147827148438,35.6470947265625" Weight="1" />
    <Link Source="@52" Target="@98" Category="CodeSchema_Calls" Weight="1" />
    <Link Source="@53" Target="@122" Category="CodeSchema_FieldRead" Bounds="1379.30518794497,1178.75569266669,93.2482538977697,27.4616541171904" Weight="4">
      <Category Ref="CodeSchema_FieldWrite" />
    </Link>
    <Link Source="@53" Target="@145" Category="CodeSchema_Calls" Bounds="1163.86948738855,1176.82920942302,127.110904100502,29.8717207567279" Weight="1" />
    <Link Source="@53" Target="@98" Category="CodeSchema_Calls" Weight="1" />
    <Link Source="@54" Target="@123" Category="CodeSchema_FieldRead" Bounds="1505.71158911248,1178.75971208083,93.8981134397784,27.4728883770058" Weight="4">
      <Category Ref="CodeSchema_FieldWrite" />
    </Link>
    <Link Source="@54" Target="@146" Category="CodeSchema_Calls" Bounds="1316.5866770036,1178.75971208083,100.193802132134,27.6092596629007" Weight="1" />
    <Link Source="@54" Target="@98" Category="CodeSchema_Calls" Weight="1" />
    <Link Source="@55" Target="@137" Category="CodeSchema_FieldRead" Bounds="1901.29309082031,450.458465576172,0,21" Weight="1" />
    <Link Source="@55" Target="@3" Category="CodeSchema_Calls" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@58" Target="@8" Category="CodeSchema_ReturnTypeLink" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@6" Target="@26" Category="Contains" FetchingParent="@6" />
    <Link Source="@60" Target="@148" Category="CodeSchema_Calls" Bounds="717.122947776026,1059.7179029988,0.51045767777407,35.0386698074215" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@62" Target="@120" Category="CodeSchema_FieldWrite" Bounds="820.938598632813,786.627685546875,37.0687255859375,24.9716186523438" Weight="1" />
    <Link Source="@62" Target="@142" Category="CodeSchema_FieldWrite" Bounds="848.125061035156,786.627685546875,108.752868652344,29.8881225585938" Weight="1" />
    <Link Source="@62" Target="@16" Category="CodeSchema_Calls" Bounds="802.205078125,786.627685546875,55.9348754882813,80.2191772460938" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@64" Target="@101" Category="CodeSchema_Calls" Bounds="1456.23600439366,201.702216473014,192.439582787229,162.473749239204" Weight="1" />
    <Link Source="@64" Target="@127" Category="CodeSchema_FieldRead" Bounds="1495.22236635206,201.160931028037,129.352605530531,28.9690215231025" Weight="1" />
    <Link Source="@64" Target="@139" Category="CodeSchema_FieldRead" Bounds="1496.49890136719,198.868103027344,268.66748046875,34.7377014160156" Weight="1" />
    <Link Source="@64" Target="@4" Category="CodeSchema_AttributeUse" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@65" Target="@148" Category="CodeSchema_Calls" Bounds="755.283709453859,1073.75562272536,64.069255987427,26.5541002955063" IsTargetVirtualized="True" Weight="2" />
    <Link Source="@66" Target="@127" Category="CodeSchema_FieldRead" Bounds="1639.79614257813,146.462219238281,29.6318359375,80.1324615478516" Weight="1" />
    <Link Source="@66" Target="@139" Category="CodeSchema_FieldRead" Bounds="1704.51447441336,146.462216473014,102.732557495475,79.7223331713266" Weight="1" />
    <Link Source="@66" Target="@4" Category="CodeSchema_AttributeUse" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@66" Target="@91" Category="CodeSchema_Calls" Bounds="1465.91425603039,146.462166473014,212.46787124279,271.670469711895" Weight="1" />
    <Link Source="@67" Target="@105" Category="CodeSchema_Calls" Bounds="1708.60592534216,201.702316473014,472.00167195867,383.563984036123" Weight="1" />
    <Link Source="@67" Target="@139" Category="CodeSchema_FieldRead" Bounds="1896.10632324219,197.956695556641,246.667602539063,37.2962188720703" Weight="1" />
    <Link Source="@67" Target="@4" Category="CodeSchema_AttributeUse" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@70" Target="@139" Category="CodeSchema_FieldRead" Bounds="1893.06279309476,200.083711095028,125.764827326367,29.5611891935356" Weight="1" />
    <Link Source="@70" Target="@4" Category="CodeSchema_AttributeUse" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@70" Target="@79" Category="CodeSchema_Calls" Bounds="1062.73794723687,201.702216473014,971.13680702617,385.916423567534" Weight="1" />
    <Link Source="@71" Target="@139" Category="CodeSchema_FieldRead" Bounds="1849.62048339844,146.462219238281,84.8543701171875,78.8794097900391" Weight="1" />
    <Link Source="@71" Target="@4" Category="CodeSchema_AttributeUse" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@71" Target="@47" Category="CodeSchema_Calls" Bounds="936.02835522243,146.462116473014,986.065127243892,440.807163200834" Weight="1" />
    <Link Source="@72" Target="@139" Category="CodeSchema_FieldRead" Bounds="1895.74816894531,146.462219238281,155.830200195313,85.2666320800781" Weight="1" />
    <Link Source="@72" Target="@4" Category="CodeSchema_AttributeUse" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@72" Target="@80" Category="CodeSchema_Calls" Bounds="1570.85661670362,146.462216473014,493.962930471658,438.505064717533" Weight="1" />
    <Link Source="@73" Target="@148" Category="CodeSchema_Calls" Bounds="609.397264091571,1073.75562272536,67.4518437344035,26.6887340989044" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@73" Target="@76" Category="CodeSchema_Calls" Bounds="573.538554339733,1073.75562272536,2.89028694522096,33.9865221159039" Weight="1" />
    <Link Source="@74" Target="@139" Category="CodeSchema_FieldRead" Bounds="1833.06884765625,114.277839660645,47.6824951171875,110.717674255371" Weight="1" />
    <Link Source="@74" Target="@4" Category="CodeSchema_AttributeUse" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@74" Target="@48" Category="CodeSchema_Calls" Bounds="1302.6852228884,114.277838674767,507.176017043774,470.543160969582" Weight="1" />
    <Link Source="@75" Target="@139" Category="CodeSchema_FieldRead" Bounds="1701.05627441406,91.2222137451172,102.759155273438,135.517440795898" Weight="1" />
    <Link Source="@75" Target="@4" Category="CodeSchema_AttributeUse" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@75" Target="@49" Category="CodeSchema_Calls" Bounds="971.476056810261,91.2220664730142,690.408944113607,549.356576239167" Weight="1" />
    <Link Source="@77" Target="@106" Category="CodeSchema_Calls" Bounds="1136.66707609784,91.2221164730142,816.181757244505,549.931260220426" Weight="1" />
    <Link Source="@77" Target="@139" Category="CodeSchema_FieldRead" Bounds="1851.46459960938,91.2222137451172,96.1429443359375,134.440551757813" Weight="3" />
    <Link Source="@77" Target="@4" Category="CodeSchema_AttributeUse" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@77" Target="@47" Category="CodeSchema_Calls" Bounds="933.435208395401,91.2221164730142,1012.3729017638,495.761984726145" Weight="1" />
    <Link Source="@77" Target="@8" Category="References" IsTargetVirtualized="True" Weight="3">
      <Category Ref="CodeSchema_Calls" />
    </Link>
    <Link Source="@77" Target="@94" Category="CodeSchema_Calls" Bounds="698.497778843353,91.2221164730142,1241.51657299676,496.378754123617" Weight="1" />
    <Link Source="@83" Target="@107" Category="CodeSchema_FunctionPointer" Bounds="493.169281005859,59.0424499511719,46.9554138183594,25.6813507080078" Weight="2" />
    <Link Source="@83" Target="@124" Category="CodeSchema_FieldRead" Bounds="470.095062255859,59.0424499511719,146.907196044922,84.6542358398438" Weight="1" />
    <Link Source="@83" Target="@13" Category="CodeSchema_AttributeUse" IsTargetVirtualized="True" Weight="3" />
    <Link Source="@83" Target="@135" Category="CodeSchema_FieldRead" Bounds="438.095062255859,59.0424499511719,24.6893615722656,76.2400054931641" Weight="3" />
    <Link Source="@83" Target="@8" Category="CodeSchema_Calls" IsTargetVirtualized="True" Weight="6" />
    <Link Source="@84" Target="@124" Category="CodeSchema_FieldRead" Bounds="701.059936523438,114.282447814941,18.7545166015625,23.0222396850586" Weight="1" />
    <Link Source="@84" Target="@135" Category="CodeSchema_FieldRead" Bounds="492.434875488281,112.647361755371,179.593688964844,33.9751815795898" Weight="1" />
    <Link Source="@84" Target="@58" Category="CodeSchema_Calls" Bounds="784.696472167969,114.282447814941,125.046264648438,28.9019241333008" Weight="1" />
    <Link Source="@84" Target="@8" Category="CodeSchema_Calls" IsTargetVirtualized="True" Weight="2" />
    <Link Source="@86" Target="@124" Category="CodeSchema_FieldRead" Bounds="733.191772460938,113.801986694336,86.8848266601563,27.7428741455078" Weight="1" />
    <Link Source="@86" Target="@58" Category="CodeSchema_Calls" Bounds="883.453857421875,114.282447814941,52.2250366210938,25.9901809692383" Weight="1" />
    <Link Source="@86" Target="@8" Category="CodeSchema_Calls" IsTargetVirtualized="True" Weight="2" />
    <Link Source="@88" Target="@102" Category="CodeSchema_Calls" Bounds="-34.4045836859141,-29.8367215117478,1591.6616189397,488.398504637234" Weight="1" />
    <Link Source="@88" Target="@110" Category="CodeSchema_Calls" Bounds="872.395874023438,114.282447814941,69.80126953125,26.7765426635742" Weight="2" />
    <Link Source="@88" Target="@111" Category="CodeSchema_Calls" Bounds="-39.2545585086286,-29.8367215117478,1085.46526487253,377.601497656941" Weight="1" />
    <Link Source="@88" Target="@124" Category="CodeSchema_FieldRead" Bounds="753.326232910156,110.701171875,174.317199707031,33.2044219970703" Weight="2" />
    <Link Source="@88" Target="@138" Category="CodeSchema_FieldRead" Bounds="1005.25146484375,114.282447814941,63.3848876953125,26.5256118774414" Weight="3" />
    <Link Source="@88" Target="@58" Category="CodeSchema_Calls" Bounds="971.437622070313,114.282447814941,2.28668212890625,21.052619934082" Weight="1" />
    <Link Source="@88" Target="@8" Category="CodeSchema_Calls" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@88" Target="@91" Category="CodeSchema_Calls" Bounds="-28.9554777097503,-30.7193553618225,1724.13777423766,434.481935107344" Weight="1" />
    <Link Source="@89" Target="@13" Category="CodeSchema_AttributeUse" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@89" Target="@96" Category="CodeSchema_Calls" Bounds="959.095092773438,224.762451171875,0,21" Weight="2" />
    <Link Source="@9" Target="@22" Category="References" IsSourceVirtualized="True" Weight="4" />
    <Link Source="@9" Target="@3" Category="References" Bounds="616.067687988281,851.274475097656,331.784851074219,543.326965332031" IsSourceVirtualized="True" IsTargetVirtualized="True" Weight="5">
      <Category Ref="CodeSchema_Calls" />
    </Link>
    <Link Source="@90" Target="@73" Category="CodeSchema_Calls" Bounds="576.817421322404,1012.14624772536,0.382513483353932,27.3702537892774" Weight="1" />
    <Link Source="@91" Target="@130" Category="CodeSchema_FieldRead" Bounds="1450.52160644531,450.458465576172,0.3802490234375,21.00146484375" Weight="1" />
    <Link Source="@91" Target="@65" Category="CodeSchema_Calls" Weight="1" />
    <Link Source="@93" Target="@102" Category="CodeSchema_Calls" Weight="1" />
    <Link Source="@93" Target="@138" Category="CodeSchema_FieldRead" Bounds="1107.09509277344,114.282447814941,0,21.0000076293945" Weight="1" />
    <Link Source="@93" Target="@58" Category="CodeSchema_Calls" Bounds="1008.97766113281,114.282447814941,66.5902099609375,26.655403137207" Weight="1" />
    <Link Source="@93" Target="@8" Category="CodeSchema_Calls" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@94" Target="@78" Category="CodeSchema_Calls" Bounds="658.369812011719,616.178466796875,0,21" Weight="1" />
    <Link Source="@95" Target="@102" Category="CodeSchema_Calls" Weight="1" />
    <Link Source="@95" Target="@138" Category="CodeSchema_FieldRead" Bounds="1149.10314941406,114.282447814941,71.40869140625,26.834114074707" Weight="1" />
    <Link Source="@95" Target="@58" Category="CodeSchema_Calls" Bounds="1029.32446289063,113.488052368164,163.758666992188,31.7404632568359" Weight="1" />
    <Link Source="@95" Target="@8" Category="CodeSchema_Calls" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@96" Target="@13" Category="CodeSchema_AttributeUse" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@98" Target="@140" Category="CodeSchema_FieldWrite" Bounds="1492.80102539063,1329.23986816406,0,21" Weight="1" />
    <Link Source="@98" Target="@149" Category="CodeSchema_Calls" Bounds="1519.53051757813,1329.23986816406,55.6566162109375,26.2774658203125" IsTargetVirtualized="True" Weight="1" />
    <Link Source="@99" Target="@131" Category="CodeSchema_FieldWrite" Bounds="1412.79931640625,841.656555175781,61.25146484375,26.4338989257813" Weight="1" />
    <Link Source="@99" Target="@136" Category="CodeSchema_FieldWrite" Bounds="1503.97875976563,841.656555175781,1.1412353515625,21.0132446289063" Weight="1" />
    <Link Source="@99" Target="@141" Category="CodeSchema_FieldWrite" Bounds="1528.88061523438,841.656555175781,52.75390625,26.0189819335938" Weight="1" />
    <Link Source="@99" Target="@22" Category="References" Bounds="564.620534240931,630.732777601528,223.8239164288,112.279744747416" Weight="1" />
    <Link Source="@99" Target="@3" Category="References" IsTargetVirtualized="True" Weight="1" />
  </Links>
  <Categories>
    <Category Id="CodeSchema_Assembly" Label="程序集" BasedOn="File" CanBeDataDriven="True" DefaultAction="Microsoft.Contains" Icon="CodeSchema_Assembly" NavigationActionLabel="程序集" />
    <Category Id="CodeSchema_AttributeUse" Label="使用特性" CanBeDataDriven="True" CanLinkedNodesBeDataDriven="True" IncomingActionLabel="使用者" OutgoingActionLabel="使用特性" />
    <Category Id="CodeSchema_Calls" Label="调用" CanBeDataDriven="True" CanLinkedNodesBeDataDriven="True" IncomingActionLabel="调用者" OutgoingActionLabel="调用" />
    <Category Id="CodeSchema_Class" Label="类" BasedOn="CodeSchema_Type" CanBeDataDriven="True" DefaultAction="Node:Both:CodeSchema_Member" Icon="CodeSchema_Class" NavigationActionLabel="类" />
    <Category Id="CodeSchema_Field" Label="字段" BasedOn="CodeSchema_Member" CanBeDataDriven="True" DefaultAction="Microsoft.Contains" Icon="CodeSchema_Field" NavigationActionLabel="字段" />
    <Category Id="CodeSchema_FieldRead" Label="字段读取" BasedOn="CodeSchema_FieldReference" CanBeDataDriven="True" CanLinkedNodesBeDataDriven="True" IncomingActionLabel="读取方" OutgoingActionLabel="读取字段" />
    <Category Id="CodeSchema_FieldReference" Label="字段引用" CanBeDataDriven="True" CanLinkedNodesBeDataDriven="True" IncomingActionLabel="引用者" OutgoingActionLabel="引用字段" />
    <Category Id="CodeSchema_FieldWrite" Label="字段写入" BasedOn="CodeSchema_FieldReference" CanBeDataDriven="True" CanLinkedNodesBeDataDriven="True" IncomingActionLabel="写入方" OutgoingActionLabel="写入字段" />
    <Category Id="CodeSchema_FunctionPointer" Label="函数指针" CanBeDataDriven="True" CanLinkedNodesBeDataDriven="True" IncomingActionLabel="函数指针" OutgoingActionLabel="函数指针" />
    <Category Id="CodeSchema_Member" Label="编号" CanBeDataDriven="True" DefaultAction="Microsoft.Contains" Icon="CodeSchema_Field" NavigationActionLabel="成员" />
    <Category Id="CodeSchema_Method" Label="方法" BasedOn="CodeSchema_Member" CanBeDataDriven="True" DefaultAction="Link:Forward:CodeSchema_Calls" Icon="CodeSchema_Method" NavigationActionLabel="方法" />
    <Category Id="CodeSchema_Namespace" Label="命名空间" CanBeDataDriven="True" DefaultAction="Node:Both:CodeSchema_Type" Icon="CodeSchema_Namespace" NavigationActionLabel="命名空间" />
    <Category Id="CodeSchema_Property" Label="属性" BasedOn="CodeSchema_Member" CanBeDataDriven="True" DefaultAction="Microsoft.Contains" Icon="CodeSchema_Property" NavigationActionLabel="属性" />
    <Category Id="CodeSchema_ReturnTypeLink" Label="返回" CanBeDataDriven="True" CanLinkedNodesBeDataDriven="True" IncomingActionLabel="返回类型" OutgoingActionLabel="返回类型" />
    <Category Id="CodeSchema_Type" Label="类型" CanBeDataDriven="True" DefaultAction="Node:Both:CodeSchema_Member" Icon="CodeSchema_Class" NavigationActionLabel="类型" />
    <Category Id="Contains" Label="包含" Description="链接的源是否包含目标对象" CanBeDataDriven="False" CanLinkedNodesBeDataDriven="True" IncomingActionLabel="包含者" IsContainment="True" OutgoingActionLabel="包含" />
    <Category Id="File" Label="文件" CanBeDataDriven="True" DefaultAction="Microsoft.Contains" Icon="File" NavigationActionLabel="文件" />
    <Category Id="References" Label="引用" CanBeDataDriven="True" CanLinkedNodesBeDataDriven="True" IncomingActionLabel="引用者" OutgoingActionLabel="引用" />
  </Categories>
  <Properties>
    <Property Id="AssemblyTimestamp" DataType="System.DateTime" />
    <Property Id="Bounds" DataType="System.Windows.Rect" />
    <Property Id="CanBeDataDriven" Label="CanBeDataDriven" Description="CanBeDataDriven" DataType="System.Boolean" />
    <Property Id="CanLinkedNodesBeDataDriven" Label="CanLinkedNodesBeDataDriven" Description="CanLinkedNodesBeDataDriven" DataType="System.Boolean" />
    <Property Id="CodeSchemaProperty_IsConstructor" Label="是构造函数" Description="一个标志，用于指示该方法是构造函数" DataType="System.Boolean" />
    <Property Id="CodeSchemaProperty_IsInternal" Label="是内部的" Description="一个标志，用于指示该方法是“内部”方法" DataType="System.Boolean" />
    <Property Id="CodeSchemaProperty_IsPrivate" Label="是私有的" Description="一个标志，用于指示该范围是“私有”范围" DataType="System.Boolean" />
    <Property Id="CodeSchemaProperty_IsProtected" Label="是受保护的" Description="一个标志，用于指示该范围是“受保护”范围" DataType="System.Boolean" />
    <Property Id="CodeSchemaProperty_IsPublic" Label="是公共的" Description="一个标志，用于指示该范围是“公共”范围" DataType="System.Boolean" />
    <Property Id="CodeSchemaProperty_IsSpecialName" Label="是特殊名称" Description="一个标志，用于指示某些编译器以特殊方式对待该方法" DataType="System.Boolean" />
    <Property Id="CodeSchemaProperty_IsStatic" Label="是静态的" Description="一个标志，用于指示该成员是静态成员" DataType="System.Boolean" />
    <Property Id="CodeSchemaProperty_IsVirtual" Label="是虚拟的" Description="一个标志，用于指示可以重写该方法" DataType="System.Boolean" />
    <Property Id="CodeSchemaProperty_StrongName" Label="StrongName" Description="StrongName" DataType="System.String" />
    <Property Id="CommonLabel" DataType="System.String" />
    <Property Id="DataVirtualized" Label="已虚拟化数据" Description="如果为 true，则图中可以包含表示虚拟化节点/链接(即，不在图中实际创建)的数据的节点和链接。" DataType="System.Boolean" />
    <Property Id="DefaultAction" Label="DefaultAction" Description="DefaultAction" DataType="System.String" />
    <Property Id="DelayedChildNodesState" Label="延迟的子节点状态" Description="如果未指定延迟的子节点状态，则为 Unspecified。如果尚未将组包含的子节点提取到关系图中，则为 NotFetched。如果已提取组内的所有延迟的子节点，则为 Fetched。" DataType="Microsoft.VisualStudio.GraphModel.DelayedDataState" />
    <Property Id="DelayedCrossGroupLinksState" Label="延迟的跨组链接状态" Description="如果未指定延迟的跨组链接状态，则为 Unspecified。如果尚未将此节点上延迟的跨组链接提取到关系图中，则为 NotFetched。如果已提取所有延迟的跨组链接，则为 Fetched。" DataType="Microsoft.VisualStudio.GraphModel.DelayedDataState" />
    <Property Id="Expression" DataType="System.String" />
    <Property Id="FetchedChildrenCount" DataType="System.Int32" />
    <Property Id="FetchingParent" DataType="Microsoft.VisualStudio.GraphModel.GraphNodeId" />
    <Property Id="FilePath" Label="文件路径" Description="文件路径" DataType="System.String" />
    <Property Id="FilterState" DataType="System.String" />
    <Property Id="Group" Label="组" Description="将节点显示为组" DataType="Microsoft.VisualStudio.GraphModel.GraphGroupStyle" />
    <Property Id="GroupLabel" DataType="System.String" />
    <Property Id="Icon" Label="图标" Description="图标" DataType="System.String" />
    <Property Id="IncomingActionLabel" Label="IncomingActionLabel" Description="IncomingActionLabel" DataType="System.String" />
    <Property Id="IsContainment" DataType="System.Boolean" />
    <Property Id="IsEnabled" DataType="System.Boolean" />
    <Property Id="IsSourceVirtualized" Label="已虚拟化链接源" Description="如果为 true，则链接源端包含虚拟化节点/链接(即，不在图中实际创建)的数据。" DataType="System.Boolean" />
    <Property Id="IsTargetVirtualized" Label="已虚拟化链接目标" Description="如果为 true，则链接目标端包含虚拟化节点/链接(即，不在图中实际创建)的数据。" DataType="System.Boolean" />
    <Property Id="Label" Label="标签" Description="可批注对象的可显示标签" DataType="System.String" />
    <Property Id="Layout" DataType="System.String" />
    <Property Id="NavigationActionLabel" Label="NavigationActionLabel" Description="NavigationActionLabel" DataType="System.String" />
    <Property Id="OutgoingActionLabel" Label="OutgoingActionLabel" Description="OutgoingActionLabel" DataType="System.String" />
    <Property Id="SourceLocation" Label="起始行号" DataType="Microsoft.VisualStudio.GraphModel.CodeSchema.SourceLocation" />
    <Property Id="TargetType" DataType="System.Type" />
    <Property Id="UseManualLocation" DataType="System.Boolean" />
    <Property Id="Value" DataType="System.String" />
    <Property Id="ValueLabel" DataType="System.String" />
    <Property Id="Visibility" Label="可见性" Description="定义关系图中的节点是否可见" DataType="System.Windows.Visibility" />
    <Property Id="Weight" Label="粗细" Description="粗细" DataType="System.Double" />
    <Property Id="ZoomLevel" DataType="System.String" />
  </Properties>
  <QualifiedNames>
    <Name Id="ArrayRank" Label="数组秩" ValueType="System.String" />
    <Name Id="Assembly" Label="程序集" ValueType="Uri" />
    <Name Id="GenericArguments" Label="泛型实参" ValueType="Microsoft.VisualStudio.GraphModel.GraphNodeIdCollection" />
    <Name Id="GenericParameterCount" Label="泛型形参计数" ValueType="System.String" />
    <Name Id="Member" Label="编号" ValueType="System.Object" />
    <Name Id="Name" Label="名称" ValueType="System.String" />
    <Name Id="Namespace" Label="命名空间" ValueType="System.String" />
    <Name Id="OverloadingParameters" Label="参数" ValueType="Microsoft.VisualStudio.GraphModel.GraphNodeIdCollection" Formatter="NameValueNoEscape" />
    <Name Id="ParentType" Label="父类型" ValueType="System.Object" />
    <Name Id="Type" Label="类型" ValueType="System.Object" />
  </QualifiedNames>
  <IdentifierAliases>
    <Alias n="1" Uri="Assembly=$(a5f35672-10fb-428a-abf3-eb9b726c48b6.OutputPathUri)" />
    <Alias n="2" Id="(@1)" />
    <Alias n="3" Id="(@1 Namespace=IoTServer)" />
    <Alias n="4" Id="(@1 Namespace=IoTServer.Common)" />
    <Alias n="5" Id="Namespace=IoTServer.Controllers" />
    <Alias n="6" Id="(@1 @5)" />
    <Alias n="7" Id="(@1 Namespace=IoTServer.Features)" />
    <Alias n="8" Id="(@1 Namespace=IoTServer.Models)" />
    <Alias n="9" Id="(@1 Namespace=IoTServer.RpcControllers)" />
    <Alias n="10" Id="Namespace=IoTServer.Services" />
    <Alias n="11" Id="(@1 @10)" />
    <Alias n="12" Id="(@1 Namespace=Microsoft.CodeAnalysis)" />
    <Alias n="13" Id="(@1 Namespace=System.Runtime.CompilerServices)" />
    <Alias n="14" Id="Type=(Name=MyDeferredQueue ParentType=QueueService)" />
    <Alias n="15" Id="(@1 @10 @14)" />
    <Alias n="16" Id="(@1 @10 Type=(Name=DayQueue ParentType=SegmentService))" />
    <Alias n="17" Id="Type=DataService" />
    <Alias n="18" Id="(@1 @10 @17)" />
    <Alias n="19" Id="Type=MqttController" />
    <Alias n="20" Id="(@1 @10 @19)" />
    <Alias n="21" Id="Type=QueueService" />
    <Alias n="22" Id="(@1 @10 @21)" />
    <Alias n="23" Id="Type=SegmentService" />
    <Alias n="24" Id="(@1 @10 @23)" />
    <Alias n="25" Id="Type=ThingController" />
    <Alias n="26" Id="(@1 @5 @25)" />
    <Alias n="27" Id="Type=ThingService" />
    <Alias n="28" Id="(@1 @10 @27)" />
    <Alias n="29" Uri="Assembly=$(ProgramFilesUri)/dotnet/packs/Microsoft.NETCore.App.Ref/7.0.3/ref/net7.0/System.Runtime.dll" />
    <Alias n="30" Id="Namespace=System.Collections.Generic" />
    <Alias n="31" Id="GenericParameterCount=1" />
    <Alias n="32" Id="Namespace=System" />
    <Alias n="33" Id="(@29 @32 Type=Object)" />
    <Alias n="34" Id="(@1 @10 @14 Member=(Name=ProcessAll OverloadingParameters=[(@29 @30 Type=(Name=ICollection @31 GenericArguments=[@33]))]))" />
    <Alias n="35" Id="(@1 @10 @14 Member=(Name=Process OverloadingParameters=[(@29 @30 Type=(Name=IList @31 GenericArguments=[@33]))]))" />
    <Alias n="36" Uri="Assembly=file:///C:/Users/<USER>/.nuget/packages/newlife.iot/1.7.2023.205/lib/netstandard2.1/NewLife.IoT.dll" />
    <Alias n="37" Id="Namespace=NewLife.IoT.ThingModels" />
    <Alias n="38" Id="ArrayRank=1" />
    <Alias n="39" Id="(@1 @5 @25 Member=(Name=PostDatas OverloadingParameters=[(@36 @37 Type=(Name=DataModels @38 ParentType=DataModels))]))" />
    <Alias n="40" Id="(@1 @5 @25 Member=(Name=PostEvents OverloadingParameters=[(@36 @37 Type=(Name=EventModels @38 ParentType=EventModels))]))" />
    <Alias n="41" Id="(@1 @5 @25 Member=(Name=PostProperties OverloadingParameters=[(@36 @37 Type=(Name=PropertyModels @38 ParentType=PropertyModels))]))" />
    <Alias n="42" Id="(@1 @5 @25 Member=(Name=PostData OverloadingParameters=[(@36 @37 Type=DataModels)]))" />
    <Alias n="43" Uri="Assembly=$(060707b1-d0e3-4ceb-aa2e-94c986a18b71.OutputPathUri)" />
    <Alias n="44" Id="Namespace=IoT.Data" />
    <Alias n="45" Id="(@43 @44 Type=Device)" />
    <Alias n="46" Id="OverloadingParameters=[@45]" />
    <Alias n="47" Id="(@1 @10 @27 Member=(Name=GetPoints @46))" />
    <Alias n="48" Id="(@1 @10 @27 Member=(Name=GetShadow @46))" />
    <Alias n="49" Id="(@1 @10 @27 Member=(Name=GetSpecification @46))" />
    <Alias n="50" Id="(@1 @10 @17 Member=(Name=AddHistory OverloadingParameters=[(@43 @44 Type=DeviceHistory)]))" />
    <Alias n="51" Id="(@1 @5 @25 Member=(Name=PostEvent OverloadingParameters=[(@36 @37 Type=EventModels)]))" />
    <Alias n="52" Id="(@1 @10 @21 Member=(Name=AddData OverloadingParameters=[(@43 @44 Type=IDeviceData)]))" />
    <Alias n="53" Id="(@1 @10 @21 Member=(Name=AddEvent OverloadingParameters=[(@43 @44 Type=IDeviceEvent)]))" />
    <Alias n="54" Id="(@1 @10 @21 Member=(Name=AddHistory OverloadingParameters=[(@43 @44 Type=IDeviceHistory)]))" />
    <Alias n="55" Id="(@1 @10 @27 Member=(Name=UpdateProperty OverloadingParameters=[(@43 @44 Type=IDeviceProperty)]))" />
    <Alias n="56" Uri="Assembly=file:///C:/Users/<USER>/.nuget/packages/newlife.core/10.1.2023.214-beta1527/lib/net7.0/NewLife.Core.dll" />
    <Alias n="57" Id="(@56 Namespace=NewLife.Net Type=INetSession)" />
    <Alias n="58" Id="(@1 @10 @19 Member=(Name=CheckLogin OverloadingParameters=[@57]))" />
    <Alias n="59" Id="(@29 @32 Type=Int64)" />
    <Alias n="60" Id="(@1 @10 @21 Member=(Name=GetReplyQueue OverloadingParameters=[@59]))" />
    <Alias n="61" Id="Name=.ctor" />
    <Alias n="62" Id="(@1 @10 @23 Member=(@61 OverloadingParameters=[(@56 Namespace=NewLife.Log Type=ITracer)]))" />
    <Alias n="63" Id="(@1 @10 @19 Member=(Name=Serialize OverloadingParameters=[@33]))" />
    <Alias n="64" Id="(@1 @5 @25 Member=(Name=PostProperty OverloadingParameters=[(@36 @37 Type=PropertyModels)]))" />
    <Alias n="65" Id="(@1 @10 @21 Member=(Name=Publish OverloadingParameters=[(@36 @37 Type=ServiceReplyModel)]))" />
    <Alias n="66" Id="(@1 @5 @25 Member=(Name=ServiceReply OverloadingParameters=[(@36 @37 Type=ServiceReplyModel)]))" />
    <Alias n="67" Id="(@1 @5 @25 Member=(Name=PostShadow OverloadingParameters=[(@36 @37 Type=ShadowModel)]))" />
    <Alias n="68" Id="(@29 @32 Type=String)" />
    <Alias n="69" Id="OverloadingParameters=[@68]" />
    <Alias n="70" Id="(@1 @5 @25 Member=(Name=GetConfig @69))" />
    <Alias n="71" Id="(@1 @5 @25 Member=(Name=GetPoints @69))" />
    <Alias n="72" Id="(@1 @5 @25 Member=(Name=GetProperty @69))" />
    <Alias n="73" Id="(@1 @10 @21 Member=(Name=GetQueue @69))" />
    <Alias n="74" Id="(@1 @5 @25 Member=(Name=GetShadow @69))" />
    <Alias n="75" Id="(@1 @5 @25 Member=(Name=GetSpecification @69))" />
    <Alias n="76" Id="(@1 @10 @21 Member=(Name=GetTopic @69))" />
    <Alias n="77" Id="(@1 @5 @25 Member=(Name=PostSpecification OverloadingParameters=[(@1 Namespace=IoTServer.Models Type=ThingSpecModel)]))" />
    <Alias n="78" Id="(@1 @10 @27 Member=(Name=SetPoints OverloadingParameters=[@45,(@29 @30 Type=(Name=IList @31 GenericArguments=[(@36 @37 Type=IPoint)]))]))" />
    <Alias n="79" Id="(@1 @10 @27 Member=(Name=GetConfig OverloadingParameters=[@45,(@29 @32 Type=(Name=String @38 ParentType=String))]))" />
    <Alias n="80" Id="(@1 @10 @27 Member=(Name=GetProperty OverloadingParameters=[@45,(@29 @32 Type=(Name=String @38 ParentType=String))]))" />
    <Alias n="81" Uri="Assembly=file:///C:/Users/<USER>/.nuget/packages/newlife.mqtt/1.2.2023.203/lib/netstandard2.1/NewLife.MQTT.dll" />
    <Alias n="82" Id="Namespace=NewLife.MQTT.Messaging" />
    <Alias n="83" Id="(@1 @10 @19 Member=(Name=OnConnect OverloadingParameters=[@57,(@81 @82 Type=ConnectMessage)]))" />
    <Alias n="84" Id="(@1 @10 @19 Member=(Name=OnDisconnect OverloadingParameters=[@57,(@81 @82 Type=DisconnectMessage)]))" />
    <Alias n="85" Id="(@29 @32 Type=Int32)" />
    <Alias n="86" Id="(@1 @10 @19 Member=(Name=OnPing OverloadingParameters=[@57,(@81 @82 Type=PingRequest)]))" />
    <Alias n="87" Id="(@1 @10 @27 Member=(Name=FixData OverloadingParameters=[@33,(@43 @44 Type=ProductFunction)]))" />
    <Alias n="88" Id="(@1 @10 @19 Member=(Name=OnPublish OverloadingParameters=[@57,(@81 @82 Type=PublishMessage)]))" />
    <Alias n="89" Id="(@1 @10 @19 Member=(Name=PublishAsync OverloadingParameters=[@57,(@81 @82 Type=PublishMessage)]))" />
    <Alias n="90" Id="(@1 @10 @21 Member=(Name=Publish OverloadingParameters=[@68,(@36 @37 Type=ServiceModel)]))" />
    <Alias n="91" Id="(@1 @10 @27 Member=(Name=ServiceReply OverloadingParameters=[@45,(@36 @37 Type=ServiceReplyModel)]))" />
    <Alias n="92" Id="(@1 @10 @27 Member=(Name=GetProperty OverloadingParameters=[@45,@68]))" />
    <Alias n="93" Id="(@1 @10 @19 Member=(Name=OnSubscribe OverloadingParameters=[@57,(@81 @82 Type=SubscribeMessage)]))" />
    <Alias n="94" Id="(@1 @10 @27 Member=(Name=SetPoints OverloadingParameters=[@45,(@36 Namespace=NewLife.IoT.ThingSpecification Type=ThingSpec)]))" />
    <Alias n="95" Id="(@1 @10 @19 Member=(Name=OnUnsubscribe OverloadingParameters=[@57,(@81 @82 Type=UnsubscribeMessage)]))" />
    <Alias n="96" Id="(@1 @10 @19 Member=(Name=SendAsync OverloadingParameters=[@57,(@81 @82 Type=MqttMessage),(@29 @32 Type=Boolean)]))" />
    <Alias n="97" Id="(@1 @10 @27 Member=(Name=VerifyModel OverloadingParameters=[@45,@68,(@43 Namespace=IoT.Data.Models Type=FunctionKinds)]))" />
    <Alias n="98" Id="(@1 @10 @14 Member=(@61 OverloadingParameters=[@68,(@56 Namespace=NewLife.Caching Type=(Name=IProducerConsumer @31 GenericArguments=[@33])),(@56 Namespace=NewLife.Log Type=ITracer)]))" />
    <Alias n="99" Id="(@1 @10 @17 Member=(@61 OverloadingParameters=[@22,(@1 Namespace=IoTServer Type=IoTSetting),(@56 Namespace=NewLife.Log Type=ITracer)]))" />
    <Alias n="100" Id="(@1 @10 @27 Member=(Name=PostEvent OverloadingParameters=[@45,(@36 @37 Type=(Name=EventModel @38 ParentType=EventModel)),@68]))" />
    <Alias n="101" Id="(@1 @10 @27 Member=(Name=PostProperty OverloadingParameters=[@45,(@36 @37 Type=(Name=PropertyModel @38 ParentType=PropertyModel)),@68]))" />
    <Alias n="102" Id="(@1 @10 @27 Member=(Name=PostEvent OverloadingParameters=[@45,(@36 @37 Type=EventModel),@68]))" />
    <Alias n="103" Id="(@1 @10 @17 Member=(Name=AddEvent OverloadingParameters=[@85,(@36 @37 Type=EventModel),@68]))" />
    <Alias n="104" Id="(@1 @10 @23 Member=(Name=AddSegment OverloadingParameters=[(@43 @44 Type=IDeviceProperty),@59,@68]))" />
    <Alias n="105" Id="(@1 @10 @27 Member=(Name=PostShadow OverloadingParameters=[@45,@68,@68]))" />
    <Alias n="106" Id="(@1 @10 @27 Member=(Name=PostSpecification OverloadingParameters=[(@36 Namespace=NewLife.IoT.ThingSpecification Type=ThingSpec),@68,@68]))" />
    <Alias n="107" Id="(@1 @10 @19 Member=(Name=ConsumeMessage OverloadingParameters=[@57,@45,@68,(@29 Namespace=System.Threading Type=CancellationTokenSource)]))" />
    <Alias n="108" Id="(@1 @10 @27 Member=(Name=InvokeService OverloadingParameters=[@45,@68,@68,(@29 @32 Type=DateTime)]))" />
    <Alias n="109" Id="(@1 @10 @27 Member=(Name=ValidRange OverloadingParameters=[(@43 @44 Type=ProductFunction),@85,(@43 @44 Type=DeviceProperty),@33]))" />
    <Alias n="110" Id="(@1 @10 @19 Member=(Name=PublishAsync OverloadingParameters=[@57,@68,@33,(@81 @82 Type=QualityOfService)]))" />
    <Alias n="111" Id="(@1 @10 @27 Member=(Name=PostData OverloadingParameters=[@45,(@36 @37 Type=DataModels),@68,@68]))" />
    <Alias n="112" Id="(@1 @10 @27 Member=(Name=BuildDataPoint OverloadingParameters=[@45,@68,@33,@59,@68]))" />
    <Alias n="113" Id="(@1 @10 @27 Member=(Name=SaveHistory OverloadingParameters=[@45,(@43 @44 Type=IDeviceProperty),@59,@68,@68]))" />
    <Alias n="114" Id="(@1 @10 @17 Member=(Name=AddData OverloadingParameters=[@85,@59,@68,@68,@68,@68]))" />
    <Alias n="116" Id="(@1 @10 @21 Member=_alarmQueue)" />
    <Alias n="117" Id="(@1 @10 @21 Member=_dataQueue)" />
    <Alias n="119" Id="(@1 @10 @27 Member=_dataService)" />
    <Alias n="120" Id="(@1 @10 @23 Member=_dayQueue)" />
    <Alias n="121" Id="(@1 @10 @21 Member=_defData)" />
    <Alias n="122" Id="(@1 @10 @21 Member=_defEvent)" />
    <Alias n="123" Id="(@1 @10 @21 Member=_defHistory)" />
    <Alias n="124" Id="(@1 @10 @19 Member=_deviceService)" />
    <Alias n="125" Id="(@1 @10 @21 Member=_eventQueue)" />
    <Alias n="126" Id="(@1 @10 @21 Member=_historyQueue)" />
    <Alias n="127" Id="(@1 @5 @25 Member=_hookService)" />
    <Alias n="128" Id="(@1 @10 @19 Member=_queue)" />
    <Alias n="130" Id="(@1 @10 @27 Member=_queue)" />
    <Alias n="131" Id="(@1 @10 @17 Member=_queueService)" />
    <Alias n="132" Id="(@1 @10 @21 Member=_redis)" />
    <Alias n="133" Id="(@1 @10 @27 Member=_ruleService)" />
    <Alias n="134" Id="(@1 @10 @27 Member=_segmentService)" />
    <Alias n="135" Id="(@1 @10 @19 Member=_Sessions)" />
    <Alias n="136" Id="(@1 @10 @17 Member=_setting)" />
    <Alias n="137" Id="(@1 @10 @27 Member=_setting)" />
    <Alias n="138" Id="(@1 @10 @19 Member=_thingService)" />
    <Alias n="139" Id="(@1 @5 @25 Member=_thingService)" />
    <Alias n="140" Id="(@1 @10 @14 Member=_tracer)" />
    <Alias n="141" Id="(@1 @10 @17 Member=_tracer)" />
    <Alias n="142" Id="(@1 @10 @23 Member=_tracer)" />
    <Alias n="143" Id="(@1 @10 @21 Member=GetAlarmQueue)" />
    <Alias n="144" Id="(@1 @10 @21 Member=GetDataQueue)" />
    <Alias n="145" Id="(@1 @10 @21 Member=GetEventQueue)" />
    <Alias n="146" Id="(@1 @10 @21 Member=GetHistoryQueue)" />
    <Alias n="147" Id="(@1 @10 @21 Member=GetRedis)" />
    <Alias n="148" Id="(@1 @10 @21 Member=Host)" />
    <Alias n="149" Id="(@1 @10 @14 Member=Queue)" />
  </IdentifierAliases>
  <Styles>
    <Style TargetType="Node" GroupLabel="结果" ValueLabel="True">
      <Condition Expression="HasCategory('QueryResult')" />
      <Setter Property="Background" Value="#FFBCFFBE" />
    </Style>
    <Style TargetType="Node" GroupLabel="测试项目" ValueLabel="测试项目">
      <Condition Expression="HasCategory('CodeMap_TestProject')" />
      <Setter Property="Icon" Value="CodeMap_TestProject" />
      <Setter Property="Background" Value="#FF307A69" />
    </Style>
    <Style TargetType="Node" GroupLabel="Web 项目" ValueLabel="Web 项目">
      <Condition Expression="HasCategory('CodeMap_WebProject')" />
      <Setter Property="Icon" Value="CodeMap_WebProject" />
    </Style>
    <Style TargetType="Node" GroupLabel="Windows 应用商店项目" ValueLabel="Windows 应用商店项目">
      <Condition Expression="HasCategory('CodeMap_WindowsStoreProject')" />
      <Setter Property="Icon" Value="CodeMap_WindowsStoreProject" />
    </Style>
    <Style TargetType="Node" GroupLabel="电话项目" ValueLabel="电话项目">
      <Condition Expression="HasCategory('CodeMap_PhoneProject')" />
      <Setter Property="Icon" Value="CodeMap_PhoneProject" />
    </Style>
    <Style TargetType="Node" GroupLabel="可移植库" ValueLabel="可移植库">
      <Condition Expression="HasCategory('CodeMap_PortableLibraryProject')" />
      <Setter Property="Icon" Value="CodeMap_PortableLibraryProject" />
    </Style>
    <Style TargetType="Node" GroupLabel="WPF 项目" ValueLabel="WPF 项目">
      <Condition Expression="HasCategory('CodeMap_WpfProject')" />
      <Setter Property="Icon" Value="CodeMap_WpfProject" />
    </Style>
    <Style TargetType="Node" GroupLabel="VSIX 项目" ValueLabel="VSIX 项目">
      <Condition Expression="HasCategory('CodeMap_VsixProject')" />
      <Setter Property="Icon" Value="CodeMap_VsixProject" />
    </Style>
    <Style TargetType="Node" GroupLabel="建模项目" ValueLabel="建模项目">
      <Condition Expression="HasCategory('CodeMap_ModelingProject')" />
      <Setter Property="Icon" Value="CodeMap_ModelingProject" />
    </Style>
    <Style TargetType="Node" IsEnabled="false" GroupLabel="程序集" ValueLabel="具有类别">
      <Condition Expression="HasCategory('CodeSchema_Assembly')" />
      <Setter Property="Background" Value="#FF094167" />
      <Setter Property="Stroke" Value="#FF094167" />
      <Setter Property="Icon" Value="CodeSchema_Assembly" />
    </Style>
    <Style TargetType="Node" IsEnabled="false" GroupLabel="命名空间" ValueLabel="具有类别">
      <Condition Expression="HasCategory('CodeSchema_Namespace')" />
      <Setter Property="Background" Value="#FF0E619A" />
      <Setter Property="Stroke" Value="#FF0E619A" />
      <Setter Property="Icon" Value="CodeSchema_Namespace" />
    </Style>
    <Style TargetType="Node" GroupLabel="接口" ValueLabel="具有类别">
      <Condition Expression="HasCategory('CodeSchema_Interface')" />
      <Setter Property="Background" Value="#FF1382CE" />
      <Setter Property="Stroke" Value="#FF1382CE" />
      <Setter Property="Icon" Value="CodeSchema_Interface" />
    </Style>
    <Style TargetType="Node" GroupLabel="结构" ValueLabel="具有类别">
      <Condition Expression="HasCategory('CodeSchema_Struct')" />
      <Setter Property="Background" Value="#FF1382CE" />
      <Setter Property="Stroke" Value="#FF1382CE" />
      <Setter Property="Icon" Value="CodeSchema_Struct" />
    </Style>
    <Style TargetType="Node" GroupLabel="枚举" ValueLabel="具有类别">
      <Condition Expression="HasCategory('CodeSchema_Enum')" />
      <Setter Property="Background" Value="#FF1382CE" />
      <Setter Property="Stroke" Value="#FF1382CE" />
      <Setter Property="Icon" Value="CodeSchema_Enum" />
      <Setter Property="LayoutSettings" Value="List" />
    </Style>
    <Style TargetType="Node" GroupLabel="委托" ValueLabel="具有类别">
      <Condition Expression="HasCategory('CodeSchema_Delegate')" />
      <Setter Property="Background" Value="#FF1382CE" />
      <Setter Property="Stroke" Value="#FF1382CE" />
      <Setter Property="Icon" Value="CodeSchema_Delegate" />
    </Style>
    <Style TargetType="Node" GroupLabel="类" ValueLabel="具有类别">
      <Condition Expression="HasCategory('CodeSchema_Type')" />
      <Setter Property="Background" Value="#FF0E70C0" />
      <Setter Property="Stroke" Value="#FF0E70C0" />
      <Setter Property="Icon" Value="CodeSchema_Class" />
    </Style>
    <Style TargetType="Node" GroupLabel="属性" ValueLabel="具有类别">
      <Condition Expression="HasCategory('CodeSchema_Property')" />
      <Setter Property="Background" Value="#FFE0E0E0" />
      <Setter Property="Stroke" Value="#FFE0E0E0" />
      <Setter Property="Icon" Value="CodeSchema_Property" />
    </Style>
    <Style TargetType="Node" GroupLabel="方法" ValueLabel="具有类别">
      <Condition Expression="HasCategory('CodeSchema_Method') Or HasCategory('CodeSchema_CallStackUnresolvedMethod')" />
      <Setter Property="Background" Value="#FFE0E0E0" />
      <Setter Property="Stroke" Value="#FFE0E0E0" />
      <Setter Property="Icon" Value="CodeSchema_Method" />
      <Setter Property="LayoutSettings" Value="List" />
    </Style>
    <Style TargetType="Node" GroupLabel="事件" ValueLabel="具有类别">
      <Condition Expression="HasCategory('CodeSchema_Event')" />
      <Setter Property="Background" Value="#FFE0E0E0" />
      <Setter Property="Stroke" Value="#FFE0E0E0" />
      <Setter Property="Icon" Value="CodeSchema_Event" />
    </Style>
    <Style TargetType="Node" GroupLabel="字段" ValueLabel="具有类别">
      <Condition Expression="HasCategory('CodeSchema_Field')" />
      <Setter Property="Background" Value="#FFE0E0E0" />
      <Setter Property="Stroke" Value="#FFE0E0E0" />
      <Setter Property="Icon" Value="CodeSchema_Field" />
    </Style>
    <Style TargetType="Node" GroupLabel="Out 参数" ValueLabel="具有类别">
      <Condition Expression="CodeSchemaProperty_IsOut = 'True'" />
      <Setter Property="Icon" Value="CodeSchema_OutParameter" />
    </Style>
    <Style TargetType="Node" GroupLabel="参数" ValueLabel="具有类别">
      <Condition Expression="HasCategory('CodeSchema_Parameter')" />
      <Setter Property="Icon" Value="CodeSchema_Parameter" />
    </Style>
    <Style TargetType="Node" GroupLabel="局部变量" ValueLabel="具有类别">
      <Condition Expression="HasCategory('CodeSchema_LocalExpression')" />
      <Setter Property="Icon" Value="CodeSchema_LocalExpression" />
    </Style>
    <Style TargetType="Node" GroupLabel="外部" ValueLabel="具有类别">
      <Condition Expression="HasCategory('Externals')" />
      <Setter Property="Background" Value="#FF424242" />
      <Setter Property="Stroke" Value="#FF424242" />
    </Style>
    <Style TargetType="Link" GroupLabel="继承自" ValueLabel="True">
      <Condition Expression="HasCategory('InheritsFrom')" />
      <Setter Property="Stroke" Value="#FF00A600" />
      <Setter Property="StrokeDashArray" Value="2 0" />
      <Setter Property="DrawArrow" Value="true" />
    </Style>
    <Style TargetType="Link" GroupLabel="实现" ValueLabel="True">
      <Condition Expression="HasCategory('Implements')" />
      <Setter Property="Stroke" Value="#8000A600" />
      <Setter Property="StrokeDashArray" Value="2 2" />
      <Setter Property="DrawArrow" Value="true" />
    </Style>
    <Style TargetType="Link" GroupLabel="调用" ValueLabel="True">
      <Condition Expression="HasCategory('CodeSchema_Calls')" />
      <Setter Property="Stroke" Value="#FFFF00FF" />
      <Setter Property="StrokeDashArray" Value="2 0" />
      <Setter Property="DrawArrow" Value="true" />
    </Style>
    <Style TargetType="Link" GroupLabel="函数指针" ValueLabel="True">
      <Condition Expression="HasCategory('CodeSchema_FunctionPointer')" />
      <Setter Property="Stroke" Value="#FFFF00FF" />
      <Setter Property="StrokeDashArray" Value="2 2" />
      <Setter Property="DrawArrow" Value="true" />
    </Style>
    <Style TargetType="Link" GroupLabel="字段读取" ValueLabel="True">
      <Condition Expression="HasCategory('CodeSchema_FieldRead')" />
      <Setter Property="Stroke" Value="#FF00AEEF" />
      <Setter Property="StrokeDashArray" Value="2 2" />
      <Setter Property="DrawArrow" Value="true" />
    </Style>
    <Style TargetType="Link" GroupLabel="字段写入" ValueLabel="True">
      <Condition Expression="HasCategory('CodeSchema_FieldWrite')" />
      <Setter Property="Stroke" Value="#FF00AEEF" />
      <Setter Property="DrawArrow" Value="true" />
      <Setter Property="IsHidden" Value="false" />
    </Style>
    <Style TargetType="Link" GroupLabel="Inherits From" ValueLabel="True" Visibility="Hidden">
      <Condition Expression="HasCategory('InheritsFrom') And Target.HasCategory('CodeSchema_Class')" />
      <Setter Property="TargetDecorator" Value="OpenArrow" />
    </Style>
    <Style TargetType="Link" GroupLabel="Implements" ValueLabel="True" Visibility="Hidden">
      <Condition Expression="HasCategory('Implements') And Target.HasCategory('CodeSchema_Interface')" />
      <Setter Property="TargetDecorator" Value="OpenArrow" />
    </Style>
    <Style TargetType="Link" GroupLabel="Comment Link" ValueLabel="True" Visibility="Hidden">
      <Condition Expression="Source.HasCategory('Comment')" />
      <Setter Property="Stroke" Value="#FFE5C365" />
    </Style>
    <Style TargetType="Node" GroupLabel="Cursor Location Changed" ValueLabel="True" Visibility="Hidden">
      <Condition Expression="IsCursorLocation" />
      <Setter Property="IndicatorWest" Value="WestIndicator" />
    </Style>
    <Style TargetType="Node" GroupLabel="Disabled Breakpoint Location Changed" ValueLabel="True" Visibility="Hidden">
      <Condition Expression="DisabledBreakpointCount" />
      <Setter Property="IndicatorWest" Value="WestIndicator" />
    </Style>
    <Style TargetType="Node" GroupLabel="Enabled Breakpoint Location Changed" ValueLabel="True" Visibility="Hidden">
      <Condition Expression="EnabledBreakpointCount" />
      <Setter Property="IndicatorWest" Value="WestIndicator" />
    </Style>
    <Style TargetType="Node" GroupLabel="Instruction Pointer Location Changed" ValueLabel="True" Visibility="Hidden">
      <Condition Expression="IsInstructionPointerLocation" />
      <Setter Property="IndicatorWest" Value="WestIndicator" />
    </Style>
    <Style TargetType="Node" GroupLabel="Current Callstack Changed" ValueLabel="True" Visibility="Hidden">
      <Condition Expression="IsCurrentCallstackFrame" />
      <Setter Property="IndicatorWest" Value="WestIndicator" />
    </Style>
    <Style TargetType="Link" GroupLabel="返回" ValueLabel="True" Visibility="Hidden">
      <Condition Expression="HasCategory('CodeSchema_ReturnTypeLink')" />
    </Style>
    <Style TargetType="Link" GroupLabel="引用" ValueLabel="True" Visibility="Hidden">
      <Condition Expression="HasCategory('References')" />
    </Style>
    <Style TargetType="Link" GroupLabel="使用特性" ValueLabel="True" Visibility="Hidden">
      <Condition Expression="HasCategory('CodeSchema_AttributeUse')" />
    </Style>
    <Style TargetType="Node" IsEnabled="false" GroupLabel="解决方案文件夹" ValueLabel="True" Visibility="Hidden">
      <Condition Expression="HasCategory('CodeMap_SolutionFolder')" />
      <Setter Property="Background" Value="#FFDEBA83" />
    </Style>
    <Style TargetType="Link" IsEnabled="false" GroupLabel="项目引用" ValueLabel="项目引用">
      <Condition Expression="HasCategory('CodeMap_ProjectReference')" />
      <Setter Property="Stroke" Value="#9A9A9A" />
      <Setter Property="StrokeDashArray" Value="2 2" />
      <Setter Property="DrawArrow" Value="true" />
    </Style>
    <Style TargetType="Link" IsEnabled="false" GroupLabel="外部引用" ValueLabel="外部引用">
      <Condition Expression="HasCategory('CodeMap_ExternalReference')" />
      <Setter Property="Stroke" Value="#9A9A9A" />
      <Setter Property="StrokeDashArray" Value="2 2" />
      <Setter Property="DrawArrow" Value="true" />
    </Style>
  </Styles>
  <Paths>
    <Path Id="060707b1-d0e3-4ceb-aa2e-94c986a18b71.OutputPath" Value="D:\X\NewLife.IoT\IoT.Data\bin\Release\netstandard2.1\IoT.Data.dll" />
    <Path Id="060707b1-d0e3-4ceb-aa2e-94c986a18b71.OutputPathUri" Value="file:///D:/X/NewLife.IoT/IoT.Data/bin/Release/netstandard2.1/IoT.Data.dll" />
    <Path Id="a5f35672-10fb-428a-abf3-eb9b726c48b6.OutputPath" Value="D:\X\NewLife.IoT\Bin\Server\IoTServer.dll" />
    <Path Id="a5f35672-10fb-428a-abf3-eb9b726c48b6.OutputPathUri" Value="file:///D:/X/NewLife.IoT/Bin/Server/IoTServer.dll" />
    <Path Id="ProgramFiles" Value="C:\Program Files" />
    <Path Id="ProgramFilesUri" Value="file:///C:/Program Files" />
  </Paths>
</DirectedGraph>